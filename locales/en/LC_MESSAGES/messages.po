# English translations for PROJECT.
# Copyright (C) 2024 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2024.
#
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2024-09-03 13:54+0200\n"
"PO-Revision-Date: 2024-09-03 13:54+0200\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language: en\n"
"Language-Team: en <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"

#: templates/base.html:16
msgid "My FastAPI App"
msgstr ""

#: templates/account/login.html:3
msgid "Login"
msgstr ""

#: templates/account/login.html:14 templates/account/register.html:11
#: templates/components/admin/navbar.html:14
#: templates/components/navbar.html:15
msgid "Logo"
msgstr ""

#: templates/account/login.html:16
msgid "Sign in to your account"
msgstr ""

#: templates/account/login.html:29 templates/account/login.html:32
#: templates/account/register.html:25 templates/account/register.html:28
msgid "Email address"
msgstr ""

#: templates/account/login.html:35 templates/account/login.html:38
#: templates/account/register.html:46 templates/account/register.html:49
msgid "Password"
msgstr ""

#: templates/account/login.html:45
msgid "Forgot your password?"
msgstr ""

#: templates/account/login.html:53
msgid "Sign in"
msgstr ""

#: templates/account/register.html:3 templates/account/register.html:93
#: templates/components/admin/sidebar.html:37
#: templates/components/admin/sidebar.html:52
#: templates/websites/register.html:47
msgid "Register"
msgstr ""

#: templates/account/register.html:13
msgid "Create your account"
msgstr ""

#: templates/account/partials/account_review_table.html:8
#: templates/account/register.html:36 templates/account/register.html:39
#: templates/account/review.html:18
#: templates/partials/account_update_modal.html:50
#: templates/partials/account_update_modal.html:53
msgid "Username"
msgstr ""

#: templates/account/register.html:52 templates/account/register.html:56
#: templates/partials/account_update_modal.html:73
msgid "Confirm Password"
msgstr ""

#: templates/account/register.html:68
msgid "Is Admin"
msgstr ""

#: templates/account/register.html:73
msgid "Role"
msgstr ""

#: templates/account/register.html:77
msgid "Please select a role"
msgstr ""

#: templates/account/review.html:3 templates/account/review.html:8
msgid "Accounts Review"
msgstr ""

#: templates/account/partials/account_review_table.html:11
#: templates/account/review.html:21
#: templates/partials/account_update_modal.html:58
#: templates/partials/account_update_modal.html:61
msgid "Email"
msgstr ""

#: templates/account/partials/account_review_table.html:14
#: templates/account/partials/account_review_table.html:35
#: templates/account/review.html:24 templates/account/review.html:45
#: templates/admin/clients/manage.html:31
#: templates/admin/clients/manage.html:44
#: templates/admin/scheduler/tasks_monitor.html:43
#: templates/admin/scheduler/tasks_monitor.html:263
msgid "Active"
msgstr ""

#: templates/account/partials/account_review_table.html:17
#: templates/account/review.html:27 templates/admin/clients/manage.html:33
#: templates/admin/scheduler/tasks_monitor.html:34
#: templates/admin/search_filter/manage.html:31
#: templates/websites/review.html:32
msgid "Actions"
msgstr ""

#: templates/account/partials/account_review_table.html:35
#: templates/account/review.html:45 templates/admin/clients/manage.html:44
#: templates/admin/scheduler/tasks_monitor.html:43
#: templates/admin/scheduler/tasks_monitor.html:235
msgid "Inactive"
msgstr ""

#: templates/account/partials/account_review_table.html:44
#: templates/account/review.html:54 templates/websites/review.html:52
msgid "Update"
msgstr ""

#: templates/account/partials/account_review_table.html:49
#: templates/account/review.html:59
#: templates/admin/scheduler/tasks_monitor.html:52
#: templates/admin/scheduler/tasks_monitor.html:281
#: templates/admin/search_filter/manage.html:54
#: templates/admin/search_filter/manage.html:185
#: templates/websites/review.html:56
msgid "Delete"
msgstr ""

#: templates/account/review.html:77 templates/websites/review.html:85
msgid "Reloading..."
msgstr ""

#: templates/account/review.html:105
msgid "Are you sure you want to delete this user?"
msgstr ""

#: templates/admin/home.html:5
msgid "Welcome to Our Admin Home Page"
msgstr ""

#: templates/admin/clients/manage.html:6
msgid "Website Settings Manager"
msgstr ""

#: templates/admin/clients/manage.html:21
msgid "No websites available for the selected user."
msgstr ""

#: templates/admin/clients/manage.html:22
msgid "Please select a different user or check back later."
msgstr ""

#: templates/admin/clients/manage.html:28
msgid "User"
msgstr ""

#: templates/admin/clients/manage.html:29
msgid "Website URL"
msgstr ""

#: templates/admin/clients/manage.html:30
msgid "Website Key"
msgstr ""

#: templates/admin/clients/manage.html:32
#: templates/admin/clients/manage.html:246
msgid "Paused"
msgstr ""

#: templates/admin/clients/manage.html:53
msgid "View Settings"
msgstr ""

#: templates/admin/clients/manage.html:55
msgid "Resume"
msgstr ""

#: templates/admin/clients/manage.html:55
msgid "Pause"
msgstr ""

#: templates/admin/clients/manage.html:71
msgid "Website Settings"
msgstr ""

#: templates/admin/clients/manage.html:73
msgid "Created on"
msgstr ""

#: templates/admin/clients/manage.html:74
msgid "Loading..."
msgstr ""

#: templates/admin/clients/manage.html:80
#: templates/client/frontend/integration.html:22
msgid "Results Container Selector"
msgstr ""

#: templates/admin/clients/manage.html:85
#: templates/client/frontend/integration.html:14
msgid "Search Input Selector"
msgstr ""

#: templates/admin/clients/manage.html:90
#: templates/macros/settings_modal.html:26 templates/websites/register.html:35
msgid "CSV URL"
msgstr ""

#: templates/admin/clients/manage.html:96
#: templates/admin/scheduler/tasks_monitor.html:94
#: templates/admin/search_filter/manage.html:114
#: templates/macros/settings_modal.html:80
#: templates/partials/account_update_modal.html:115
#: templates/partials/website_update_modal.html:85
msgid "Cancel"
msgstr ""

#: templates/admin/clients/manage.html:97
#: templates/admin/scheduler/tasks_monitor.html:96
#: templates/client/frontend/customization.html:46
#: templates/client/frontend/integration.html:42
#: templates/macros/settings_modal.html:84
#: templates/partials/account_update_modal.html:119
#: templates/partials/website_update_modal.html:89
msgid "Save"
msgstr ""

#: templates/admin/clients/manage.html:133
#: templates/client/frontend/integration.html:112
#: templates/macros/settings_modal.html:159
#: templates/partials/account_update_modal.html:255
#: templates/partials/account_update_modal.html:270
#: templates/partials/website_update_modal.html:159
msgid "An error occurred. Please try again."
msgstr ""

#: templates/admin/clients/manage.html:163
msgid "Failed to load website settings"
msgstr ""

#: templates/admin/clients/manage.html:203
msgid "Website settings updated successfully"
msgstr ""

#: templates/admin/clients/manage.html:206
msgid "Failed to update website settings"
msgstr ""

#: templates/admin/clients/manage.html:229
msgid "Website paused successfully"
msgstr ""

#: templates/admin/clients/manage.html:231
msgid "Website resumed successfully"
msgstr ""

#: templates/admin/clients/manage.html:248
msgid "Not Paused"
msgstr ""

#: templates/admin/logs/index.html:5
msgid "System Logs"
msgstr ""

#: templates/admin/logs/index.html:9
msgid "Log Entries"
msgstr ""

#: templates/admin/logs/index.html:12
msgid "All Logs"
msgstr ""

#: templates/admin/logs/index.html:13
msgid "Error"
msgstr ""

#: templates/admin/logs/index.html:14
msgid "Warning"
msgstr ""

#: templates/admin/logs/index.html:15
msgid "Info"
msgstr ""

#: templates/admin/logs/index.html:28
msgid "Log ID"
msgstr ""

#: templates/admin/logs/index.html:29
msgid "Website ID"
msgstr ""

#: templates/admin/logs/index.html:30
msgid "Event Type"
msgstr ""

#: templates/admin/logs/index.html:31
msgid "Event Description"
msgstr ""

#: templates/admin/logs/index.html:32
msgid "Created At"
msgstr ""

#: templates/admin/logs/index.html:107
msgid "An error has been reported by DataTables:"
msgstr ""

#: templates/admin/logs/index.html:108
msgid "An error occurred within the data table. Please try again later."
msgstr ""

#: templates/admin/scheduler/create_task.html:6
#: templates/admin/scheduler/tasks_monitor.html:6
msgid "Scheduled Tasks Monitor"
msgstr ""

#: templates/admin/scheduler/create_task.html:9
msgid "Monitor Tasks"
msgstr ""

#: templates/admin/scheduler/create_task.html:13
msgid "Create a New Task"
msgstr ""

#: templates/admin/scheduler/create_task.html:26
msgid "Task Name:"
msgstr ""

#: templates/admin/scheduler/create_task.html:29
msgid "Task name is required."
msgstr ""

#: templates/admin/scheduler/create_task.html:34
#: templates/admin/scheduler/tasks_monitor.html:369
msgid "Description:"
msgstr ""

#: templates/admin/scheduler/create_task.html:37
msgid "Description is required."
msgstr ""

#: templates/admin/scheduler/create_task.html:42
#: templates/admin/scheduler/tasks_monitor.html:373
msgid "Frequency:"
msgstr ""

#: templates/admin/scheduler/create_task.html:49
msgid "Frequency is required."
msgstr ""

#: templates/admin/scheduler/create_task.html:54
#: templates/admin/scheduler/tasks_monitor.html:377
msgid "Indexing Type:"
msgstr ""

#: templates/admin/scheduler/create_task.html:61
msgid "Indexing type is required."
msgstr ""

#: templates/admin/scheduler/create_task.html:66
#: templates/admin/scheduler/tasks_monitor.html:381
msgid "Time:"
msgstr ""

#: templates/admin/scheduler/create_task.html:69
msgid "Time is required."
msgstr ""

#: templates/admin/scheduler/create_task.html:74
msgid "Website:"
msgstr ""

#: templates/admin/scheduler/create_task.html:77
msgid "Select Website"
msgstr ""

#: templates/admin/scheduler/create_task.html:82
msgid "Website is required when Indexing Type is not 'Full'."
msgstr ""

#: templates/admin/scheduler/create_task.html:87
msgid "Hour Interval:"
msgstr ""

#: templates/admin/scheduler/create_task.html:90
#: templates/admin/scheduler/create_task.html:91
msgid "Task hours interval must be a positive integer."
msgstr ""

#: templates/admin/scheduler/create_task.html:97
#: templates/admin/scheduler/create_task.html:231
#: templates/components/admin/sidebar.html:67
msgid "Create Task"
msgstr ""

#: templates/admin/scheduler/create_task.html:179
#: templates/admin/scheduler/tasks_monitor.html:172
msgid "Please enter a new time for the task"
msgstr ""

#: templates/admin/scheduler/create_task.html:190
msgid "Creating..."
msgstr ""

#: templates/admin/scheduler/create_task.html:220
msgid "Task created successfully!"
msgstr ""

#: templates/admin/scheduler/create_task.html:227
msgid "Failed to create task. Please try again."
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:13
msgid "Add New Task"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:20
msgid "Refresh Scheduler"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:30
msgid "Task ID"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:31
msgid "Name"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:32
msgid "Frequency"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:33
msgid "Status"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:45
#: templates/admin/scheduler/tasks_monitor.html:279
msgid "Details"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:47
#: templates/admin/scheduler/tasks_monitor.html:266
msgid "Deactivate"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:49
#: templates/admin/scheduler/tasks_monitor.html:238
msgid "Activate"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:51
#: templates/admin/scheduler/tasks_monitor.html:280
msgid "Reschedule"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:64
msgid "No tasks"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:65
msgid "Get started by creating a new task."
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:71
msgid "Add new task"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:85
#: templates/admin/scheduler/tasks_monitor.html:155
msgid "Reschedule Task"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:88
msgid "New Time"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:114
msgid "Task Details"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:127
#: templates/macros/form_error.html:8 templates/macros/settings_modal.html:54
#: templates/macros/settings_modal.html:69
#: templates/partials/account_update_modal.html:89
#: templates/partials/account_update_modal.html:104
#: templates/partials/form_error_notification.html:8
#: templates/partials/form_success_notification.html:8
#: templates/partials/website_update_modal.html:58
#: templates/partials/website_update_modal.html:74
msgid "Close"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:176
msgid "Task time cannot be in the past"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:199
msgid "Task rescheduled successfully. Next run: "
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:211
#: templates/admin/scheduler/tasks_monitor.html:334
msgid "An error occurred while processing the server response"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:216
msgid "An error occurred while rescheduling the task"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:230
msgid "Failed to deactivate task"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:241
msgid "Task deactivated successfully"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:245
msgid "An error occurred while deactivating the task"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:258
msgid "Failed to activate task"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:269
msgid "Task activated successfully"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:273
msgid "An error occurred while activating the task"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:301
msgid "Task deleted successfully"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:305
msgid "An error occurred while deleting the task"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:323
msgid "Scheduler refreshed successfully"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:330
msgid "An error occurred while refreshing the scheduler: "
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:339
msgid "An error occurred while refreshing the scheduler"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:348
msgid "Failed to fetch task details"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:354
msgid "Yes"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:355
msgid "No"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:357
msgid "Task Details: "
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:361
msgid "Task ID:"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:365
msgid "Name:"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:385
msgid "Active:"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:389
msgid "Scheduled:"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:393
msgid "Hours Interval:"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:394
#: templates/admin/scheduler/tasks_monitor.html:398
#: templates/admin/scheduler/tasks_monitor.html:402
#: templates/admin/scheduler/tasks_monitor.html:406
msgid "N/A"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:397
msgid "Last Run:"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:401
msgid "Next Run:"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:405
msgid "Website ID:"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:409
msgid "Created At:"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:413
msgid "Updated At:"
msgstr ""

#: templates/admin/scheduler/tasks_monitor.html:422
msgid "An error occurred while fetching task details"
msgstr ""

#: templates/admin/search_filter/manage.html:5
msgid "Search Filters Base Template"
msgstr ""

#: templates/admin/search_filter/manage.html:8
msgid "Current Template Filters"
msgstr ""

#: templates/admin/search_filter/manage.html:16
#: templates/admin/search_filter/manage.html:80
msgid "Display Name"
msgstr ""

#: templates/admin/search_filter/manage.html:19
#: templates/admin/search_filter/manage.html:85
msgid "Filter Type ID"
msgstr ""

#: templates/admin/search_filter/manage.html:22
#: templates/admin/search_filter/manage.html:90
msgid "Field"
msgstr ""

#: templates/admin/search_filter/manage.html:25
#: templates/admin/search_filter/manage.html:95
msgid "Type"
msgstr ""

#: templates/admin/search_filter/manage.html:28
#: templates/admin/search_filter/manage.html:103
msgid "Order"
msgstr ""

#: templates/admin/search_filter/manage.html:50
#: templates/admin/search_filter/manage.html:182
#: templates/client/frontend/customization.html:16
#: templates/client/frontend/customization.html:50
msgid "Edit"
msgstr ""

#: templates/admin/search_filter/manage.html:67
msgid "Add New Filter"
msgstr ""

#: templates/admin/search_filter/manage.html:71
msgid "Save Template"
msgstr ""

#: templates/admin/search_filter/manage.html:76
msgid "Add/Edit Template Filter"
msgstr ""

#: templates/admin/search_filter/manage.html:98
msgid "Checkbox"
msgstr ""

#: templates/admin/search_filter/manage.html:99
msgid "Input Range"
msgstr ""

#: templates/admin/search_filter/manage.html:110
msgid "Save Filter"
msgstr ""

#: templates/admin/search_filter/manage.html:135
msgid "Add Template Filter"
msgstr ""

#: templates/admin/search_filter/manage.html:214
msgid "Edit Template Filter"
msgstr ""

#: templates/admin/search_filter/manage.html:234
msgid "Your changes will be saved. Are you sure?"
msgstr ""

#: templates/admin/search_filter/manage.html:244
msgid "No filters to save"
msgstr ""

#: templates/admin/search_filter/manage.html:252
msgid "Saving..."
msgstr ""

#: templates/admin/search_filter/manage.html:265
msgid "Filters saved successfully"
msgstr ""

#: templates/admin/search_filter/manage.html:268
#: templates/admin/search_filter/manage.html:272
msgid "An error occurred while saving the filters"
msgstr ""

#: templates/admin/search_filter/manage.html:330
msgid "Are you sure you want to delete this filter?"
msgstr ""

#: templates/admin/security/index.html:5
msgid "Security Settings"
msgstr ""

#: templates/admin/security/index.html:20
msgid "Login Rate Limit"
msgstr ""

#: templates/admin/security/index.html:22
msgid "Limit:"
msgstr ""

#: templates/admin/security/index.html:25
msgid "per second"
msgstr ""

#: templates/admin/security/index.html:26
msgid "per minute"
msgstr ""

#: templates/admin/security/index.html:27
msgid "per hour"
msgstr ""

#: templates/admin/security/index.html:28
msgid "per day"
msgstr ""

#: templates/admin/security/index.html:34
msgid "Failed Login Attempts"
msgstr ""

#: templates/admin/security/index.html:40
msgid "Enable Failed Login Attempts Protection"
msgstr ""

#: templates/admin/security/index.html:44
msgid "Cooldown Time:"
msgstr ""

#: templates/admin/security/index.html:47
msgid "Minutes"
msgstr ""

#: templates/admin/security/index.html:48
msgid "Hours"
msgstr ""

#: templates/admin/security/index.html:52
msgid "Max Attempts Before Limit:"
msgstr ""

#: templates/admin/security/index.html:61
msgid "Save Changes"
msgstr ""

#: templates/admin/security/index.html:82
msgid "All numeric values must be greater than zero."
msgstr ""

#: templates/admin/security/index.html:87
msgid "Login rate limit and max attempts before limit must be the same."
msgstr ""

#: templates/client/home.html:5
msgid "Welcome to your workspace"
msgstr ""

#: templates/client/home.html:6
msgid "This is a subtitle or introductory text. Customize it to fit your needs"
msgstr ""

#: templates/client/data/synchronize.html:6
msgid "Data synchronization"
msgstr ""

#: templates/client/data/synchronize.html:11
#: templates/components/admin/navbar.html:63
#: templates/components/navbar.html:113 templates/macros/settings_modal.html:11
msgid "Settings"
msgstr ""

#: templates/client/data/synchronize.html:20
msgid "Automatic Import"
msgstr ""

#: templates/client/data/synchronize.html:32
msgid "Imported"
msgstr ""

#: templates/client/data/synchronize.html:37
msgid "Failed"
msgstr ""

#: templates/client/data/synchronize.html:60
msgid "Statistics"
msgstr ""

#: templates/client/data/synchronize.html:67
msgid "Avg. Download"
msgstr ""

#: templates/client/data/synchronize.html:71
msgid "Avg. Total"
msgstr ""

#: templates/client/data/synchronize.html:85
msgid "Manual Import"
msgstr ""

#: templates/client/data/synchronize.html:94
msgid "Start Import"
msgstr ""

#: templates/client/data/synchronize.html:112
msgid "Are you sure you want to index this website?"
msgstr ""

#: templates/client/data/synchronize.html:118
msgid "Please select a website."
msgstr ""

#: templates/client/data/synchronize.html:134
msgid "An unexpected error occurred."
msgstr ""

#: templates/client/data/synchronize.html:137
msgid "An indexation is already in progress for this website."
msgstr ""

#: templates/client/frontend/customization.html:6
msgid "Search Customization"
msgstr ""

#: templates/client/frontend/customization.html:7
msgid "Customize the order of the search filters for your website"
msgstr ""

#: templates/client/frontend/customization.html:20
msgid "Field:"
msgstr ""

#: templates/client/frontend/customization.html:21
msgid "Type:"
msgstr ""

#: templates/client/frontend/customization.html:69
#: templates/client/frontend/customization.html:70
msgid "Display name of filter"
msgstr ""

#: templates/client/frontend/customization.html:69
#: templates/client/frontend/customization.html:70
msgid "updated successfully"
msgstr ""

#: templates/client/frontend/customization.html:72
msgid "Failed to update display name of filter"
msgstr ""

#: templates/client/frontend/customization.html:160
msgid "Filter order updated successfully"
msgstr ""

#: templates/client/frontend/customization.html:162
msgid "Failed to update filter order"
msgstr ""

#: templates/client/frontend/integration.html:6
msgid "Search Integration"
msgstr ""

#: templates/client/frontend/integration.html:7
msgid "Useful configurations and code snippets to integrate your search"
msgstr ""

#: templates/client/frontend/integration.html:10
msgid "Search input and results container selectors"
msgstr ""

#: templates/client/frontend/integration.html:11
msgid ""
"Fill in the selectors for the search input and results container of your "
"website"
msgstr ""

#: templates/client/frontend/integration.html:17
msgid "Enter search input selector"
msgstr ""

#: templates/client/frontend/integration.html:25
msgid "Enter results container selector"
msgstr ""

#: templates/client/frontend/integration.html:48
msgid "Copy to clipboard"
msgstr ""

#: templates/client/frontend/integration.html:51
msgid "JS Code Snippet"
msgstr ""

#: templates/client/frontend/integration.html:54
msgid "Code snippet not available yet"
msgstr ""

#: templates/client/frontend/integration.html:57
msgid "Copied to clipboard!"
msgstr ""

#: templates/client/frontend/integration.html:73
#: templates/macros/settings_modal.html:116
msgid "Please fill in all fields."
msgstr ""

#: templates/client/frontend/integration.html:85
#: templates/macros/settings_modal.html:128
msgid "Invalid website settings id."
msgstr ""

#: templates/client/frontend/integration.html:104
#: templates/macros/settings_modal.html:144
msgid "Website settings updated successfully."
msgstr ""

#: templates/client/reports/search_terms.html:6
#: templates/components/sidebar.html:53
msgid "Search Terms"
msgstr ""

#: templates/client/reports/search_terms.html:9
msgid "Date Range"
msgstr ""

#: templates/client/reports/search_terms.html:22
msgid "Search Term"
msgstr ""

#: templates/client/reports/search_terms.html:25
msgid "Occurrences"
msgstr ""

#: templates/client/reports/search_terms.html:68
msgid "Date range selected:"
msgstr ""

#: templates/client/reports/search_terms.html:68
msgid "to"
msgstr ""

#: templates/client/reports/search_terms.html:93
msgid "Error during data fetch:"
msgstr ""

#: templates/client/reports/search_terms.html:130
msgid "Search..."
msgstr ""

#: templates/client/reports/search_terms.html:131
msgid "Show _MENU_ entries"
msgstr ""

#: templates/client/reports/search_terms.html:132
msgid "Showing _START_ to _END_ of _TOTAL_ entries"
msgstr ""

#: templates/components/admin/navbar.html:7
#: templates/components/admin/sidebar.html:4 templates/components/navbar.html:8
#: templates/components/sidebar.html:6
msgid "Toolbrothers"
msgstr ""

#: templates/components/navbar.html:62
msgid "Contact Us"
msgstr ""

#: templates/components/admin/navbar.html:28
#: templates/components/navbar.html:70
msgid "Change Language"
msgstr ""

#: templates/components/admin/navbar.html:43
#: templates/components/navbar.html:91
msgid "User profile"
msgstr ""

#: templates/components/admin/navbar.html:50
#: templates/components/navbar.html:99
msgid "Dark Mode"
msgstr ""

#: templates/components/admin/navbar.html:59
#: templates/components/navbar.html:109
msgid "Profile"
msgstr ""

#: templates/components/admin/navbar.html:68
#: templates/components/navbar.html:118
msgid "Logout"
msgstr ""

#: templates/components/admin/navbar.html:79
#: templates/components/navbar.html:132
msgid "Home"
msgstr ""

#: templates/components/admin/navbar.html:81
#: templates/components/navbar.html:135
msgid "About"
msgstr ""

#: templates/components/admin/navbar.html:83
#: templates/components/navbar.html:138
msgid "Services"
msgstr ""

#: templates/components/admin/navbar.html:85
#: templates/components/navbar.html:141
msgid "Contact"
msgstr ""

#: templates/components/admin/sidebar.html:11
#: templates/components/sidebar.html:13
msgid "Dashboard"
msgstr ""

#: templates/components/sidebar.html:18
msgid "Data"
msgstr ""

#: templates/components/sidebar.html:25
msgid "Synchronization"
msgstr ""

#: templates/components/sidebar.html:26
msgid "Search Stats"
msgstr ""

#: templates/components/sidebar.html:32
msgid "Frontend"
msgstr ""

#: templates/components/sidebar.html:39
msgid "Integration"
msgstr ""

#: templates/components/sidebar.html:40
msgid "Customization"
msgstr ""

#: templates/components/sidebar.html:46
msgid "Reports"
msgstr ""

#: templates/components/admin/sidebar.html:15
msgid "Manage filters"
msgstr ""

#: templates/components/admin/sidebar.html:19
msgid "Monitor Websites"
msgstr ""

#: templates/components/admin/sidebar.html:23
msgid "Security"
msgstr ""

#: templates/components/admin/sidebar.html:30
msgid "Accounts"
msgstr ""

#: templates/components/admin/sidebar.html:38
#: templates/components/admin/sidebar.html:53
msgid "Review"
msgstr ""

#: templates/components/admin/sidebar.html:46
msgid "Websites"
msgstr ""

#: templates/components/admin/sidebar.html:61
msgid "Scheduled Tasks"
msgstr ""

#: templates/components/admin/sidebar.html:68
msgid "Monitor"
msgstr ""

#: templates/components/admin/sidebar.html:74
msgid "View Logs"
msgstr ""

#: templates/errors/error403.html:15
msgid "Access Denied"
msgstr ""

#: templates/errors/error403.html:19
msgid ""
"You do not have the necessary permissions to view this page. Please check"
" your credentials and try again."
msgstr ""

#: templates/errors/error403.html:20
msgid ""
"If you believe this is an error, contact support. Attempting to access "
"restricted content without authorization is not allowed."
msgstr ""

#: templates/errors/error403.html:24 templates/errors/error500.html:23
msgid "Go to Homepage"
msgstr ""

#: templates/errors/error500.html:15
msgid "Oops! Something went wrong."
msgstr ""

#: templates/errors/error500.html:19
msgid ""
"We're having trouble processing your request. Our team has been notified "
"and is working to fix the issue. Please try again later."
msgstr ""

#: templates/errors/rate_limit_exceeded.html:15
msgid "Rate Limit Exceeded"
msgstr ""

#: templates/errors/rate_limit_exceeded.html:19
msgid ""
"You've reached the maximum number of requests allowed in a given time "
"frame. Please wait a moment before trying again."
msgstr ""

#: templates/errors/rate_limit_exceeded.html:23
msgid "Go to Login page"
msgstr ""

#: templates/macros/form_error.html:3 templates/macros/settings_modal.html:49
#: templates/partials/account_update_modal.html:84
#: templates/partials/form_error_notification.html:3
#: templates/partials/website_update_modal.html:52
msgid "Error!"
msgstr ""

#: templates/macros/settings_modal.html:30
msgid "Enter CSV URL here"
msgstr ""

#: templates/macros/settings_modal.html:35
msgid "Website Domain"
msgstr ""

#: templates/macros/settings_modal.html:39
msgid "Enter website domain here"
msgstr ""

#: templates/macros/settings_modal.html:64
#: templates/partials/account_update_modal.html:99
#: templates/partials/form_success_notification.html:3
#: templates/partials/website_update_modal.html:68
msgid "Success!"
msgstr ""

#: templates/partials/account_update_modal.html:10
msgid "Update User Account"
msgstr ""

#: templates/partials/account_update_modal.html:33
msgid "Account Status"
msgstr ""

#: templates/partials/account_update_modal.html:44
msgid "Admin Account"
msgstr ""

#: templates/partials/account_update_modal.html:65
msgid "New Password"
msgstr ""

#: templates/partials/account_update_modal.html:68
msgid "Leave blank to keep current password"
msgstr ""

#: templates/partials/account_update_modal.html:76
msgid "Confirm your new password"
msgstr ""

#: templates/partials/account_update_modal.html:192
msgid "Passwords do not match. Please try again."
msgstr ""

#: templates/partials/account_update_modal.html:233
#: templates/partials/website_update_modal.html:144
msgid "Account updated successfully."
msgstr ""

#: templates/partials/website_update_modal.html:10
msgid "Update Website"
msgstr ""

#: templates/partials/website_update_modal.html:27
#: templates/websites/review.html:23
msgid "Website key"
msgstr ""

#: templates/partials/website_update_modal.html:35
#: templates/partials/website_update_modal.html:38
msgid "Website Url"
msgstr ""

#: templates/partials/website_update_modal.html:42
#: templates/partials/website_update_modal.html:45
msgid "CSV Url"
msgstr ""

#: templates/websites/register.html:5 templates/websites/review.html:72
msgid "Register a website"
msgstr ""

#: templates/websites/register.html:28
msgid "Website domain"
msgstr ""

#: templates/websites/register.html:31
msgid "Enter the domain of the website"
msgstr ""

#: templates/websites/register.html:38
msgid "Enter the URL of the CSV file"
msgstr ""

#: templates/websites/review.html:3
msgid "Websites Review"
msgstr ""

#: templates/websites/review.html:11
msgid "Registered Websites"
msgstr ""

#: templates/websites/review.html:14
msgid "The following websites have been registered to be tracked."
msgstr ""

#: templates/websites/review.html:26
msgid "Website url"
msgstr ""

#: templates/websites/review.html:29
msgid "CSV url"
msgstr ""

#: templates/websites/review.html:68
msgid "You haven't registered any websites yet."
msgstr ""

#: templates/websites/review.html:112
msgid "could not delete website, missing id."
msgstr ""

#: templates/websites/review.html:118
msgid "Are you sure you want to delete this website?"
msgstr ""

