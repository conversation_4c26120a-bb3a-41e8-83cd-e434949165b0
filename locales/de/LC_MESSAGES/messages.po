# Deutsche Übersetzungen für PROJEKT.
# Copyright (C) 2024 ORGANISATION
# Diese Datei wird unter derselben Lizenz wie das PROJEKT-Projekt verteilt.
# <AUTHOR> <EMAIL>, 2024.
#
msgid ""
msgstr ""
"Project-Id-Version: PROJEKT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADRESSE\n"
"POT-Creation-Date: 2024-09-03 13:54+0200\n"
"PO-Revision-Date: 2024-09-03 13:54+0200\n"
"Last-Translator: VOLLE NAME <EMAIL@ADRESSE>\n"
"Language: de\n"
"Language-Team: de <<EMAIL>>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"

#: templates/base.html:16
msgid "My FastAPI App"
msgstr "Meine FastAPI-App"

#: templates/account/login.html:3
msgid "Login"
msgstr "Anmelden"

#: templates/account/login.html:14 templates/account/register.html:11
#: templates/components/admin/navbar.html:14
#: templates/components/navbar.html:15
msgid "Logo"
msgstr "Logo"

#: templates/account/login.html:16
msgid "Sign in to your account"
msgstr "Melden Sie sich bei Ihrem Konto an"

#: templates/account/login.html:29 templates/account/login.html:32
#: templates/account/register.html:25 templates/account/register.html:28
msgid "Email address"
msgstr "E-Mail-Adresse"

#: templates/account/login.html:35 templates/account/login.html:38
#: templates/account/register.html:46 templates/account/register.html:49
msgid "Password"
msgstr "Passwort"

#: templates/account/login.html:45
msgid "Forgot your password?"
msgstr "Haben Sie Ihr Passwort vergessen?"

#: templates/account/login.html:53
msgid "Sign in"
msgstr "Anmelden"

#: templates/account/register.html:3 templates/account/register.html:93
#: templates/components/admin/sidebar.html:37
#: templates/components/admin/sidebar.html:52
#: templates/websites/register.html:47
msgid "Register"
msgstr "Registrieren"

#: templates/account/register.html:13
msgid "Create your account"
msgstr "Erstellen Sie Ihr Konto"

#: templates/account/partials/account_review_table.html:8
#: templates/account/register.html:36 templates/account/register.html:39
#: templates/account/review.html:18
#: templates/partials/account_update_modal.html:50
#: templates/partials/account_update_modal.html:53
msgid "Username"
msgstr "Benutzername"

#: templates/account/register.html:52 templates/account/register.html:56
#: templates/partials/account_update_modal.html:73
msgid "Confirm Password"
msgstr "Passwort bestätigen"

#: templates/account/register.html:68
msgid "Is Admin"
msgstr "Ist Admin"

#: templates/account/register.html:73
msgid "Role"
msgstr "Rolle"

#: templates/account/register.html:77
msgid "Please select a role"
msgstr "Bitte wählen Sie eine Rolle"

#: templates/account/review.html:3 templates/account/review.html:8
msgid "Accounts Review"
msgstr "Kontenübersicht"

#: templates/account/partials/account_review_table.html:11
#: templates/account/review.html:21
#: templates/partials/account_update_modal.html:58
#: templates/partials/account_update_modal.html:61
msgid "Email"
msgstr "E-Mail"

#: templates/account/partials/account_review_table.html:14
#: templates/account/partials/account_review_table.html:35
#: templates/account/review.html:24 templates/account/review.html:45
#: templates/admin/clients/manage.html:31
#: templates/admin/clients/manage.html:44
#: templates/admin/scheduler/tasks_monitor.html:43
#: templates/admin/scheduler/tasks_monitor.html:263
msgid "Active"
msgstr "Aktiv"

#: templates/account/partials/account_review_table.html:17
#: templates/account/review.html:27 templates/admin/clients/manage.html:33
#: templates/admin/scheduler/tasks_monitor.html:34
#: templates/admin/search_filter/manage.html:31
#: templates/websites/review.html:32
msgid "Actions"
msgstr "Aktionen"

#: templates/account/partials/account_review_table.html:35
#: templates/account/review.html:45 templates/admin/clients/manage.html:44
#: templates/admin/scheduler/tasks_monitor.html:43
#: templates/admin/scheduler/tasks_monitor.html:235
msgid "Inactive"
msgstr "Inaktiv"

#: templates/account/partials/account_review_table.html:44
#: templates/account/review.html:54 templates/websites/review.html:52
msgid "Update"
msgstr "Aktualisieren"

#: templates/account/partials/account_review_table.html:49
#: templates/account/review.html:59
#: templates/admin/scheduler/tasks_monitor.html:52
#: templates/admin/scheduler/tasks_monitor.html:281
#: templates/admin/search_filter/manage.html:54
#: templates/admin/search_filter/manage.html:185
#: templates/websites/review.html:56
msgid "Delete"
msgstr "Löschen"

#: templates/account/review.html:77 templates/websites/review.html:85
msgid "Reloading..."
msgstr "Wird neu geladen..."

#: templates/account/review.html:105
msgid "Are you sure you want to delete this user?"
msgstr "Sind Sie sicher, dass Sie diesen Benutzer löschen möchten?"

#: templates/admin/home.html:5
msgid "Welcome to Our Admin Home Page"
msgstr "Willkommen auf unserer Admin-Startseite"

#: templates/admin/clients/manage.html:6
msgid "Website Settings Manager"
msgstr "Website-Einstellungsmanager"

#: templates/admin/clients/manage.html:21
msgid "No websites available for the selected user."
msgstr "Keine Websites für den ausgewählten Benutzer verfügbar."

#: templates/admin/clients/manage.html:22
msgid "Please select a different user or check back later."
msgstr "Bitte wählen Sie einen anderen Benutzer oder kommen Sie später zurück."

#: templates/admin/clients/manage.html:28
msgid "User"
msgstr "Benutzer"

#: templates/admin/clients/manage.html:29
msgid "Website URL"
msgstr "Website-URL"

#: templates/admin/clients/manage.html:30
msgid "Website Key"
msgstr "Website-Schlüssel"

#: templates/admin/clients/manage.html:32
#: templates/admin/clients/manage.html:246
msgid "Paused"
msgstr "Pausiert"

#: templates/admin/clients/manage.html:53
msgid "View Settings"
msgstr "Einstellungen anzeigen"

#: templates/admin/clients/manage.html:55
msgid "Resume"
msgstr "Fortsetzen"

#: templates/admin/clients/manage.html:55
msgid "Pause"
msgstr "Pause"

#: templates/admin/clients/manage.html:71
msgid "Website Settings"
msgstr "Website-Einstellungen"

#: templates/admin/clients/manage.html:73
msgid "Created on"
msgstr "Erstellt am"

#: templates/admin/clients/manage.html:74
msgid "Loading..."
msgstr "Wird geladen..."

#: templates/admin/clients/manage.html:80
#: templates/client/frontend/integration.html:22
msgid "Results Container Selector"
msgstr "Ergebniscontainer-Auswahl"

#: templates/admin/clients/manage.html:85
#: templates/client/frontend/integration.html:14
msgid "Search Input Selector"
msgstr "Suchfeld-Auswahl"

#: templates/admin/clients/manage.html:90
#: templates/macros/settings_modal.html:26 templates/websites/register.html:35
msgid "CSV URL"
msgstr "CSV-URL"

#: templates/admin/clients/manage.html:96
#: templates/admin/scheduler/tasks_monitor.html:94
#: templates/admin/search_filter/manage.html:114
#: templates/macros/settings_modal.html:80
#: templates/partials/account_update_modal.html:115
#: templates/partials/website_update_modal.html:85
msgid "Cancel"
msgstr "Abbrechen"

#: templates/admin/clients/manage.html:97
#: templates/admin/scheduler/tasks_monitor.html:96
#: templates/client/frontend/customization.html:46
#: templates/client/frontend/integration.html:42
#: templates/macros/settings_modal.html:84
#: templates/partials/account_update_modal.html:119
#: templates/partials/website_update_modal.html:89
msgid "Save"
msgstr "Speichern"

#: templates/admin/clients/manage.html:133
#: templates/client/frontend/integration.html:112
#: templates/macros/settings_modal.html:159
#: templates/partials/account_update_modal.html:255
#: templates/partials/account_update_modal.html:270
#: templates/partials/website_update_modal.html:159
msgid "An error occurred. Please try again."
msgstr "Ein Fehler ist aufgetreten. Bitte versuchen Sie es erneut."

#: templates/admin/clients/manage.html:163
msgid "Failed to load website settings"
msgstr "Website-Einstellungen konnten nicht geladen werden"

#: templates/admin/clients/manage.html:203
msgid "Website settings updated successfully"
msgstr "Website-Einstellungen erfolgreich aktualisiert"

#: templates/admin/clients/manage.html:206
msgid "Failed to update website settings"
msgstr "Website-Einstellungen konnten nicht aktualisiert werden"

#: templates/admin/clients/manage.html:229
msgid "Website paused successfully"
msgstr "Website erfolgreich pausiert"

#: templates/admin/clients/manage.html:231
msgid "Website resumed successfully"
msgstr "Website erfolgreich fortgesetzt"

#: templates/admin/clients/manage.html:248
msgid "Not Paused"
msgstr "Nicht pausiert"

#: templates/admin/logs/index.html:5
msgid "System Logs"
msgstr "Systemprotokolle"

#: templates/admin/logs/index.html:9
msgid "Log Entries"
msgstr "Protokolleinträge"

#: templates/admin/logs/index.html:12
msgid "All Logs"
msgstr "Alle Protokolle"

#: templates/admin/logs/index.html:13
msgid "Error"
msgstr "Fehler"

#: templates/admin/logs/index.html:14
msgid "Warning"
msgstr "Warnung"

#: templates/admin/logs/index.html:15
msgid "Info"
msgstr "Info"

#: templates/admin/logs/index.html:28
msgid "Log ID"
msgstr "Protokoll-ID"

#: templates/admin/logs/index.html:29
msgid "Website ID"
msgstr "Website-ID"

#: templates/admin/logs/index.html:30
msgid "Event Type"
msgstr "Ereignistyp"

#: templates/admin/logs/index.html:31
msgid "Event Description"
msgstr "Ereignisbeschreibung"

#: templates/admin/logs/index.html:32
msgid "Created At"
msgstr "Erstellt am"

#: templates/admin/logs/index.html:107
msgid "An error has been reported by DataTables:"
msgstr "Ein Fehler wurde von DataTables gemeldet:"

#: templates/admin/logs/index.html:108
msgid "An error occurred within the data table. Please try again later."
msgstr "Ein Fehler ist im Datentabelle aufgetreten. Bitte versuchen Sie es später erneut."

#: templates/admin/scheduler/create_task.html:6
#: templates/admin/scheduler/tasks_monitor.html:6
msgid "Scheduled Tasks Monitor"
msgstr "Überwachung geplanter Aufgaben"

#: templates/admin/scheduler/create_task.html:9
msgid "Monitor Tasks"
msgstr "Aufgaben überwachen"

#: templates/admin/scheduler/create_task.html:13
msgid "Create a New Task"
msgstr "Eine neue Aufgabe erstellen"

#: templates/admin/scheduler/create_task.html:26
msgid "Task Name:"
msgstr "Aufgabenname:"

#: templates/admin/scheduler/create_task.html:29
msgid "Task name is required."
msgstr "Der Aufgabenname ist erforderlich."

#: templates/admin/scheduler/create_task.html:34
#: templates/admin/scheduler/tasks_monitor.html:369
msgid "Description:"
msgstr "Beschreibung:"

#: templates/admin/scheduler/create_task.html:37
msgid "Description is required."
msgstr "Die Beschreibung ist erforderlich."

#: templates/admin/scheduler/create_task.html:42
#: templates/admin/scheduler/tasks_monitor.html:373
msgid "Frequency:"
msgstr "Häufigkeit:"

#: templates/admin/scheduler/create_task.html:49
msgid "Frequency is required."
msgstr "Die Häufigkeit ist erforderlich."

#: templates/admin/scheduler/create_task.html:54
#: templates/admin/scheduler/tasks_monitor.html:377
msgid "Indexing Type:"
msgstr "Indexierungstyp:"

#: templates/admin/scheduler/create_task.html:61
msgid "Indexing type is required."
msgstr "Der Indexierungstyp ist erforderlich."

#: templates/admin/scheduler/create_task.html:66
#: templates/admin/scheduler/tasks_monitor.html:381
msgid "Time:"
msgstr "Zeit:"

#: templates/admin/scheduler/create_task.html:69
msgid "Time is required."
msgstr "Die Zeit ist erforderlich."

#: templates/admin/scheduler/create_task.html:74
msgid "Website:"
msgstr "Website:"

#: templates/admin/scheduler/create_task.html:77
msgid "Select Website"
msgstr "Website auswählen"

#: templates/admin/scheduler/create_task.html:82
msgid "Website is required when Indexing Type is not 'Full'."
msgstr "Die Website ist erforderlich, wenn der Indexierungstyp nicht 'Voll' ist."

#: templates/admin/scheduler/create_task.html:87
msgid "Hour Interval:"
msgstr "Stundenintervall:"

#: templates/admin/scheduler/create_task.html:90
#: templates/admin/scheduler/create_task.html:91
msgid "Task hours interval must be a positive integer."
msgstr "Das Stundenintervall der Aufgabe muss eine positive ganze Zahl sein."

#: templates/admin/scheduler/create_task.html:97
#: templates/admin/scheduler/create_task.html:231
#: templates/components/admin/sidebar.html:67
msgid "Create Task"
msgstr "Aufgabe erstellen"

#: templates/admin/scheduler/create_task.html:179
#: templates/admin/scheduler/tasks_monitor.html:172
msgid "Please enter a new time for the task"
msgstr "Bitte geben Sie eine neue Zeit für die Aufgabe ein"

#: templates/admin/scheduler/create_task.html:190
msgid "Creating..."
msgstr "Wird erstellt..."

#: templates/admin/scheduler/create_task.html:220
msgid "Task created successfully!"
msgstr "Aufgabe erfolgreich erstellt!"

#: templates/admin/scheduler/create_task.html:227
msgid "Failed to create task. Please try again."
msgstr "Fehler beim Erstellen der Aufgabe. Bitte versuchen Sie es erneut."

#: templates/admin/scheduler/tasks_monitor.html:13
msgid "Add New Task"
msgstr "Neue Aufgabe hinzufügen"

#: templates/admin/scheduler/tasks_monitor.html:20
msgid "Refresh Scheduler"
msgstr "Scheduler aktualisieren"

#: templates/admin/scheduler/tasks_monitor.html:30
msgid "Task ID"
msgstr "Aufgaben-ID"

#: templates/admin/scheduler/tasks_monitor.html:31
msgid "Name"
msgstr "Name"

#: templates/admin/scheduler/tasks_monitor.html:32
msgid "Frequency"
msgstr "Häufigkeit"

#: templates/admin/scheduler/tasks_monitor.html:33
msgid "Status"
msgstr "Status"

#: templates/admin/scheduler/tasks_monitor.html:45
#: templates/admin/scheduler/tasks_monitor.html:279
msgid "Details"
msgstr "Details"

#: templates/admin/scheduler/tasks_monitor.html:47
#: templates/admin/scheduler/tasks_monitor.html:266
msgid "Deactivate"
msgstr "Deaktivieren"

#: templates/admin/scheduler/tasks_monitor.html:49
#: templates/admin/scheduler/tasks_monitor.html:238
msgid "Activate"
msgstr "Aktivieren"

#: templates/admin/scheduler/tasks_monitor.html:51
#: templates/admin/scheduler/tasks_monitor.html:280
msgid "Reschedule"
msgstr "Neu planen"

#: templates/admin/scheduler/tasks_monitor.html:64
msgid "No tasks"
msgstr "Keine Aufgaben"

#: templates/admin/scheduler/tasks_monitor.html:65
msgid "Get started by creating a new task."
msgstr "Beginnen Sie, indem Sie eine neue Aufgabe erstellen."

#: templates/admin/scheduler/tasks_monitor.html:71
msgid "Add new task"
msgstr "Neue Aufgabe hinzufügen"

#: templates/admin/scheduler/tasks_monitor.html:85
#: templates/admin/scheduler/tasks_monitor.html:155
msgid "Reschedule Task"
msgstr "Aufgabe neu planen"

#: templates/admin/scheduler/tasks_monitor.html:88
msgid "New Time"
msgstr "Neue Zeit"

#: templates/admin/scheduler/tasks_monitor.html:114
msgid "Task Details"
msgstr "Aufgabendetails"

#: templates/admin/scheduler/tasks_monitor.html:127
#: templates/macros/form_error.html:8 templates/macros/settings_modal.html:54
#: templates/macros/settings_modal.html:69
#: templates/partials/account_update_modal.html:89
#: templates/partials/account_update_modal.html:104
#: templates/partials/form_error_notification.html:8
#: templates/partials/form_success_notification.html:8
#: templates/partials/website_update_modal.html:58
#: templates/partials/website_update_modal.html:74
msgid "Close"
msgstr "Schließen"

#: templates/admin/scheduler/tasks_monitor.html:176
msgid "Task time cannot be in the past"
msgstr "Die Aufgabenzeit kann nicht in der Vergangenheit liegen"

#: templates/admin/scheduler/tasks_monitor.html:199
msgid "Task rescheduled successfully. Next run: "
msgstr "Aufgabe erfolgreich neu geplant. Nächster Lauf: "

#: templates/admin/scheduler/tasks_monitor.html:211
#: templates/admin/scheduler/tasks_monitor.html:334
msgid "An error occurred while processing the server response"
msgstr "Ein Fehler ist beim Verarbeiten der Serverantwort aufgetreten"

#: templates/admin/scheduler/tasks_monitor.html:216
msgid "An error occurred while rescheduling the task"
msgstr "Ein Fehler ist beim Neuplanen der Aufgabe aufgetreten"

#: templates/admin/scheduler/tasks_monitor.html:230
msgid "Failed to deactivate task"
msgstr "Deaktivieren der Aufgabe fehlgeschlagen"

#: templates/admin/scheduler/tasks_monitor.html:241
msgid "Task deactivated successfully"
msgstr "Aufgabe erfolgreich deaktiviert"

#: templates/admin/scheduler/tasks_monitor.html:245
msgid "An error occurred while deactivating the task"
msgstr "Ein Fehler ist beim Deaktivieren der Aufgabe aufgetreten"

#: templates/admin/scheduler/tasks_monitor.html:258
msgid "Failed to activate task"
msgstr "Aktivieren der Aufgabe fehlgeschlagen"

#: templates/admin/scheduler/tasks_monitor.html:269
msgid "Task activated successfully"
msgstr "Aufgabe erfolgreich aktiviert"

#: templates/admin/scheduler/tasks_monitor.html:273
msgid "An error occurred while activating the task"
msgstr "Ein Fehler ist beim Aktivieren der Aufgabe aufgetreten"

#: templates/admin/scheduler/tasks_monitor.html:301
msgid "Task deleted successfully"
msgstr "Aufgabe erfolgreich gelöscht"

#: templates/admin/scheduler/tasks_monitor.html:305
msgid "An error occurred while deleting the task"
msgstr "Ein Fehler ist beim Löschen der Aufgabe aufgetreten"

#: templates/admin/scheduler/tasks_monitor.html:323
msgid "Scheduler refreshed successfully"
msgstr "Scheduler erfolgreich aktualisiert"

#: templates/admin/scheduler/tasks_monitor.html:330
msgid "An error occurred while refreshing the scheduler: "
msgstr "Ein Fehler ist beim Aktualisieren des Schedulers aufgetreten: "

#: templates/admin/scheduler/tasks_monitor.html:339
msgid "An error occurred while refreshing the scheduler"
msgstr "Ein Fehler ist beim Aktualisieren des Schedulers aufgetreten"

#: templates/admin/scheduler/tasks_monitor.html:348
msgid "Failed to fetch task details"
msgstr "Abrufen der Aufgabendetails fehlgeschlagen"

#: templates/admin/scheduler/tasks_monitor.html:354
msgid "Yes"
msgstr "Ja"

#: templates/admin/scheduler/tasks_monitor.html:355
msgid "No"
msgstr "Nein"

#: templates/admin/scheduler/tasks_monitor.html:357
msgid "Task Details: "
msgstr "Aufgabendetails: "

#: templates/admin/scheduler/tasks_monitor.html:361
msgid "Task ID:"
msgstr "Aufgaben-ID:"

#: templates/admin/scheduler/tasks_monitor.html:365
msgid "Name:"
msgstr "Name:"

#: templates/admin/scheduler/tasks_monitor.html:385
msgid "Active:"
msgstr "Aktiv:"

#: templates/admin/scheduler/tasks_monitor.html:389
msgid "Scheduled:"
msgstr "Geplant:"

#: templates/admin/scheduler/tasks_monitor.html:393
msgid "Hours Interval:"
msgstr "Stundenintervall:"

#: templates/admin/scheduler/tasks_monitor.html:394
#: templates/admin/scheduler/tasks_monitor.html:398
#: templates/admin/scheduler/tasks_monitor.html:402
#: templates/admin/scheduler/tasks_monitor.html:406
msgid "N/A"
msgstr "N/A"

#: templates/admin/scheduler/tasks_monitor.html:397
msgid "Last Run:"
msgstr "Letzter Lauf:"

#: templates/admin/scheduler/tasks_monitor.html:401
msgid "Next Run:"
msgstr "Nächster Lauf:"

#: templates/admin/scheduler/tasks_monitor.html:405
msgid "Website ID:"
msgstr "Website-ID:"

#: templates/admin/scheduler/tasks_monitor.html:409
msgid "Created At:"
msgstr "Erstellt am:"

#: templates/admin/scheduler/tasks_monitor.html:413
msgid "Updated At:"
msgstr "Aktualisiert am:"

#: templates/admin/scheduler/tasks_monitor.html:422
msgid "An error occurred while fetching task details"
msgstr "Ein Fehler ist beim Abrufen der Aufgabendetails aufgetreten."

#: templates/admin/search_filter/manage.html:5
msgid "Search Filters Base Template"
msgstr "Basisvorlage für Suchfilter"

#: templates/admin/search_filter/manage.html:8
msgid "Current Template Filters"
msgstr "Aktuelle Vorlagenfilter"

#: templates/admin/search_filter/manage.html:16
#: templates/admin/search_filter/manage.html:80
msgid "Display Name"
msgstr "Anzeigename"

#: templates/admin/search_filter/manage.html:19
#: templates/admin/search_filter/manage.html:85
msgid "Filter Type ID"
msgstr "Filtertyp-ID"

#: templates/admin/search_filter/manage.html:22
#: templates/admin/search_filter/manage.html:90
msgid "Field"
msgstr "Feld"

#: templates/admin/search_filter/manage.html:25
#: templates/admin/search_filter/manage.html:95
msgid "Type"
msgstr "Typ"

#: templates/admin/search_filter/manage.html:28
#: templates/admin/search_filter/manage.html:103
msgid "Order"
msgstr "Reihenfolge"

#: templates/admin/search_filter/manage.html:50
#: templates/admin/search_filter/manage.html:182
#: templates/client/frontend/customization.html:16
#: templates/client/frontend/customization.html:50
msgid "Edit"
msgstr "Bearbeiten"

#: templates/admin/search_filter/manage.html:67
msgid "Add New Filter"
msgstr "Neuen Filter hinzufügen"

#: templates/admin/search_filter/manage.html:71
msgid "Save Template"
msgstr "Vorlage speichern"

#: templates/admin/search_filter/manage.html:76
msgid "Add/Edit Template Filter"
msgstr "Vorlagenfilter hinzufügen/bearbeiten"

#: templates/admin/search_filter/manage.html:98
msgid "Checkbox"
msgstr "Kontrollkästchen"

#: templates/admin/search_filter/manage.html:99
msgid "Input Range"
msgstr "Eingabebereich"

#: templates/admin/search_filter/manage.html:110
msgid "Save Filter"
msgstr "Filter speichern"

#: templates/admin/search_filter/manage.html:135
msgid "Add Template Filter"
msgstr "Vorlagenfilter hinzufügen"

#: templates/admin/search_filter/manage.html:214
msgid "Edit Template Filter"
msgstr "Vorlagenfilter bearbeiten"

#: templates/admin/search_filter/manage.html:234
msgid "Your changes will be saved. Are you sure?"
msgstr "Ihre Änderungen werden gespeichert. Sind Sie sicher?"

#: templates/admin/search_filter/manage.html:244
msgid "No filters to save"
msgstr "Keine Filter zum Speichern"

#: templates/admin/search_filter/manage.html:252
msgid "Saving..."
msgstr "Speichern..."

#: templates/admin/search_filter/manage.html:265
msgid "Filters saved successfully"
msgstr "Filter erfolgreich gespeichert"

#: templates/admin/search_filter/manage.html:268
#: templates/admin/search_filter/manage.html:272
msgid "An error occurred while saving the filters"
msgstr "Ein Fehler ist beim Speichern der Filter aufgetreten."

#: templates/admin/search_filter/manage.html:330
msgid "Are you sure you want to delete this filter?"
msgstr "Sind Sie sicher, dass Sie diesen Filter löschen möchten?"

#: templates/admin/security/index.html:5
msgid "Security Settings"
msgstr "Sicherheitseinstellungen"

#: templates/admin/security/index.html:20
msgid "Login Rate Limit"
msgstr "Login-Rate-Limit"

#: templates/admin/security/index.html:22
msgid "Limit:"
msgstr "Limit:"

#: templates/admin/security/index.html:25
msgid "per second"
msgstr "pro Sekunde"

#: templates/admin/security/index.html:26
msgid "per minute"
msgstr "pro Minute"

#: templates/admin/security/index.html:27
msgid "per hour"
msgstr "pro Stunde"

#: templates/admin/security/index.html:28
msgid "per day"
msgstr "pro Tag"

#: templates/admin/security/index.html:34
msgid "Failed Login Attempts"
msgstr "Fehlgeschlagene Anmeldeversuche"

#: templates/admin/security/index.html:40
msgid "Enable Failed Login Attempts Protection"
msgstr "Schutz vor fehlgeschlagenen Anmeldeversuchen aktivieren"

#: templates/admin/security/index.html:44
msgid "Cooldown Time:"
msgstr "Abkühlzeit:"

#: templates/admin/security/index.html:47
msgid "Minutes"
msgstr "Minuten"

#: templates/admin/security/index.html:48
msgid "Hours"
msgstr "Stunden"

#: templates/admin/security/index.html:52
msgid "Max Attempts Before Limit:"
msgstr "Maximale Versuche vor dem Limit:"

#: templates/admin/security/index.html:61
msgid "Save Changes"
msgstr "Änderungen speichern"

#: templates/admin/security/index.html:82
msgid "All numeric values must be greater than zero."
msgstr "Alle numerischen Werte müssen größer als null sein."

#: templates/admin/security/index.html:87
msgid "Login rate limit and max attempts before limit must be the same."
msgstr "Login-Rate-Limit und maximale Versuche vor dem Limit müssen gleich sein."

#: templates/client/home.html:5
msgid "Welcome to your workspace"
msgstr "Willkommen in Ihrem Arbeitsbereich"

#: templates/client/home.html:6
msgid "This is a subtitle or introductory text. Customize it to fit your needs"
msgstr ""

#: templates/client/data/synchronize.html:6
msgid "Data synchronization"
msgstr ""

#: templates/client/data/synchronize.html:11
#: templates/components/admin/navbar.html:63
#: templates/components/navbar.html:113 templates/macros/settings_modal.html:11
msgid "Settings"
msgstr "Einstellungen"

#: templates/client/data/synchronize.html:20
msgid "Automatic Import"
msgstr "Automatischer Import"

#: templates/client/data/synchronize.html:32
msgid "Imported"
msgstr "Importiert"

#: templates/client/data/synchronize.html:37
msgid "Failed"
msgstr "Fehlgeschlagen"

#: templates/client/data/synchronize.html:60
msgid "Statistics"
msgstr "Statistiken"

#: templates/client/data/synchronize.html:67
msgid "Avg. Download"
msgstr "Durchschnittlicher Download"

#: templates/client/data/synchronize.html:71
msgid "Avg. Total"
msgstr "Durchschnittlich Gesamt"

#: templates/client/data/synchronize.html:85
msgid "Manual Import"
msgstr "Manueller Import"

#: templates/client/data/synchronize.html:94
msgid "Start Import"
msgstr "Import starten"

#: templates/client/data/synchronize.html:112
msgid "Are you sure you want to index this website?"
msgstr "Sind Sie sicher, dass Sie diese Website indizieren möchten?"

#: templates/client/data/synchronize.html:118
msgid "Please select a website."
msgstr "Bitte wählen Sie eine Website aus."

#: templates/client/data/synchronize.html:134
msgid "An unexpected error occurred."
msgstr "Ein unerwarteter Fehler ist aufgetreten."

#: templates/client/data/synchronize.html:137
msgid "An indexation is already in progress for this website."
msgstr "Eine Indizierung ist bereits für diese Website im Gange."

#: templates/client/frontend/customization.html:6
msgid "Search Customization"
msgstr "Suchanpassung"

#: templates/client/frontend/customization.html:7
msgid "Customize the order of the search filters for your website"
msgstr "Passen Sie die Reihenfolge der Suchfilter für Ihre Website an"

#: templates/client/frontend/customization.html:20
msgid "Field:"
msgstr "Feld:"

#: templates/client/frontend/customization.html:21
msgid "Type:"
msgstr "Typ:"

#: templates/client/frontend/customization.html:69
#: templates/client/frontend/customization.html:70
msgid "Display name of filter"
msgstr "Anzeigename des Filters"

#: templates/client/frontend/customization.html:69
#: templates/client/frontend/customization.html:70
msgid "updated successfully"
msgstr "erfolgreich aktualisiert"

#: templates/client/frontend/customization.html:72
msgid "Failed to update display name of filter"
msgstr "Aktualisierung des Anzeigenamens des Filters fehlgeschlagen"

#: templates/client/frontend/customization.html:160
msgid "Filter order updated successfully"
msgstr "Reihenfolge der Filter erfolgreich aktualisiert"

#: templates/client/frontend/customization.html:162
msgid "Failed to update filter order"
msgstr "Aktualisierung der Filterreihenfolge fehlgeschlagen"

#: templates/client/frontend/integration.html:6
msgid "Search Integration"
msgstr "Suchintegration"

#: templates/client/frontend/integration.html:7
msgid "Useful configurations and code snippets to integrate your search"
msgstr "Nützliche Konfigurationen und Code-Snippets zur Integration Ihrer Suche"

#: templates/client/frontend/integration.html:10
msgid "Search input and results container selectors"
msgstr "Selektoren für Suchfeld und Ergebniscontainer"

#: templates/client/frontend/integration.html:11
msgid ""
"Fill in the selectors for the search input and results container of your "
"website"
msgstr ""
"Füllen Sie die Selektoren für das Suchfeld und den Ergebniscontainer Ihrer "
"Website aus"

#: templates/client/frontend/integration.html:17
msgid "Enter search input selector"
msgstr "Suchfeld-Selektor eingeben"

#: templates/client/frontend/integration.html:25
msgid "Enter results container selector"
msgstr "Ergebniscontainer-Selektor eingeben"

#: templates/client/frontend/integration.html:48
msgid "Copy to clipboard"
msgstr "In die Zwischenablage kopieren"

#: templates/client/frontend/integration.html:51
msgid "JS Code Snippet"
msgstr "JS-Code-Snippet"

#: templates/client/frontend/integration.html:54
msgid "Code snippet not available yet"
msgstr "Code-Snippet noch nicht verfügbar"

#: templates/client/frontend/integration.html:57
msgid "Copied to clipboard!"
msgstr "In die Zwischenablage kopiert!"

#: templates/client/frontend/integration.html:73
#: templates/macros/settings_modal.html:116
msgid "Please fill in all fields."
msgstr "Bitte füllen Sie alle Felder aus."

#: templates/client/frontend/integration.html:85
#: templates/macros/settings_modal.html:128
msgid "Invalid website settings id."
msgstr "Ungültige Website-Einstellungs-ID."

#: templates/client/frontend/integration.html:104
#: templates/macros/settings_modal.html:144
msgid "Website settings updated successfully."
msgstr "Website-Einstellungen erfolgreich aktualisiert."

#: templates/client/reports/search_terms.html:6
#: templates/components/sidebar.html:53
msgid "Search Terms"
msgstr "Suchbegriffe"

#: templates/client/reports/search_terms.html:9
msgid "Date Range"
msgstr "Datumsbereich"

#: templates/client/reports/search_terms.html:22
msgid "Search Term"
msgstr "Suchbegriff"

#: templates/client/reports/search_terms.html:25
msgid "Occurrences"
msgstr "Vorkommen"

#: templates/client/reports/search_terms.html:68
msgid "Date range selected:"
msgstr "Ausgewählter Datumsbereich:"

#: templates/client/reports/search_terms.html:68
msgid "to"
msgstr "bis"

#: templates/client/reports/search_terms.html:93
msgid "Error during data fetch:"
msgstr "Fehler beim Abrufen der Daten:"

#: templates/client/reports/search_terms.html:130
msgid "Search..."
msgstr "Suche..."

#: templates/client/reports/search_terms.html:131
msgid "Show _MENU_ entries"
msgstr "_MENU_ Einträge anzeigen"

#: templates/client/reports/search_terms.html:132
msgid "Showing _START_ to _END_ of _TOTAL_ entries"
msgstr "Zeige _START_ bis _END_ von _TOTAL_ Einträgen an"

#: templates/components/admin/navbar.html:7
#: templates/components/admin/sidebar.html:4 templates/components/navbar.html:8
#: templates/components/sidebar.html:6
msgid "Toolbrothers"
msgstr "Toolbrothers"

#: templates/components/navbar.html:62
msgid "Contact Us"
msgstr "Kontaktieren Sie uns"

#: templates/components/admin/navbar.html:28
#: templates/components/navbar.html:70
msgid "Change Language"
msgstr "Sprache ändern"

#: templates/components/admin/navbar.html:43
#: templates/components/navbar.html:91
msgid "User profile"
msgstr "Benutzerprofil"

#: templates/components/admin/navbar.html:50
#: templates/components/navbar.html:99
msgid "Dark Mode"
msgstr "Dunkelmodus"

#: templates/components/admin/navbar.html:59
#: templates/components/navbar.html:109
msgid "Profile"
msgstr "Profil"

#: templates/components/admin/navbar.html:68
#: templates/components/navbar.html:118
msgid "Logout"
msgstr "Abmelden"

#: templates/components/admin/navbar.html:79
#: templates/components/navbar.html:132
msgid "Home"
msgstr "Startseite"

#: templates/components/admin/navbar.html:81
#: templates/components/navbar.html:135
msgid "About"
msgstr "Über"

#: templates/components/admin/navbar.html:83
#: templates/components/navbar.html:138
msgid "Services"
msgstr "Dienstleistungen"

#: templates/components/admin/navbar.html:85
#: templates/components/navbar.html:141
msgid "Contact"
msgstr "Kontakt"

#: templates/components/admin/sidebar.html:11
#: templates/components/sidebar.html:13
msgid "Dashboard"
msgstr "Dashboard"

#: templates/components/sidebar.html:18
msgid "Data"
msgstr "Daten"

#: templates/components/sidebar.html:25
msgid "Synchronization"
msgstr "Synchronisation"

#: templates/components/sidebar.html:26
msgid "Search Stats"
msgstr "Suchstatistiken"

#: templates/components/sidebar.html:32
msgid "Frontend"
msgstr "Frontend"

#: templates/components/sidebar.html:39
msgid "Integration"
msgstr "Integration"

#: templates/components/sidebar.html:40
msgid "Customization"
msgstr "Anpassung"

#: templates/components/sidebar.html:46
msgid "Reports"
msgstr "Berichte"

#: templates/components/admin/sidebar.html:15
msgid "Manage filters"
msgstr "Filter verwalten"

#: templates/components/admin/sidebar.html:19
msgid "Monitor Websites"
msgstr "Websites überwachen"

#: templates/components/admin/sidebar.html:23
msgid "Security"
msgstr "Sicherheit"

#: templates/components/admin/sidebar.html:30
msgid "Accounts"
msgstr "Konten"

#: templates/components/admin/sidebar.html:38
#: templates/components/admin/sidebar.html:53
msgid "Review"
msgstr "Überprüfung"

#: templates/components/admin/sidebar.html:46
msgid "Websites"
msgstr "Websites"

#: templates/components/admin/sidebar.html:61
msgid "Scheduled Tasks"
msgstr "Geplante Aufgaben"

#: templates/components/admin/sidebar.html:68
msgid "Monitor"
msgstr "Überwachen"

#: templates/components/admin/sidebar.html:74
msgid "View Logs"
msgstr "Protokolle anzeigen"

#: templates/errors/error403.html:15
msgid "Access Denied"
msgstr "Zugriff verweigert"

#: templates/errors/error403.html:19
msgid ""
"You do not have the necessary permissions to view this page. Please check"
" your credentials and try again."
msgstr "Sie haben nicht die erforderlichen Berechtigungen, um diese Seite anzuzeigen. Bitte überprüfen Sie Ihre Anmeldeinformationen und versuchen Sie es erneut."

#: templates/errors/error403.html:20
msgid ""
"If you believe this is an error, contact support. Attempting to access "
"restricted content without authorization is not allowed."
msgstr "Wenn Sie glauben, dass dies ein Fehler ist, kontaktieren Sie den Support. Der Zugriff auf eingeschränkte Inhalte ohne Autorisierung ist nicht erlaubt."

#: templates/errors/error403.html:24 templates/errors/error500.html:23
msgid "Go to Homepage"
msgstr "Zur Startseite gehen"

#: templates/errors/error500.html:15
msgid "Oops! Something went wrong."
msgstr "Ups! Etwas ist schiefgelaufen."

#: templates/errors/error500.html:19
msgid ""
"We're having trouble processing your request. Our team has been notified "
"and is working to fix the issue. Please try again later."
msgstr ""
"Wir haben Probleme, Ihre Anfrage zu bearbeiten. Unser Team wurde benachrichtigt "
"und arbeitet daran, das Problem zu beheben. Bitte versuchen Sie es später erneut."

#: templates/errors/rate_limit_exceeded.html:15
msgid "Rate Limit Exceeded"
msgstr "Rate-Limit überschritten"

#: templates/errors/rate_limit_exceeded.html:19
msgid ""
"You've reached the maximum number of requests allowed in a given time "
"frame. Please wait a moment before trying again."
msgstr ""
"Sie haben die maximale Anzahl an Anfragen erreicht, die in einem bestimmten "
"Zeitrahmen erlaubt sind. Bitte warten Sie einen Moment, bevor Sie es erneut versuchen."

#: templates/errors/rate_limit_exceeded.html:23
msgid "Go to Login page"
msgstr "Zur Anmeldeseite gehen"

#: templates/macros/form_error.html:3 templates/macros/settings_modal.html:49
#: templates/partials/account_update_modal.html:84
#: templates/partials/form_error_notification.html:3
#: templates/partials/website_update_modal.html:52
msgid "Error!"
msgstr "Fehler!"

#: templates/macros/settings_modal.html:30
msgid "Enter CSV URL here"
msgstr "Geben Sie hier die CSV-URL ein"

#: templates/macros/settings_modal.html:35
msgid "Website Domain"
msgstr "Website-Domain"

#: templates/macros/settings_modal.html:39
msgid "Enter website domain here"
msgstr "Geben Sie hier die Website-Domain ein"

#: templates/macros/settings_modal.html:64
#: templates/partials/account_update_modal.html:99
#: templates/partials/form_success_notification.html:3
#: templates/partials/website_update_modal.html:68
msgid "Success!"
msgstr "Erfolg!"

#: templates/partials/account_update_modal.html:10
msgid "Update User Account"
msgstr "Benutzerkonto aktualisieren"

#: templates/partials/account_update_modal.html:33
msgid "Account Status"
msgstr "Kontostatus"

#: templates/partials/account_update_modal.html:44
msgid "Admin Account"
msgstr "Admin-Konto"

#: templates/partials/account_update_modal.html:65
msgid "New Password"
msgstr "Neues Passwort"

#: templates/partials/account_update_modal.html:68
msgid "Leave blank to keep current password"
msgstr "Leer lassen, um das aktuelle Passwort beizubehalten"

#: templates/partials/account_update_modal.html:76
msgid "Confirm your new password"
msgstr "Bestätigen Sie Ihr neues Passwort"

#: templates/partials/account_update_modal.html:192
msgid "Passwords do not match. Please try again."
msgstr "Die Passwörter stimmen nicht überein. Bitte versuchen Sie es erneut."

#: templates/partials/account_update_modal.html:233
#: templates/partials/website_update_modal.html:144
msgid "Account updated successfully."
msgstr "Konto erfolgreich aktualisiert."

#: templates/partials/website_update_modal.html:10
msgid "Update Website"
msgstr "Website aktualisieren"

#: templates/partials/website_update_modal.html:27
#: templates/websites/review.html:23
msgid "Website key"
msgstr "Website-Schlüssel"

#: templates/partials/website_update_modal.html:35
#: templates/partials/website_update_modal.html:38
msgid "Website Url"
msgstr "Website-URL"

#: templates/partials/website_update_modal.html:42
#: templates/partials/website_update_modal.html:45
msgid "CSV Url"
msgstr "CSV-URL"

#: templates/websites/register.html:5 templates/websites/review.html:72
msgid "Register a website"
msgstr "Eine Website registrieren"

#: templates/websites/register.html:28
msgid "Website domain"
msgstr "Website-Domain"

#: templates/websites/register.html:31
msgid "Enter the domain of the website"
msgstr "Geben Sie die Domain der Website ein"

#: templates/websites/register.html:38
msgid "Enter the URL of the CSV file"
msgstr "Geben Sie die URL der CSV-Datei ein"

#: templates/websites/review.html:3
msgid "Websites Review"
msgstr "Überprüfung der Websites"

#: templates/websites/review.html:11
msgid "Registered Websites"
msgstr "Registrierte Websites"

#: templates/websites/review.html:14
msgid "The following websites have been registered to be tracked."
msgstr "Die folgenden Websites wurden zur Verfolgung registriert."

#: templates/websites/review.html:26
msgid "Website url"
msgstr "Website-URL"

#: templates/websites/review.html:29
msgid "CSV url"
msgstr "CSV-URL"

#: templates/websites/review.html:68
msgid "You haven't registered any websites yet."
msgstr "Sie haben noch keine Websites registriert."

#: templates/websites/review.html:112
msgid "could not delete website, missing id."
msgstr "Website konnte nicht gelöscht werden, ID fehlt."

#: templates/websites/review.html:118
msgid "Are you sure you want to delete this website?"
msgstr "Sind Sie sicher, dass Sie diese Website löschen möchten?"
