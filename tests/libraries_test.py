import bcrypt

password = b"super secret password"
salt = bcrypt.gensalt()
hashed = bcrypt.hashpw(password, salt)
print(hashed)
assert bcrypt.checkpw(password, hashed)

# test with passlib
print("test with passlib")
from passlib.context import CryptContext
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
hashed_password = pwd_context.hash(password)
print(hashed_password)
assert pwd_context.verify(password, hashed_password)