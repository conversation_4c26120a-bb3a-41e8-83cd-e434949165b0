from typing import List, Optional
from models.models import ProductsIndexation
from models.Connexion import SessionLocal

class ProductIndexationRepository:

    def __init__(self) -> None:
        self.db_session = SessionLocal()
    
    def __exit__(self):
        self.db_session.close()
    
    def get_all(self, page: int = None, per_page: int = None) -> List[ProductsIndexation]:
        try:
            products_indexation = ProductsIndexation.get_all(self.db_session, page, per_page)
            return products_indexation
        except Exception as e:
            self.db_session.rollback()
            raise e

    def get_by_id(self, id: int) -> Optional[ProductsIndexation]:
        try:
            products_indexation = ProductsIndexation.get_by_id(self.db_session, id)
            return products_indexation
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def get_by_website_id(self, website_id: int, page: int = None, per_page: int = None) -> List[ProductsIndexation]:
        try:
            products_indexation = ProductsIndexation.get_by_website_id(self.db_session, website_id, page, per_page)
            return products_indexation
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def get_last_inserted_by_website_id(self, website_id: int) -> Optional[ProductsIndexation]:
        try:
            products_indexation = ProductsIndexation.get_last_inserted_by_website_id(self.db_session, website_id)
            return products_indexation
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def create(self, products_indexation_data: dict) -> ProductsIndexation:
        try:
            products_indexation = ProductsIndexation(**products_indexation_data)
            products_indexation = ProductsIndexation.create(self.db_session, products_indexation)
            return products_indexation
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def update(self, id: int, products_indexation_data: dict) -> ProductsIndexation:
        try:
            products_indexation = ProductsIndexation.get_by_id(self.db_session, id)
            for attr, value in products_indexation_data.items():
                setattr(products_indexation, attr, value)
            products_indexation = ProductsIndexation.update(self.db_session, products_indexation)
            return products_indexation
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def delete(self, id: int) -> bool:
        try:
            products_indexation = ProductsIndexation.delete(self.db_session, id)
            return products_indexation
        except Exception as e:
            self.db_session.rollback()
            raise e