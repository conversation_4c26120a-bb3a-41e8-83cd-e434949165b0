from typing import List
from sqlalchemy.orm import Session
from models.models import RolePermission
from models.Connexion import SessionLocal

class RolePermissionRepository:
    def __init__(self):
        self.db_session = SessionLocal()

    def __exit__(self):
        self.db_session.close()

    def get_all_by_role_id(self, role_id: int) -> List[RolePermission]:
        try:
            return RolePermission.get_all_by_role_id(self.db_session, role_id)
        except Exception as e:
            self.db_session.rollback()
            raise e
        

    def get_all_grouped_by_role_id(self) -> List[tuple]:
        try:
            return RolePermission.get_all_grouped_by_role_id(self.db_session)
        except Exception as e:
            self.db_session.rollback()
            raise e
        

    def get_by_role_id_and_permission_id(self, role_id: int, permission_id: int) -> RolePermission:
        try:
            return RolePermission.get_by_role_id_and_permission_id(self.db_session, role_id, permission_id)
        except Exception as e:
            self.db_session.rollback()
            raise e
        

    def create(self, role_permission: dict) -> RolePermission:
        try:
            new_role_permission = RolePermission(**role_permission)
            return RolePermission.create(self.db_session, new_role_permission)
        except Exception as e:
            self.db_session.rollback()
            raise e
        

    def delete(self, role_id: int, permission_id: int) -> bool:
        try:
            return RolePermission.delete(self.db_session, role_id, permission_id)
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def delete_all_by_role_id(self, role_id: int) -> bool:
        try:
            return RolePermission.delete_all_by_role_id(self.db_session, role_id)
        except Exception as e:
            self.db_session.rollback()
            raise e
        
