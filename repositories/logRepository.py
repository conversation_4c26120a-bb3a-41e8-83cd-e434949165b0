from typing import List, Optional, Tuple
from models.Connexion import SessionLocal
from models.models import Log
from sqlalchemy.orm import Session

class LogRepository:
    def __init__(self):
        self.db_session = SessionLocal()
    
    def __exit__(self):
        self.db_session.close()
    
    def get_session(self):
        return self.db_session
    
    def set_session(self, session):
        if self.db_session is not None:
            self.db_session.close()
        self.db_session = session

    def get_all(self, page: int = None, per_page: int = None) -> Tuple[List[Log], int]:
        try:
            return Log.get_all(self.db_session, page, per_page)
        except Exception as e:
            self.db_session.rollback()
            raise e

    def get_by_id(self, id: int) -> Optional[Log]:
        try:
            return Log.get_by_id(self.db_session, id)
        except Exception as e:
            self.db_session.rollback()
            raise e

    def create(self, log: Log) -> Log:
        try:
            return Log.create(self.db_session, log)
        except Exception as e:
            self.db_session.rollback()
            raise e

    def update(self, log: Log) -> Log:
        try:
            return Log.update(self.db_session, log)
        except Exception as e:
            self.db_session.rollback()
            raise e

    def delete(self, id: int) -> bool:
        try:
            return Log.delete(self.db_session, id)
        except Exception as e:
            self.db_session.rollback()
            raise e
