from models.Connexion import <PERSON>Local
from models.models import ScheduledTask, TaskExecutionLog
from sqlalchemy import desc

class TaskExecutionLogRepository:
    def __init__(self):
        self.db = SessionLocal()
    
    def __exit__(self):
        self.db.close()

    def create(self, log: dict):
        try:
            new_log = TaskExecutionLog(**log)
            return TaskExecutionLog.create(self.db, log=new_log)
        except Exception as e:
            self.db.rollback()
            raise e

    def get_by_task_id(self, task_id: int, page: int = None, per_page: int = None):
        try:
            return TaskExecutionLog.get_by_task_id(self.db, task_id=task_id, page=page, per_page=per_page)
        except Exception as e:
            self.db.rollback()
            raise e

    def get_latest_log_by_task_id(self, task_id: int):
        try:
            return self.db.query(TaskExecutionLog).filter(TaskExecutionLog.task_id == task_id).order_by(desc(TaskExecutionLog.execution_time)).first()
        except Exception as e:
            self.db.rollback()
            raise e

    def get_logs_by_date_range(self, start_date, end_date):
        try:
            return self.db.query(TaskExecutionLog).filter(
                TaskExecutionLog.execution_time.between(start_date, end_date)
            ).order_by(desc(TaskExecutionLog.execution_time)).all()
        except Exception as e:
            self.db.rollback()
            raise e

    def get_logs_by_website(self, website_id: int, page: int = None, per_page: int = None):
        try:
            query = self.db.query(TaskExecutionLog).join(ScheduledTask).filter(ScheduledTask.website_id == website_id)
            if page is not None and per_page is not None:
                items_offset = (page - 1) * per_page
                query = query.offset(items_offset).limit(per_page)
            return query.order_by(desc(TaskExecutionLog.execution_time)).all()
        except Exception as e:
            self.db.rollback()
            raise e

    def get_logs_for_all_websites(self, page: int = None, per_page: int = None):
        try:
            query = self.db.query(TaskExecutionLog).join(ScheduledTask).filter(ScheduledTask.website_id == None)
            if page is not None and per_page is not None:
                items_offset = (page - 1) * per_page
                query = query.offset(items_offset).limit(per_page)
            return query.order_by(desc(TaskExecutionLog.execution_time)).all()
        except Exception as e:
            self.db.rollback()
            raise e