from typing import List
from models.models import Role
from models.Connexion import SessionLocal

class RoleRepository:
    def __init__(self) -> None:
        self.db_session = SessionLocal()

    def __exit__(self):
        self.db_session.close()
    
    def get_session(self):
        return self.db_session
    
    def set_session(self, session):
        if self.db_session is not None:
            self.db_session.close()
        self.db_session = session
    
    def get_all(self, page: int = None, per_page: int = None) -> List[Role]:
        try:
            roles = Role.get_all(self.db_session, page, per_page)
            return roles
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def get_by_id(self, role_id: int) -> Role:
        try:
            role = Role.get_by_id(self.db_session, role_id)
            return role
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def get_by_name(self, name: str) -> Role:
        try:
            role = Role.get_by_name(self.db_session, name)
            return role
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def create(self, role_data: dict) -> Role:
        try:
            new_role = Role(**role_data)
            new_role = Role.create(self.db_session, new_role)
            return new_role
        except Exception as e:
            self.db_session.rollback()
            raise e

    def update(self, role_id: int, role_data: dict) -> Role:
        try:
            role = Role.get_by_id(self.db_session, role_id)
            for key, value in role_data.items():
                setattr(role, key, value)
            role = Role.update(self.db_session, role)
            return role
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def delete(self, role_id: int) -> bool:
        try:
            return Role.delete(self.db_session, role_id)
        except Exception as e:
            self.db_session.rollback()
            raise e