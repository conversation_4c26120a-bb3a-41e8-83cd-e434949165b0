from contextlib import contextmanager
from datetime import datetime
from models.Connexion import <PERSON><PERSON><PERSON><PERSON>
from models.models import ScheduledTask
from sqlalchemy import or_

class ScheduledTaskRepository:
    def __init__(self):
        self._db = None
    
    @property
    def db(self):
        if self._db is None:
            self._db = SessionLocal()
        return self._db
    
    def refresh_connection(self):
        if self._db is not None:
            self._db.close()
            self._db = None
    
    @contextmanager
    def session_scope(self):
        try:
            yield self.db
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            self.refresh_connection()
            raise e
    
    def get_all(self, page: int = None, per_page: int = None):
        try:
            with self.session_scope() as session:
                return ScheduledTask.get_all(session, page=page, per_page=per_page)
        except Exception as e:
            self.refresh_connection()
            raise e
    
    def get_by_id(self, task_id: int):
        try:
            with self.session_scope() as session:
                return ScheduledTask.get_by_id(session, task_id=task_id)
        except Exception as e:
            self.refresh_connection()
            raise e

    def create(self, task: dict):
        try:
            new_task = ScheduledTask(**task)
            with self.session_scope() as session:
                return ScheduledTask.create(session, task=new_task)
        except Exception as e:
            self.refresh_connection()
            raise e

    def update(self, task_id: int, update_data: dict):
        try:
            task = self.get_by_id(task_id)
            if task:
                for key, value in update_data.items():
                    setattr(task, key, value)
                with self.session_scope() as session:
                    return ScheduledTask.update(session, task=task)
            return None
        except Exception as e:
            self.refresh_connection()
            raise e

    def delete(self, task_id: int):
        try:
            with self.session_scope() as session:
                return ScheduledTask.delete(session, task_id=task_id)
        except Exception as e:
            self.refresh_connection()
            raise e

    def get_active_tasks(self):
        try:
            with self.session_scope() as session:
                return session.query(ScheduledTask).filter(ScheduledTask.is_active == True).all()
        except Exception as e:
            self.refresh_connection()
            raise e

    def get_tasks_by_website(self, website_id: int = None):
        try:
            with self.session_scope() as session:
                if website_id is None:
                    return session.query(ScheduledTask).filter(ScheduledTask.website_id == None).all()
                else:
                    return session.query(ScheduledTask).filter(ScheduledTask.website_id == website_id).all()
        except Exception as e:
            self.refresh_connection()
            raise e

    def get_all_website_tasks(self):
        try:
            with self.session_scope() as session:
                return session.query(ScheduledTask).filter(ScheduledTask.website_id == None).all()
        except Exception as e:
            self.refresh_connection()
            raise e

    def get_specific_website_tasks(self, website_id: int):
        try:
            with self.session_scope() as session:
                return session.query(ScheduledTask).filter(ScheduledTask.website_id == website_id).all()
        except Exception as e:
            self.refresh_connection()
            raise e

    def get_tasks_for_execution(self):
        try:
            with self.session_scope() as session:
                return session.query(ScheduledTask).filter(
                    ScheduledTask.is_active == True,
                    or_(ScheduledTask.last_run == None, ScheduledTask.next_run <= datetime.now())
                ).all()
        except Exception as e:
            self.refresh_connection()
            raise e