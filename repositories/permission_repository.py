from models.Connexion import SessionLocal
from models.models import Permission

class PermissionRepository():

    def __init__(self):
        self.db = SessionLocal()
    
    def __exit__(self):
        self.db.close()

    def get_all(self, page: int = None, per_page: int = None):
        try:
            return Permission.get_all(self.db, page=page, per_page=per_page)
        except Exception as e:
            self.db.rollback()
            raise e
    
    def get_by_id(self, id: int):
        try:
            return Permission.get_by_id(self.db, id=id)
        except Exception as e:
            self.db.rollback()
            raise e

    def get_by_name(self, name: str):
        try:
            return Permission.get_by_name(self.db, name=name)
        except Exception as e:
            self.db.rollback()
            raise e

    def create(self, permission: Permission):
        try:
            return Permission.create(self.db, permission=permission)
        except Exception as e:
            self.db.rollback()
            raise e

    def update(self, permission: Permission):
        try:
            return Permission.update(self.db, permission=permission)
        except Exception as e:
            self.db.rollback()
            raise e

    def delete(self, id: int):
        try:
            return Permission.delete(self.db, id=id)
        except Exception as e:
            self.db.rollback()
            raise e
