from typing import List
from models.Connexion import SessionLocal
from models.models import User

class UserRepository:
    def __init__(self):
        self.db_session = SessionLocal()
    
    def __exit__(self):
        self.db_session.close()
    
    def get_session(self):
        return self.db_session
    
    def set_session(self, session):
        if self.db_session is not None:
            self.db_session.close()
        self.db_session = session

    def get_all(self, page: int = None, per_page: int = None) -> List[User]:
        try:
            users = User.get_all(self.db_session, page, per_page)
            return users
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def get_all_by_role_name(self, role_name: str, page: int = None, per_page: int = None) -> List[User]:
        try:
            users = User.get_all_by_role_name(self.db_session, role_name, page, per_page)
            return users
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def get_by_email(self, email: str) -> User:
        try:
            user = User.get_by_email(self.db_session, email)
            return user
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def get_by_id(self, user_id: int) -> User:
        try:
            user = User.get_by_id(self.db_session, user_id)
            return user
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def get_by_username(self, username: str) -> User:
        try:
            user = User.get_by_username(self.db_session, username)
            return user
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        try:
            return User.verify_password(plain_password, hashed_password)
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def create(self, user_data: dict) -> User:
        try:
            new_user = User(**user_data)
            new_user = User.create(self.db_session, new_user)
            return new_user
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def update(self, user_id: int, user_data: dict) -> User:
        try:
            user = User.get_by_id(self.db_session, user_id)
            if 'password' in user_data:
                user_data['password'] = User.hash(user_data['password'])
            for key, value in user_data.items():
                setattr(user, key, value)
            user = User.update(self.db_session, user)
            return user
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def delete(self, user_id: int) -> bool:
        try:
            return User.delete(self.db_session, user_id)
        except Exception as e:
            self.db_session.rollback()
            raise e