from typing import List
from models.Connexion import SessionLocal
from models.models import User, Website, WebsiteSettings

class WebsiteRepository:

    def __init__(self) -> None:
        self.db_session = SessionLocal()
    
    def __exit__(self):
        self.db_session.close()

    def get_all(self, page: int = None, per_page: int = None) -> List[Website]:
        try:
            websites = Website.get_all(self.db_session, page, per_page)
            return websites
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def get_by_id(self, website_id: int) -> Website:
        try:
            website = Website.get_by_id(self.db_session, website_id)
            return website
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def get_by_key(self, website_key: str) -> Website:
        try:
            website = Website.get_by_key(self.db_session, website_key)
            return website
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def get_by_user_id(self, user_id: int) -> List[Website]:
        try:
            websites = Website.get_by_user_id(self.db_session, user_id)
            return websites
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def get_by_website_url(self, website_url: str) -> Website:
        try:
            website = Website.get_by_website_url(self.db_session, website_url)
            return website
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def get_active(self, user_id: int) -> Website:
        try:
            website = Website.get_active(self.db_session, user_id)
            return website
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def set_active(self, website_id: int, user_id: int) -> Website:
        try:
            website = Website.set_active(self.db_session, website_id, user_id)
            return website
        except Exception as e:
            self.db_session.rollback()
            raise e

    def create_website(self, website_data: dict) -> Website:
        try:
            # create website
            new_website = Website(**website_data)
            new_website = Website.create(self.db_session, new_website)
            return new_website
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def update_website(self, website_id: int, website_data: dict) -> Website:
        try:
            website = Website.get_by_id(self.db_session, website_id)
            for key, value in website_data.items():
                setattr(website, key, value)
            website = Website.update(self.db_session, website)
            return website
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def delete_website(self, website_id: int) -> bool:
        try:
            return Website.delete(self.db_session, website_id)
        except Exception as e:
            self.db_session.rollback()
            raise e
    

    # website settings
        
    def get_by_website_id(self, website_id: int) -> WebsiteSettings:
        try:
            website_settings = WebsiteSettings.get_by_website_id(self.db_session, website_id)
            return website_settings
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def get_by_website_settings_id(self, website_settings_id: int) -> WebsiteSettings:
        try:
            website_settings = WebsiteSettings.get_by_website_settings_id(self.db_session, website_settings_id)
            return website_settings
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def create_website_settings(self, website_settings_data: dict) -> WebsiteSettings:
        try:
            # create website settings
            new_website_settings = WebsiteSettings(**website_settings_data)
            new_website_settings = WebsiteSettings.create(self.db_session, new_website_settings)
            return new_website_settings
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def update_website_settings(self, website_id: int, website_settings_data: dict) -> WebsiteSettings:
        try:
            website_settings = WebsiteSettings.get_by_website_id(self.db_session, website_id)
            for key, value in website_settings_data.items():
                setattr(website_settings, key, value)
            website_settings = WebsiteSettings.update(self.db_session, website_settings)
            return website_settings
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def delete_website_settings(self, website_id: int) -> bool:
        try:
            return WebsiteSettings.delete(self.db_session, website_id)
        except Exception as e:
            self.db_session.rollback()
            raise e