from fastapi import Depends, HTTPException, status
from repositories.permission_repository import PermissionRepository
from schemas.UserSchema import PermissionSchema, PermissionCreateSchema, PermissionUpdateSchema


class PermissionService:
    def __init__(self, permission_repository: PermissionRepository = Depends(PermissionRepository)) -> None:
        self.permission_repository = permission_repository

    def get_all_permissions(self, page: int = None, per_page: int = None) -> PermissionSchema:
        try:
            permissions = self.permission_repository.get_all(page, per_page)
            return [PermissionSchema.model_validate(permission) for permission in permissions]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching permissions: {e}"
            )

    def get_permission_by_id(self, permission_id: int) -> PermissionSchema:
        try:
            permission = self.permission_repository.get_by_id(permission_id)
            return PermissionSchema.model_validate(permission)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching permission: {e}"
            )

    def create_permission(self, permission: PermissionCreateSchema) -> PermissionSchema:
        try:
            new_permission = self.permission_repository.create(permission.model_dump())
            return PermissionSchema.model_validate(new_permission)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while creating permission: {e}"
            )

    def update_permission(self, permission_id: int, permission_update_schema: PermissionUpdateSchema) -> PermissionSchema:
        try:
            permission = self.permission_repository.get_by_id(permission_id)
            if not permission:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Permission not found")
            update_data = permission_update_schema.model_dump(exclude_unset=True)
            updated_permission = self.permission_repository.update(permission_id, update_data)
            return PermissionSchema.model_validate(updated_permission)
        except Exception as e:
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while updating permission: {e}"
            )

    def delete_permission(self, permission_id: int) -> bool:
        permission = self.permission_repository.get_by_id(permission_id)
        if not permission:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Permission not found")
        try:
            return self.permission_repository.delete(permission_id)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while deleting permission: {e}"
            )
