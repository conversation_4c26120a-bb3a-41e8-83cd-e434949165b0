
import json
import re
from fastapi import Depends, HTTPException, status
from config import CDN_DOMAIN, ELASTICSEARCH_RELAY_URL
from enums.role_enum import RoleEnum
from repositories.role_repository import RoleRepository
from repositories.user_repository import UserRepository
from schemas.UserSchema import UserSchema, UserUpdateSchema, UserCreateSchema
import validators


class UserService:
    def __init__(self, user_repository: UserRepository = Depends(UserRepository), role_repository: RoleRepository = Depends(RoleRepository)) -> None:
        self.user_repository = user_repository
        self.role_repository = role_repository

        # make sure both repo use the same db session
        self.user_repository.set_session(self.role_repository.get_session())

    def get_all_users(self, page: int = None, per_page: int = None)-> UserSchema:
        try:
            users = self.user_repository.get_all(page, per_page)
            return [UserSchema.model_validate(user) for user in users]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching users: {e}"
            )

    def get_all_users_by_role(self, role_name: str, page: int = None, per_page: int = None) -> UserSchema:
        try:
            users = self.user_repository.get_all_by_role_name(role_name, page, per_page)
            return [UserSchema.model_validate(user) for user in users]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching users: {e}"
            )
    
    def get_user_by_id(self, user_id: int) -> UserSchema:
        try:
            user = self.user_repository.get_by_id(user_id)
            return UserSchema.model_validate(user)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching user: {e}"
            )
    
    def get_user_by_email(self, email: str) -> UserSchema:
        try:
            user = self.user_repository.get_by_email(email)
            return UserSchema.model_validate(user)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching user: {e}"
            )
    
    def get_user_by_username(self, username: str) -> UserSchema:
        try:
            user = self.user_repository.get_by_username(username)
            return UserSchema.model_validate(user)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching user: {e}"
            )

    def create_user(self, user: UserCreateSchema) -> UserSchema:
        # check if password and confirm password match
        if user.password != user.confirm_password:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Passwords do not match")
        # check if role exists
        role = self.role_repository.get_by_id(user.role_id)
        if not role:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Role not found")
        # check unique username
        existing_user = self.user_repository.get_by_username(user.username)
        if existing_user:
            raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="Username already exists")
        # check unique email
        existing_user = self.user_repository.get_by_email(user.email)
        if existing_user:
            raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="Email already exists")
        try:
            # remove confirm_password from user
            user_dict = user.model_dump()
            user_dict.pop('confirm_password')
            user_dict.update({
                "is_active": True,
                "role": role,
                "is_admin": role.name == RoleEnum.ADMIN.value
            })
            new_user = self.user_repository.create(user_dict)
            return UserSchema.model_validate(new_user)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while creating user: {e}"
            )
    
    def update_user(self, user_id: int, user_update_schema: UserUpdateSchema) -> UserSchema:
        try:
            # check if user exists
            user = self.user_repository.get_by_id(user_id)
            if not user:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
            update_data = user_update_schema.model_dump(exclude_unset=True)
            # update user role
            roles = self.role_repository.get_all()
            roles_dict = {role.name: role.role_id for role in roles}
            if update_data.get('is_admin') is True:
                update_data['role_id'] = roles_dict[RoleEnum.ADMIN.value]
            else:
                update_data['role_id'] = roles_dict[RoleEnum.USER.value]

            user = self.user_repository.update(user_id, update_data)
            return UserSchema.model_validate(user)
        except Exception as e:
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while updating user: {e}"
            )
    
    def delete_user(self, user_id: int) -> bool:
        user = self.user_repository.get_by_id(user_id)
        if not user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
        try:
            return self.user_repository.delete(user_id)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while deleting user: {e}"
            )

    def code_snippet(self, ws):
            js_files = ['utils.js', 'responsive_search.js', 'desktop_search.js', 'db_search_runner.js']
            css_files = ['desktop_search_style.css', 'mobile_search_style.css', 'responsive_price_filter.css']
            wk = ws['website']['website_key'].lower()
            js_path = f'{CDN_DOMAIN}/cdn/js/'
            css_path = f'{CDN_DOMAIN}/cdn/css/'

            # Create the list of full URLs for JS and CSS files
            js_urls = [js_path + f for f in js_files]
            css_urls = [css_path + f for f in css_files]

            # Create shortened and optimized JavaScript code
            code = f"""
                <script>
                document.addEventListener('DOMContentLoaded', function() {{
                    function lS(u,a) {{
                        return new Promise((r,e) => {{
                            const s = document.createElement('script');
                            s.src = u;
                            s.defer = true;
                            if (a) {{
                                Object.keys(a).forEach(k => s.setAttribute(k, a[k]));
                            }}
                            s.onload = () => r(s);
                            s.onerror = () => e(new Error(`Script load error: ${{u}}`));
                            document.body.appendChild(s);
                        }});
                    }}

                    async function lAll() {{
                        try {{
                            await lS('{js_urls[0]}', {{
                                'data-website-key': '{wk}',
                                'data-relay-server': '{ELASTICSEARCH_RELAY_URL}'
                            }});
                            await lS('{js_urls[1]}');
                            await lS('{js_urls[2]}');
                            await lS('{js_urls[3]}', {{
                                'data-input-search-selector': '{ws['search_input_selector']}',
                                'data-results-container-selector': '{ws['results_container_selector']}',
                                'data-website-key': '{wk}'
                            }});
                        }} catch (e) {{
                            console.error(e);
                        }}
                    }}

                    function lCSS() {{
                        {json.dumps(css_urls)}.forEach(u => {{
                            const l = document.createElement('link');
                            l.rel = 'stylesheet';
                            l.type = 'text/css';
                            l.href = u;
                            document.head.appendChild(l);
                        }});
                    }}

                    lAll();
                    lCSS();
                }});
                </script>
            """

            # Minify the JavaScript code
            minified_code = re.sub(r'\s+', ' ', code)
            minified_code = re.sub(r'(?<!:)\/\/.*|/\*.*?\*/', '', minified_code)  # Remove comments
            minified_code = minified_code.strip()

            return minified_code



    def email_validation(self, email: str) -> bool:
        try:
            v = validators.email(email)
            v = v == True
            if v:
                # check if email already exists
                user = self.user_repository.get_by_email(email)
                if user:
                    return False
                return True
            return False
        except:
            return False
    
    def username_validation(self, username: str) -> bool:
        # check if username already exists
        user = self.user_repository.get_by_username(username)
        if user:
            return False
        return True
