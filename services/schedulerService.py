from datetime import datetime, timedelta
import logging
from fastapi import Depends
import schedule
import threading
import time
import signal

from background_jobs.product_indexation import Integrator
from enums.task_schedule import ScheduleFrequency
from schemas.ScheduledTaskSchema import ScheduledTaskSchema, ScheduledTaskUpdateSchema
from schemas.TaskExecutionLogSchema import TaskExecutionLogCreateSchema
from services.scheduledTaskService import ScheduledTaskService, SERVER_TZ
from services.taskExecutionLogService import TaskExecutionLogService

class SchedulerService:
    def __init__(
        self,
        scheduled_task_service: ScheduledTaskService = Depends(ScheduledTaskService),
        task_execution_log_service: TaskExecutionLogService = Depends(TaskExecutionLogService),
        integrator: Integrator = Depends(Integrator)
    ):
        self.scheduled_task_service = scheduled_task_service
        self.task_execution_log_service = task_execution_log_service
        self.integrator = integrator
        self.scheduler_thread = None
        self.running = False

    def setup_scheduler(self):
        schedule.clear()
        tasks = self.scheduled_task_service.get_all_tasks()
        if not tasks:
            logging.info("No scheduled tasks found. Scheduler is running but idle.")
        for task in tasks:
            self.schedule_and_update_task(task)

    def schedule_and_update_task(self, task: ScheduledTaskSchema):
        if task.is_active:
            self._schedule_task(task)
        _is_scheduled = self.is_task_scheduled(task.task_id)
        update_schema = ScheduledTaskUpdateSchema(
            is_scheduled= _is_scheduled,
        )
        if _is_scheduled:
            next_run = self.scheduled_task_service._calculate_next_run(task.frequency, task.time, task.hours_interval)
            if next_run:
                update_schema.next_run = next_run
        self.scheduled_task_service.update_task(task.task_id, update_schema)


    def is_task_scheduled(self, task_id):
        frequencies = [f.value.lower() for f in ScheduleFrequency]
        for frequency in frequencies:
            tag = f"{frequency}_{task_id}"
            for job in schedule.get_jobs():
                if tag in job.tags:
                    return True
        return False

    def unschedule_task(self, task_id: int):
        task = self.scheduled_task_service.get_task_by_id(task_id)
        if not task:
            logging.warning(f"Attempted to unschedule non-existent task with id {task_id}")
            return

        # Remove the task from the scheduler
        schedule.clear(f"{task.frequency.value.lower()}_{task_id}")
        logging.info(f"Task {task_id} removed from scheduler")

    def deactivate_task(self, task_id: int) -> bool:
        task = self.scheduled_task_service.get_task_by_id(task_id)
        if not task:
            logging.warning(f"Attempted to deactivate non-existent task with id {task_id}")
            return False

        try:
            # Unschedule the task
            self.unschedule_task(task_id)

            # Update the task status in the database
            update_data = {
                "is_active": False,
                "is_scheduled": False
            }
            update_schema = ScheduledTaskUpdateSchema(**update_data)
            self.scheduled_task_service.update_task(task_id, update_schema)
            logging.info(f"Task {task_id} deactivated and unscheduled")

            return True
        except Exception as e:
            logging.error(f"Failed to deactivate task {task_id}: {e}")
            return False

    def reschedule_task(self, task_id: int, task_data: dict) -> bool:
        try:
            task = self.scheduled_task_service.get_task_by_id(task_id)
            if not task:
                logging.warning(f"Attempted to reschedule non-existent task with id {task_id}")
                return False

            update_data = {
                'is_active': True,
            }
            if 'time' in task_data:
                update_data['time'] = task_data['time']
            update_schema = ScheduledTaskUpdateSchema(**update_data)
            updated_task = self.scheduled_task_service.update_task(task_id, update_schema)

            # Unschedule the existing task before rescheduling
            self.unschedule_task(task_id)

            # Schedule the updated task
            self._schedule_task(updated_task)

            if not self.is_task_scheduled(updated_task.task_id):
                logging.error(f"Failed to schedule task {task_id}")
                return False

            # Update is_scheduled status
            update_schema = ScheduledTaskUpdateSchema(is_scheduled=True)
            self.scheduled_task_service.update_task(task_id, update_schema)

            logging.info(f"Task {task_id} rescheduled successfully")
            return True

        except Exception as e:
            import traceback
            traceback.print_exc()
            logging.error(f"Error rescheduling task {task_id}: {e}")
            return False
        
    def _schedule_task(self, task):
        try:
            # First, unschedule the task if it's already scheduled
            self.unschedule_task(task.task_id)

            # Get the timezone from task.time if it's timezone-aware
            task_tz = task.time.tzinfo if task.time.tzinfo else SERVER_TZ
            
            # Convert the stored times to server timezone
            task_time = task.time.replace(tzinfo=task_tz).astimezone(SERVER_TZ)

            if task.frequency == ScheduleFrequency.EVERY_X_HOURS:
                self._schedule_every_x_hours_task(task, task_time)
            elif task.frequency == ScheduleFrequency.DAILY:
                schedule.every().day.at(task_time.strftime("%H:%M")).do(
                    self._execute_task, task.task_id).tag(f"daily_{task.task_id}")
            elif task.frequency == ScheduleFrequency.WEEKLY:
                self._schedule_weekly_task(task, task_time)
            elif task.frequency == ScheduleFrequency.MONTHLY:
                self._schedule_monthly_task(task, task_time)
            elif task.frequency == ScheduleFrequency.ONE_TIME:
                self._schedule_one_time_task(task)
        except Exception as e:
            logging.error(f"Error scheduling task {task.task_id}: {e}")

    def _schedule_every_x_hours_task(self, task, task_time):
        schedule.every(task.hours_interval).hours.at(f":{task_time.minute:02d}").do(
            self._execute_task, task.task_id).tag(f"every_x_hours_{task.task_id}")

    def _schedule_weekly_task(self, task, task_time):
        days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
        day = days[task_time.weekday()]
        schedule.every().__getattribute__(day).at(task_time.strftime("%H:%M")).do(
            self._execute_task, task.task_id).tag(f"weekly_{task.task_id}")

    def _schedule_monthly_task(self, task, task_time):
        def monthly_job():
            now = datetime.now(SERVER_TZ)
            target_day = min(task_time.day, (now.replace(month=now.month % 12 + 1, day=1) - timedelta(days=1)).day)
            if now.day == target_day:
                self._execute_task(task.task_id)
        
        schedule.every().day.at(task_time.strftime("%H:%M")).do(monthly_job).tag(f"monthly_{task.task_id}")

    def _schedule_one_time_task(self, task):
        now = datetime.now(SERVER_TZ)
        
        # Get the timezone from task.time if it's timezone-aware
        task_tz = task.time.tzinfo if task.time.tzinfo else SERVER_TZ
        
        # Convert the stored times to server timezone
        next_run = task.next_run.replace(tzinfo=task_tz).astimezone(SERVER_TZ) if task.next_run else None
        task_time = task.time.replace(tzinfo=task_tz).astimezone(SERVER_TZ)
        
        execution_time = next_run if next_run else task_time
        
        if execution_time > now:
            delay = (execution_time - now).total_seconds()
            schedule.every(delay).seconds.do(self._execute_task, task.task_id).tag(f"one_time_{task.task_id}")
            logging.info(f"One-time task {task.task_id} scheduled for {execution_time}")
        else:
            logging.warning(f"One-time task {task.task_id} scheduled for past time {execution_time}. Skipping.")

    def _execute_task(self, task_id: int):
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                task = self.scheduled_task_service.get_task_by_id(task_id)
                if not task:
                    return

                now = datetime.now(SERVER_TZ)
                logging.info(f"Executing task {task_id} at {now}")
                start_time = now

                if task.website_id is None:
                    total_products = self.integrator.automatic_indexation()
                else:
                    website = self.scheduled_task_service.website_repository.get_by_id(task.website_id)
                    if website:
                        total_products = self.integrator.manual_indexation(website.website_key)

                end_time = datetime.now(SERVER_TZ)
                duration = (end_time - start_time).total_seconds()

                log = TaskExecutionLogCreateSchema(
                    task_id=task_id,
                    status="SUCCESS",
                    message="Task executed successfully",
                    total_products_indexed=total_products,
                    duration=int(duration)
                )
                self.task_execution_log_service.create_log(log)

                update_data = {
                    "last_run": start_time.replace(tzinfo=None),
                    'frequency': task.frequency,
                }

                if task.frequency == ScheduleFrequency.ONE_TIME:
                    update_data["is_active"] = False
                    update_data["is_scheduled"] = False
                    self.unschedule_task(task_id)
                
                update_schema = ScheduledTaskUpdateSchema(**update_data)
                task = self.scheduled_task_service.update_task(task_id, update_schema)

                if task.frequency != ScheduleFrequency.ONE_TIME:
                    self._schedule_task(task)
                
                break  # Success, exit the retry loop
            
            except Exception as e:
                retry_count += 1
                if "Server has gone away" in str(e) and retry_count < max_retries:
                    logging.warning(f"Database connection lost, attempting to reconnect. Retry {retry_count}/{max_retries}")
                    time.sleep(1)  # Wait before retrying
                    continue
                
                end_time = datetime.now(SERVER_TZ)
                duration = (end_time - start_time).total_seconds()

                log = TaskExecutionLogCreateSchema(
                    task_id=task_id,
                    status="FAILURE",
                    message=f"Task execution failed: {str(e)}",
                    total_products_indexed=0,
                    duration=int(duration)
                )
                self.task_execution_log_service.create_log(log)
                break


    def start_scheduler(self):
        self.setup_scheduler()
        self.running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler)
        self.scheduler_thread.start()
        logging.info("Scheduler started and running in background.")

        # register signal handlers for clean shutdown
        signal.signal(signal.SIGINT, self.stop_scheduler)
        signal.signal(signal.SIGTERM, self.stop_scheduler)

    def stop_scheduler(self, signum=None, frame=None):
        if self.scheduler_thread:
            self.running = False
            self.scheduler_thread.join()
        logging.info("Scheduler stopped.")

    def _run_scheduler(self):
        while self.running:
            schedule.run_pending()
            time.sleep(1)

    def refresh_scheduler(self):
        self.setup_scheduler()
    
