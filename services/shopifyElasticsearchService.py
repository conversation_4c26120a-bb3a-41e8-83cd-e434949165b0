from datetime import datetime, timezone
from elasticsearch import ConflictError, Elasticsearch, NotFoundError
from fastapi import <PERSON><PERSON>PEx<PERSON>, status
from config import ELASTICSEARCH_URL, ELASTICSEARCH_USERNAME, ELASTICSEARCH_PASSWORD
import logging
import re

class ShopifyElasticsearchService:
    """
    Shopify-specific Elasticsearch service for product variant search.

    Based on Shopify feed structure with fields:
    - product_id, title, description, vendor, product_type, tags, url, primary_image, all_images, created_at, status
    - variant_id, sku, barcode, price, inventory, is_available, compare_at_price

    Optimized for flexible search patterns like 'makita 1245' matching 'makita1245'
    """

    def __init__(self):
        self.es = Elasticsearch(
            hosts=[ELASTICSEARCH_URL],
            basic_auth=(ELASTICSEARCH_USERNAME, ELASTICSEARCH_PASSWORD),
            verify_certs=True,
        )

    def build_shopify_query(self, pattern=None, filters=None):
        """
        Build flexible Elasticsearch query for Shopify product variants.

        Handles flexible matching like:
        - 'makita 1245' matches 'makita1245'
        - 'makita1245' matches 'makita 1245'
        - Barcode/SKU exact matches
        - Fuzzy text matching
        """
        if not pattern:
            return {
                "function_score": {
                    "query": {
                        "bool": {
                            "must": [{"match_all": {}}],
                            "filter": self._build_filters(filters)
                        }
                    },
                    "functions": [{"random_score": {}}],
                    "boost_mode": "multiply"
                }
            }

        pattern = pattern.strip()
        if not pattern:
            return {"match_all": {}}

        # Create multiple variations of the search pattern for flexible matching
        pattern_variations = self._create_pattern_variations(pattern)

        should_clauses = []

        # 1. Exact barcode match (highest priority)
        should_clauses.append({
            "term": {
                "barcode.keyword": {
                    "value": pattern,
                    "boost": 1000
                }
            }
        })

        # 2. Exact SKU match (very high priority)
        should_clauses.append({
            "term": {
                "sku.keyword": {
                    "value": pattern,
                    "boost": 900
                }
            }
        })

        # 3. Flexible matching on title and SKU with all pattern variations
        for i, variation in enumerate(pattern_variations):
            boost_factor = 800 - (i * 50)  # Decrease boost for each variation

            # Title matching with phrase and phrase_prefix
            should_clauses.extend([
                {
                    "match_phrase": {
                        "title": {
                            "query": variation,
                            "slop": 2,
                            "boost": boost_factor
                        }
                    }
                },
                {
                    "match_phrase_prefix": {
                        "title": {
                            "query": variation,
                            "boost": boost_factor - 100
                        }
                    }
                },
                # SKU matching
                {
                    "match_phrase": {
                        "sku": {
                            "query": variation,
                            "slop": 1,
                            "boost": boost_factor + 100
                        }
                    }
                }
            ])

        # 4. Multi-field search with fuzziness for typos
        should_clauses.append({
            "multi_match": {
                "query": pattern,
                "fields": [
                    "title^3",
                    "sku^4",
                    "description^1",
                    "vendor^2",
                    "product_type^2",
                    "tags^1"
                ],
                "type": "best_fields",
                "fuzziness": "AUTO",
                "boost": 300
            }
        })

        # 5. Cross-field matching for complex queries
        should_clauses.append({
            "multi_match": {
                "query": pattern,
                "fields": [
                    "title^2",
                    "sku^3",
                    "vendor^1",
                    "product_type^1"
                ],
                "type": "cross_fields",
                "operator": "and",
                "boost": 200
            }
        })

        return {
            "bool": {
                "should": should_clauses,
                "minimum_should_match": 1,
                "filter": self._build_filters(filters)
            }
        }

    def _create_pattern_variations(self, pattern):
        """
        Create variations of search pattern for flexible matching.

        Examples:
        - 'makita 1245' -> ['makita 1245', 'makita1245', 'makita-1245']
        - 'makita1245' -> ['makita1245', 'makita 1245', 'makita-1245']
        """
        variations = [pattern]

        # Remove all spaces
        no_spaces = re.sub(r'\s+', '', pattern)
        if no_spaces != pattern:
            variations.append(no_spaces)

        # Add spaces before numbers if not present
        spaced_numbers = re.sub(r'([a-zA-Z])(\d)', r'\1 \2', pattern)
        if spaced_numbers != pattern:
            variations.append(spaced_numbers)

        # Replace spaces with hyphens
        hyphenated = re.sub(r'\s+', '-', pattern)
        if hyphenated != pattern:
            variations.append(hyphenated)

        # Replace hyphens with spaces
        unhyphenated = re.sub(r'-+', ' ', pattern)
        if unhyphenated != pattern:
            variations.append(unhyphenated)

        # Remove duplicates while preserving order
        seen = set()
        unique_variations = []
        for var in variations:
            if var not in seen:
                seen.add(var)
                unique_variations.append(var)

        return unique_variations[:5]  # Limit to 5 variations to avoid too many clauses

    def _build_filters(self, filters):
        """Build filter clauses based on Shopify feed structure."""
        if not filters:
            return []

        filter_clauses = []

        # Vendor filter (from feed: vendor field)
        if filters.get('vendor'):
            vendors = filters['vendor'] if isinstance(filters['vendor'], list) else [filters['vendor']]
            filter_clauses.append({
                "terms": {"vendor.keyword": vendors}
            })

        # Product type filter (from feed: product_type field)
        if filters.get('product_type'):
            types = filters['product_type'] if isinstance(filters['product_type'], list) else [filters['product_type']]
            filter_clauses.append({
                "terms": {"product_type.keyword": types}
            })

        # Price range filter (from feed: price field)
        if filters.get('price') and isinstance(filters['price'], dict):
            price_filter = {"range": {"price": {}}}
            if 'min' in filters['price']:
                price_filter["range"]["price"]["gte"] = filters['price']['min']
            if 'max' in filters['price']:
                price_filter["range"]["price"]["lte"] = filters['price']['max']
            filter_clauses.append(price_filter)

        # Availability filter (from feed: is_available field)
        if filters.get('is_available') is not None:
            filter_clauses.append({
                "term": {"is_available": filters['is_available']}
            })

        # Status filter (from feed: status field - active, draft, archived)
        if filters.get('status'):
            statuses = filters['status'] if isinstance(filters['status'], list) else [filters['status']]
            filter_clauses.append({
                "terms": {"status.keyword": statuses}
            })

        # Tags filter (from feed: tags field)
        if filters.get('tags'):
            tags = filters['tags'] if isinstance(filters['tags'], list) else [filters['tags']]
            # Tags might be comma-separated in the feed, so we use match query
            filter_clauses.append({
                "bool": {
                    "should": [
                        {"match": {"tags": tag}} for tag in tags
                    ],
                    "minimum_should_match": 1
                }
            })

        # Inventory filter (from feed: inventory field)
        if filters.get('inventory') and isinstance(filters['inventory'], dict):
            inventory_filter = {"range": {"inventory": {}}}
            if 'min' in filters['inventory']:
                inventory_filter["range"]["inventory"]["gte"] = filters['inventory']['min']
            if 'max' in filters['inventory']:
                inventory_filter["range"]["inventory"]["lte"] = filters['inventory']['max']
            filter_clauses.append(inventory_filter)

        return filter_clauses

    def search_products(self, index, pattern, current_page=0, page_size=50, args=None):
        """
        Main search method for Shopify product variants.
        Matches the interface pattern but optimized for Shopify feed structure.
        """
        try:
            # Build the query
            query = self.build_shopify_query(pattern, args)

            # Calculate pagination
            from_offset = current_page * page_size

            # Define sort options based on Shopify fields
            sort_options = {
                "relevance": [{"_score": {"order": "desc"}}],
                "price_asc": [{"price": {"order": "asc"}}, {"_score": {"order": "desc"}}],
                "price_desc": [{"price": {"order": "desc"}}, {"_score": {"order": "desc"}}],
                "title_asc": [{"title.keyword": {"order": "asc"}}],
                "created_desc": [{"created_at": {"order": "desc"}}],
                "inventory_desc": [{"inventory": {"order": "desc"}}]
            }

            sort_by = args.get('sort', 'relevance') if args else 'relevance'

            search_body = {
                "query": query,
                "sort": sort_options.get(sort_by, sort_options["relevance"]),
                "from": from_offset,
                "size": page_size,
                "aggs": {
                    # Price statistics
                    "min_price": {"min": {"field": "price"}},
                    "max_price": {"max": {"field": "price"}},

                    # Vendor aggregation (from Shopify feed)
                    "vendors": {
                        "terms": {
                            "field": "vendor.keyword",
                            "size": 100,
                            "order": {"_count": "desc"}
                        }
                    },

                    # Product type aggregation (from Shopify feed)
                    "product_types": {
                        "terms": {
                            "field": "product_type.keyword",
                            "size": 100,
                            "order": {"_count": "desc"}
                        }
                    },

                    # Status aggregation (active, draft, archived)
                    "status": {
                        "terms": {
                            "field": "status.keyword",
                            "size": 10
                        }
                    },

                    # Availability aggregation
                    "availability": {
                        "terms": {
                            "field": "is_available",
                            "size": 2
                        }
                    }
                }
            }

            response = self.es.search(index=index, body=search_body)
            return self._format_response(response)

        except Exception as e:
            logging.error(f"Shopify search error: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Search failed: {str(e)}"
            )

    def _format_response(self, response):
        """Format Elasticsearch response based on Shopify feed structure."""
        if not response:
            return None

        try:
            hits = response.get('hits', {}).get('hits', [])
            products = []

            # Format each product variant based on feed structure
            for hit in hits:
                source = hit['_source']
                product = {
                    # Product fields from feed
                    'product_id': source.get('product_id'),
                    'title': source.get('title'),
                    'description': source.get('description'),
                    'vendor': source.get('vendor'),
                    'product_type': source.get('product_type'),
                    'tags': source.get('tags'),
                    'url': source.get('url'),
                    'primary_image': source.get('primary_image'),
                    'all_images': source.get('all_images'),
                    'created_at': source.get('created_at'),
                    'status': source.get('status'),

                    # Variant fields from feed
                    'variant_id': source.get('variant_id'),
                    'sku': source.get('sku'),
                    'barcode': source.get('barcode'),
                    'price': source.get('price'),
                    'inventory': source.get('inventory', 0),
                    'is_available': source.get('is_available', False),
                    'compare_at_price': source.get('compare_at_price'),

                    # Search metadata
                    'score': hit.get('_score', 0)
                }
                products.append(product)

            # Process aggregations
            aggregations = response.get('aggregations', {})

            # Format aggregations for easy consumption
            filter_elements = {
                'minPrice': aggregations.get('min_price', {}).get('value'),
                'maxPrice': aggregations.get('max_price', {}).get('value'),
                'vendors': [bucket['key'] for bucket in aggregations.get('vendors', {}).get('buckets', [])],
                'productTypes': [bucket['key'] for bucket in aggregations.get('product_types', {}).get('buckets', [])],
                'statuses': [bucket['key'] for bucket in aggregations.get('status', {}).get('buckets', [])],
                'availability': {
                    bucket['key']: bucket['doc_count']
                    for bucket in aggregations.get('availability', {}).get('buckets', [])
                }
            }

            result = {
                'data': products,
                'totalProducts': response.get('hits', {}).get('total', {}).get('value', 0),
                'filterElements': filter_elements,
                'took': response.get('took', 0),
                'maxScore': response.get('hits', {}).get('max_score')
            }

            return result

        except Exception as e:
            logging.error(f"Error formatting Shopify response: {e}")
            raise HTTPException(
                status_code=500,
                detail="Error processing search results"
            )

