import calendar
import logging
from typing import List, Optional
from zoneinfo import ZoneInfo
from fastapi import Depends, HTTPException, status
from repositories.scheduled_task_repository import ScheduledTaskRepository
from repositories.website_repository import WebsiteRepository
from schemas.ScheduledTaskSchema import ScheduledTaskCreateSchema, ScheduledTaskUpdateSchema, ScheduledTaskSchema
from enums.task_schedule import ScheduleFrequency
from datetime import datetime, timedelta
import pytz

from utils.datetimeUtil import get_server_timezone

# Setting up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# get server's timezone
SERVER_TZ = get_server_timezone()

def serialize_scheduled_task(task: ScheduledTaskSchema, depth: int = 0, user_tz: ZoneInfo = ZoneInfo('UTC')) -> ScheduledTaskSchema:
    if depth < 0:
        return None

    def convert_timezone(dt: datetime) -> datetime:
        if dt is not None:
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=SERVER_TZ)
            return dt.astimezone(user_tz)
        return dt

    return ScheduledTaskSchema(
        task_id=task.task_id,
        name=task.name,
        description=task.description,
        frequency=task.frequency,
        indexing_type=task.indexing_type,
        time=convert_timezone(task.time),
        is_active=task.is_active,
        is_scheduled=task.is_scheduled,
        hours_interval=task.hours_interval,
        last_run=convert_timezone(task.last_run),
        next_run=convert_timezone(task.next_run),
        website_id=task.website_id,
        created_at=convert_timezone(task.created_at),
        updated_at=convert_timezone(task.updated_at)
    )

class ScheduledTaskService:
    def __init__(self, 
                 task_repository: ScheduledTaskRepository = Depends(ScheduledTaskRepository),
                 website_repository: WebsiteRepository = Depends(WebsiteRepository)):
        self.task_repository = task_repository
        self.website_repository = website_repository

    def get_all_tasks(self, page: int = None, per_page: int = None) -> List[ScheduledTaskSchema]:
        try:
            tasks = self.task_repository.get_all(page, per_page)
            return [serialize_scheduled_task(task) for task in tasks]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching tasks: {e}"
            )

    def get_task_by_id(self, task_id: int) -> ScheduledTaskSchema:
        try:
            task = self.task_repository.get_by_id(task_id)
            if not task:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Task not found")
            return serialize_scheduled_task(task)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching task: {e}"
            )

    def create_task(self, task: ScheduledTaskCreateSchema) -> ScheduledTaskSchema:
        # Check if website exists if website_id is provided
        if task.website_id is not None:
            website = self.website_repository.get_by_id(task.website_id)
            if not website:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Website not found")

        try:
            new_task = task.model_dump(exclude_unset=True)
        
            input_time = new_task['time']
            print(f"Received input time: {input_time}")
            
            # Ensure the input_time is timezone-aware
            if input_time.tzinfo is None:
                input_time = input_time.replace(tzinfo=pytz.UTC)
            
            # Convert to server timezone
            server_time = input_time.astimezone(SERVER_TZ)
            print(f"Converted to server time: {server_time}")
            
            new_task['time'] = server_time.replace(tzinfo=None)
            
            hours_interval = new_task.get('hours_interval', None)
            next_run = self._calculate_next_run(new_task['frequency'], server_time, hours_interval)
            new_task.update({'next_run': next_run})

            created_task = self.task_repository.create(new_task)
            return serialize_scheduled_task(created_task)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while creating task: {e}"
            )

    def update_task(self, task_id: int, task_update: ScheduledTaskUpdateSchema) -> ScheduledTaskSchema:
        existing_task = self.task_repository.get_by_id(task_id)
        if not existing_task:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Task not found")

        try:
            update_data = task_update.model_dump(exclude_unset=True)

            if 'website_id' in update_data and update_data['website_id'] is not None:
                website = self.website_repository.get_by_id(update_data['website_id'])
                if not website:
                    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Website not found")

            # Get the existing task's timezone or use SERVER_TZ as default
            task_tz = existing_task.time.tzinfo if existing_task.time.tzinfo else SERVER_TZ

            if 'time' in update_data:
                new_time = update_data['time']
                
                # If the new time is naive, assume it's in the task's timezone
                if new_time.tzinfo is None:
                    new_time = new_time.replace(tzinfo=task_tz)
                
                # Convert to SERVER_TZ for consistent handling
                new_time = new_time.astimezone(SERVER_TZ)
                
                # Store as naive datetime in SERVER_TZ
                update_data.update({'time': new_time.replace(tzinfo=None)})

            if 'frequency' in update_data or 'time' in update_data:
                frequency = update_data.get('frequency', existing_task.frequency)
                time = update_data.get('time', existing_task.time)
                hours_interval = update_data.get('hours_interval', existing_task.hours_interval)

                # Ensure time is timezone-aware for _calculate_next_run
                time_with_tz = time.replace(tzinfo=SERVER_TZ)
                
                next_run = self._calculate_next_run(frequency, time_with_tz, hours_interval)
                update_data.update({'next_run': next_run.replace(tzinfo=None)})  # Store as naive datetime
            updated_task = self.task_repository.update(task_id, update_data)

            return serialize_scheduled_task(updated_task)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while updating task: {e}"
            )

    def get_tasks_by_website(self, website_id: Optional[int] = None) -> List[ScheduledTaskSchema]:
        try:
            tasks = self.task_repository.get_tasks_by_website(website_id)
            return [serialize_scheduled_task(task) for task in tasks]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching tasks: {e}"
            )

    def delete_task(self, task_id: int) -> bool:
        try:
            return self.task_repository.delete(task_id)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while deleting task: {e}"
            )

    def _calculate_next_run(self, frequency: ScheduleFrequency, time: datetime, hours_interval: Optional[int] = None) -> datetime:
        now = datetime.now(SERVER_TZ)
        print(f"Now: {now}")
        print(f"Input time: {time}")
        
        if time.tzinfo is None:
            time = time.replace(tzinfo=SERVER_TZ)
        else:
            time = time.astimezone(SERVER_TZ)
        
        print(f"Adjusted time: {time}")
        
        if frequency == ScheduleFrequency.EVERY_X_HOURS:
            if hours_interval is None or hours_interval <= 0:
                raise ValueError("Invalid hours_interval for EVERY_X_HOURS frequency")
            
            # If the given time is in the future, use it as the next run
            if time > now:
                next_run = time
            else:
                # Calculate how many intervals have passed since the given time
                time_diff = now - time
                intervals_passed = time_diff // timedelta(hours=hours_interval)
                
                # Calculate the next run time
                next_run = time + timedelta(hours=hours_interval * (intervals_passed + 1))

        elif frequency == ScheduleFrequency.DAILY:
            # Extract the time component (hour, minute, second) from the input time
            run_time = time.time()
            
            # Set the next run to today at the specified time
            next_run = datetime.combine(now.date(), run_time).replace(tzinfo=SERVER_TZ)
            
            # If this time has already passed today, schedule for tomorrow
            if next_run <= now:
                next_run += timedelta(days=1)
            
        elif frequency == ScheduleFrequency.WEEKLY:
            # Extract the day of week and time from the input
            target_weekday = time.weekday()
            target_time = time.time()
            
            # Calculate days until the next occurrence of the target weekday
            days_ahead = target_weekday - now.weekday()
            if days_ahead <= 0:  # Target day has already passed this week
                days_ahead += 7
            
            # Calculate the next run date
            next_run_date = now.date() + timedelta(days=days_ahead)
            
            # Combine the next run date with the target time
            next_run = datetime.combine(next_run_date, target_time).replace(tzinfo=SERVER_TZ)
            
            # If the calculated time is in the past (can happen if scheduling on the same day),
            # move to the next week
            if next_run <= now:
                next_run += timedelta(days=7)
        elif frequency == ScheduleFrequency.MONTHLY:
            target_day = time.day
            target_time = time.time()
            
            # Start with this month
            next_run = datetime(now.year, now.month, 1, tzinfo=SERVER_TZ)
            
            # Move to next month if this month's target day has passed
            if now.day > target_day or (now.day == target_day and now.time() > target_time):
                if now.month == 12:
                    next_run = next_run.replace(year=now.year + 1, month=1)
                else:
                    next_run = next_run.replace(month=now.month + 1)
            
            # Adjust the day, handling cases where the target day exceeds the days in the month
            _, last_day = calendar.monthrange(next_run.year, next_run.month)
            next_run = next_run.replace(day=min(target_day, last_day))
            
            # Set the time component
            next_run = next_run.replace(hour=target_time.hour, minute=target_time.minute, second=target_time.second)
            
        else:  # ONE_TIME
            next_run = time  # Use the exact time provided
            # if next_run <= now:
            #     raise ValueError("ONE_TIME task cannot be scheduled in the past")
        
        return next_run.replace(tzinfo=None)  # Remove tzinfo for database storage
    