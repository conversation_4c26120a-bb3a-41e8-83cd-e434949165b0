
from typing import List, <PERSON><PERSON>
from fastapi import Depends, HTTPException, status
from repositories.logRepository import LogRepository
from schemas.LogSchema import LogCreateSchema, LogSchema

class LogService:
    def __init__(self, log_repository: LogRepository = Depends(LogRepository)):
        self.log_repository = log_repository

    def create_log(self, log: LogCreateSchema) -> LogSchema:
        try:
            created_log = self.log_repository.create(log.model_dump())
            return LogSchema.model_validate(created_log)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while creating log: {e}"
            )

    def get_all_logs(self, page: int = None, per_page: int = None) -> Tuple[List[LogSchema], int]:
        try:
            logs, count = self.log_repository.get_all(page, per_page)
            return ([LogSchema.model_validate(log) for log in logs], count)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching logs: {e}"
            )

    def get_log_by_id(self, id: int) -> LogSchema:
        try:
            log = self.log_repository.get_by_id(id)
            if not log:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Log not found")
            return LogSchema.model_validate(log)
        except Exception as e:
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching the log: {e}"
            )

    def update_log(self, id: int, log: LogCreateSchema) -> LogSchema:
        try:
            existing_log = self.log_repository.get_by_id(id)
            if not existing_log:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Log not found")
            updated_log = self.log_repository.update(id, log.model_dump())
            return LogSchema.model_validate(updated_log)
        except Exception as e:
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while updating the log: {e}"
            )

    def delete_log(self, id: int) -> bool:
        try:
            deleted = self.log_repository.delete(id)
            if not deleted:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Log not found")
            return deleted
        except Exception as e:
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while deleting the log: {e}"
            )
