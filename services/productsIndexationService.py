from typing import Dict, Optional
from fastapi import Depends, HTTPException
from fastapi.encoders import jsonable_encoder
from repositories.product_indexation_repository import ProductIndexationRepository
from repositories.website_repository import WebsiteRepository
from services.websiteService import WebsiteService

class ProductIndexationService:

    def __init__(self, product_indexation_repository: ProductIndexationRepository = Depends(ProductIndexationRepository), website_service: WebsiteService = Depends(WebsiteService)):
        self.product_indexation_repository = product_indexation_repository
        self.website_service = website_service
    
    def get_all_indexation(self, website_key: str) -> Optional[Dict[str, str | int]]:
        try:
            return self.product_indexation_repository.get_all()
        except Exception as e:
            return None

    def format_duration(self, duration: int) -> str:
        """
        Formats a duration in seconds to a human-readable string.

        Args:
            duration (int): The duration in seconds.

        Returns:
            str: The formatted duration string.
        """
        if duration > 60:
            return f"{duration / 60:.1f} minutes"
        else:
            return f"{duration} seconds"


    def product_indexation_statistics(
        self,
        website_key: str
    ) -> Optional[Dict[str, str | int]]:
        """
        Retrieves and processes product indexation statistics for a given website.

        Args:
            db (Session): The database session.
            website_key (str): The key of the website.
            template (Jinja2Templates): The Jinja2 template object.
            request (Request): The FastAPI request object.

        Returns:
            Optional[Dict[str, str | int]]: A dictionary containing the processed statistics, or None if an exception occurs.
        """
        try:
            # Get the website
            website = jsonable_encoder(self.website_service.get_by_key(website_key))
            # get the product indexation for the website
            product_indexation = self.product_indexation_repository.get_by_website_id(website["website_id"])
            if not len(product_indexation):
                return {}
            # get the last product indexation
            last_product_indexation = self.product_indexation_repository.get_last_inserted_by_website_id(website["website_id"])

            # compute average download duration
            avg_download_duration = sum(
                item.download_duration for item in product_indexation
            ) // len(product_indexation)
            avg_indexation_duration = sum(item.duration for item in product_indexation) // len(
                product_indexation
            )

            res = {
                "avg_download_duration": self.format_duration(avg_download_duration),
                "avg_indexation_duration": self.format_duration(avg_indexation_duration),
                "duration": self.format_duration(last_product_indexation.duration),
                "download_duration": self.format_duration(last_product_indexation.download_duration),
                "total_products": last_product_indexation.total_products,
            }
            return res
        except Exception as exc:
            import traceback
            print(traceback.format_exc())
            raise HTTPException(status_code=500, detail=str(exc))
