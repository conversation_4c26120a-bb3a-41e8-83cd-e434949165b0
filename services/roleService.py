from fastapi import Depends, HTTPException, status
from repositories.role_repository import RoleRepository
from repositories.permission_repository import PermissionRepository
from repositories.role_permission_repository import RolePermissionRepository
from schemas.UserSchema import RoleSchema, RoleUpdateSchema, RoleCreateSchema


class RoleService:
    def __init__(
            self, 
            role_repository: RoleRepository = Depends(RoleRepository),
            permission_repository: PermissionRepository = Depends(PermissionRepository),
            role_permission_repository: RolePermissionRepository = Depends(RolePermissionRepository)
    ) -> None:
        self.role_repository = role_repository
        self.permission_repository = permission_repository
        self.role_permission_repository = role_permission_repository

    def get_all_roles(self, page: int = None, per_page: int = None) -> RoleSchema:
        try:
            roles = self.role_repository.get_all(page, per_page)
            return [RoleSchema.model_validate(role) for role in roles]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching roles: {e}"
            )

    def get_role_by_id(self, role_id: int) -> RoleSchema:
        try:
            role = self.role_repository.get_by_id(role_id)
            return RoleSchema.model_validate(role)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching role: {e}"
            )

    def create_role(self, role: RoleCreateSchema) -> RoleSchema:
        try:
            role_exists = self.role_repository.get_by_name(role.name)
            if role_exists:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Role with given name already exists")
            role_data = {
                "name": role.name,
            }
            new_role = self.role_repository.create(role_data)
            for permission_id in role.permissions:
                # if permission exists, create a new role_permission entry
                permission = self.permission_repository.get_by_id(permission_id)
                if permission:
                    role_permission_data = {
                        "role_id": new_role.role_id,
                        "permission_id": permission_id,
                    }
                    self.role_permission_repository.create(role_permission_data)
            return RoleSchema.model_validate(new_role)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while creating role: {e}"
            )

    def update_role(self, role_id: int, role_update_schema: RoleUpdateSchema) -> RoleSchema:
        try:
            role = self.role_repository.get_by_id(role_id)
            if not role:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Role not found")
            if role_update_schema.name:
                role_data = {
                    "name": role_update_schema.name or role.name
                }
                updated_role = self.role_repository.update(role_id, role_data)
            if role_update_schema.permissions and len(role_update_schema.permissions) > 0:
                # Remove existing role permissions
                self.role_permission_repository.delete_all_by_role_id(role_id)
                
                # Add new permissions
                for permission_id in role_update_schema.permissions:
                    permission = self.permission_repository.get_by_id(permission_id)
                    if permission:
                        role_permission_data = {
                            "role_id": updated_role.role_id,
                            "permission_id": permission_id,
                        }
                        self.role_permission_repository.create(role_permission_data)
            
            return RoleSchema.model_validate(updated_role)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while updating role: {e}"
            )
    
    def delete_role(self, role_id: int) -> bool:
        try:
            role = self.role_repository.get_by_id(role_id)
            if not role:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Role not found")
            self.role_repository.delete(role_id)
            return True
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while deleting role: {e}"
            )