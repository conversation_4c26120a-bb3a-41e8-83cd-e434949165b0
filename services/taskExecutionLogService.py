from typing import List
from fastapi import Depends, HTTPException, status
from repositories.task_execution_log_repository import TaskExecutionLogRepository
from schemas.TaskExecutionLogSchema import TaskExecutionLogCreateSchema, TaskExecutionLogSchema
from datetime import datetime

def serialize_task_execution_log(log: TaskExecutionLogSchema) -> TaskExecutionLogSchema:
    return TaskExecutionLogSchema(
        log_id=log.log_id,
        task_id=log.task_id,
        execution_time=log.execution_time,
        status=log.status,
        message=log.message,
        total_products_indexed=log.total_products_indexed,
        duration=log.duration
    )

class TaskExecutionLogService:
    def __init__(self, log_repository: TaskExecutionLogRepository = Depends(TaskExecutionLogRepository)):
        self.log_repository = log_repository

    def create_log(self, log: TaskExecutionLogCreateSchema) -> TaskExecutionLogSchema:
        try:
            created_log = self.log_repository.create(log.model_dump())
            return serialize_task_execution_log(created_log)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while creating log: {e}"
            )

    def get_logs_by_task_id(self, task_id: int, page: int = None, per_page: int = None) -> List[TaskExecutionLogSchema]:
        try:
            logs = self.log_repository.get_by_task_id(task_id, page, per_page)
            return [serialize_task_execution_log(log) for log in logs]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching logs: {e}"
            )

    def get_latest_log_by_task_id(self, task_id: int) -> TaskExecutionLogSchema:
        try:
            log = self.log_repository.get_latest_log_by_task_id(task_id)
            if not log:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No logs found for this task")
            return serialize_task_execution_log(log)
        except Exception as e:
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching the latest log: {e}"
            )

    def get_logs_by_date_range(self, start_date: datetime, end_date: datetime) -> List[TaskExecutionLogSchema]:
        try:
            logs = self.log_repository.get_logs_by_date_range(start_date, end_date)
            return [serialize_task_execution_log(log) for log in logs]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching logs: {e}"
            )

    def get_logs_by_website(self, website_id: int, page: int = None, per_page: int = None) -> List[TaskExecutionLogSchema]:
        try:
            logs = self.log_repository.get_logs_by_website(website_id, page, per_page)
            return [serialize_task_execution_log(log) for log in logs]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching logs: {e}"
            )

    def get_logs_for_all_websites(self, page: int = None, per_page: int = None) -> List[TaskExecutionLogSchema]:
        try:
            logs = self.log_repository.get_logs_for_all_websites(page, per_page)
            return [serialize_task_execution_log(log) for log in logs]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching logs: {e}"
            )