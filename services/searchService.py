import json
import os
from threading import Lock
import logging

from fastapi import HTTP<PERSON>x<PERSON>

from config import BASE_DIR

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SearchService:
    _lock = Lock()

    def __init__(self):
        self.sample_filter_file = os.path.join(BASE_DIR, "config/sample_search_filters.json")
        self.filter_file = os.path.join(BASE_DIR, "config/search_filters.json")

    def _load_json_file(self, file_path: str) -> dict:
        """Load JSON file and return as a dictionary. Handle missing or malformed files gracefully."""
        try:
            if os.path.exists(file_path):
                with open(file_path, "r") as file:
                    return json.load(file)
            else:
                return {}
        except (json.JSONDecodeError, IOError) as e:
            logger.error(f"Error reading JSON file {file_path}: {e}")
            return {}

    def _save_json_file(self, file_path: str, data: dict):
        """Save data to JSON file."""
        try:
            with open(file_path, "w") as file:
                json.dump(data, file, indent=2)
            logger.info(f"Successfully wrote to {file_path}")
        except IOError as e:
            logger.error(f"Error writing JSON file {file_path}: {e}")

    def get_search_filters(self, website_key: str) -> dict:
        """Retrieve filters for a given website key and sort them by the 'order' field."""
        website_key = website_key.lower()
        with self._lock:
            # Load the existing filters
            customization_json = self._load_json_file(self.filter_file)

            # If the website key exists, return its filters
            if website_key in customization_json:
                logger.info(f"Found filters for {website_key} in {self.filter_file}")
                filters = customization_json[website_key]["filters"]
                sorted_filters = sorted(filters, key=lambda x: x.get("order", float("inf")))
                return {"filters": sorted_filters, "website_key": website_key}

            # Load the sample filters
            sample_filters = self._load_json_file(self.sample_filter_file)

            # Check if the sample contains the "filters" key
            if "filters" in sample_filters:
                customization_json[website_key] = {"filters": sample_filters["filters"]}
                logger.info(
                    f"Adding new filters for {website_key} based on {self.sample_filter_file}"
                )
            else:
                raise ValueError("No valid 'filters' key found in the sample filter file")

            # Save the updated filters back to search_filters.json
            self._save_json_file(self.filter_file, customization_json)

            # Return the sorted filters
            sorted_filters = sorted(sample_filters["filters"], key=lambda x: x.get("order", float("inf")))
            return {"filters": sorted_filters, "website_key": website_key}
    
    def get_sample_search_filters(self) -> dict:
        """Retrieve sample filters."""
        return self._load_json_file(self.sample_filter_file)
    
    def save_sample_search_filters(self, new_filters: dict):
        """Save sample filters."""
        self._save_json_file(self.sample_filter_file, new_filters)

    def update_search_filters(self, website_key: str, new_filters: dict):
        """Update or add filters for a given website key."""
        website_key = website_key.lower()
        with self._lock:
            # Load the existing filters
            customization_json = self._load_json_file(self.filter_file)

            # Check if the website key exists
            if website_key not in customization_json:
                raise HTTPException(status_code=404, detail=f"No filters found for {website_key}")
            
            # Update the filters for the given website key
            customization_json[website_key] = new_filters
            logger.info(f"Updating filters for {website_key}")

            # Save the updated filters back to search_filters.json
            self._save_json_file(self.filter_file, customization_json)