from datetime import datetime, timezone
from elasticsearch import ConflictError, Elasticsearch, NotFoundError
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from config import <PERSON>LASTIC<PERSON>ARCH_URL, ELASTICSEARCH_USERNAME, ELASTICSEARCH_PASSWORD
import logging
import urllib.parse

class ElasticsearchService:
    def __init__(self):
        self.es = Elasticsearch(
            hosts=[ELASTICSEARCH_URL],
            basic_auth=(ELASTICSEARCH_USERNAME, ELASTICSEARCH_PASSWORD),
            verify_certs=True,
        )

    def query_builder(self, pattern=None, args=None):
        body_query = {}

        if pattern:
            main_should = [
                {
                    "match_phrase": {
                        "name": {
                            "query": pattern,
                            "slop": 2,
                            "boost": 200
                        }
                    }
                },
                {
                    "match_phrase": {
                        "ordernumber": {
                            "query": pattern,
                            "slop": 2,
                            "boost": 200
                        }
                    }
                },
                {
                    "match_phrase_prefix": {
                        "name": {
                            "query": pattern,
                            "boost": 150
                        }
                    }
                },
                {
                    "match_phrase_prefix": {
                        "ordernumber": {
                            "query": pattern,
                            "boost": 150
                        }
                    }
                },
            ]

            body_query = {
                "bool": {
                    "must": [],
                    "filter": [],
                    "should": main_should,
                    "minimum_should_match": 1
                }
            }
        else:
            body_query = {
                "function_score": {
                    "query": {
                        "bool": {
                            "must": [{ "match_all": {} }],
                            "filter": []
                        }
                    },
                    "functions": [
                        {
                            "random_score": {}
                        }
                    ],
                    "boost_mode": "multiply"
                }
            }

        if args:
            for key, value in args.items():
                filter_ = None
                if key == "cat":
                    filter_ = {"match_phrase_prefix": {key: '_'.join(value)}}
                elif key == "price" and isinstance(value, dict) and 'min' in value and 'max' in value:
                    filter_ = {
                        "range": {
                            key: {
                                "gte": value.get('min'),
                                "lte": value.get('max')
                            }
                        }
                    }
                else:
                    filter_ = {"match_phrase_prefix": {key: value}}

                if pattern:
                    body_query["bool"]["filter"].append(filter_)
                else:
                    body_query["function_score"]["query"]["bool"]["filter"].append(filter_)

        return body_query

    def execute_query(self, index, body_query, from_, size, aggSize = 1000):
        try:
            response = self.es.search(
                index=index,
                body={
                    "query": body_query,
                    "sort": [
                        {"name.keyword": {"order": "asc"}}
                    ],
                    "from": from_,
                    "size": size,
                    "aggs": {
                        "min_price": {"min": {"field": "price"}},
                        "max_price": {"max": {"field": "price"}},
                        "categories": {"terms": {"field": "cat.keyword", "size": aggSize}},
                        "product_types": {"terms": {"field": "Produkttyp.keyword", "size": aggSize}},
                        "manufacturers": {"terms": {"field": "vendor.keyword", "size": aggSize}}
                    }
                }
            )
            return response
        except Exception as e:
            logging.error(f"An error occurred while executing query: {e}")
            raise HTTPException(status_code=500, detail="An error occurred while executing the query")
        
    def handle_response(self, response):
        if not response:
            return None

        try:
            data = response.get('hits', {}).get('hits', [])
            filtered_data = [item['_source'] for item in data]
            aggregations = response.get('aggregations', {})

            min_price = aggregations.get('min_price', {}).get('value')
            max_price = aggregations.get('max_price', {}).get('value')
            categories = [item['key'] for item in aggregations.get('categories', {}).get('buckets', [])]
            product_types = [item['key'] for item in aggregations.get('product_types', {}).get('buckets', [])]
            manufacturers = [item['key'] for item in aggregations.get('manufacturers', {}).get('buckets', [])]

            filter_elements = {
                'minPrice': min_price,
                'maxPrice': max_price,
                'categories': categories,
                'productTypes': product_types,
                'manufacturers': manufacturers
            }

            result = {
                'data': filtered_data,
                'totalProducts': response.get('hits', {}).get('total', {}).get('value', 0),
                'filterElements': filter_elements
            }

            return result
        except Exception as e:
            logging.error(f"An error occurred while handling the response: {e}")
            raise HTTPException(status_code=500, detail="An error occurred while handling the response")
        
    def search_products(self, index:str, pattern:str, currentPage=0, pageSize=50, args=None):
        pattern = pattern.strip()
        body_query = self.query_builder(pattern, args)
        response = self.execute_query(index, body_query, currentPage * pageSize, pageSize)
        return self.handle_response(response)


    def normalize_search_term(self, search_term:str):
        return search_term.strip().lower()

    async def update_top_searches(self, search_term, index_name):
        normalized_search_term = self.normalize_search_term(search_term)
        
        # Assuming 'normalized_search_term' could be used as an ID
        doc_id = normalized_search_term  # Or generate an ID based on some logic
        
        body = {
            "script": {
                "source": "ctx._source.count += 1",
                "lang": "painless"
            },
            "upsert": {
                "search_term": normalized_search_term,
                "count": 1,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        }

        try:
            # Added 'id' to the update method
            response = self.es.update(
                index=index_name,
                id=doc_id,  # This line is crucial for fixing the error
                body=body,
                retry_on_conflict=3
            )
            logging.info(f"Successfully updated or inserted search term: {normalized_search_term}")
            return response
        except NotFoundError as e:
            # If the document with the given ID does not exist, you might want to create it first
            logging.error(f"Document not found for ID: {doc_id}. Consider creating it.")
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Document not found for update")
        except ConflictError as e:
            logging.error(f"Failed to update search term due to conflict: {normalized_search_term}, Error: {e}")
            raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="Failed to update due to conflict")
        except Exception as e:
            logging.error(f"An unexpected error occurred: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected error occurred")
        
    def build_diversified_search_query(self, patterns):
        should_clauses = [{"match_phrase_prefix": {"name": pattern}} for pattern in patterns]
        
        filters = {pattern: {"match_phrase_prefix": {"name": pattern}} for pattern in patterns}

        query = {
            "size": 0,
            "query": {
                "bool": {
                    "should": should_clauses,
                    "minimum_should_match": 1
                }
            },
            "aggs": {
                "patterns": {
                    "filters": {
                        "filters": filters
                    },
                    "aggs": {
                        "top_hits": {
                            "top_hits": {
                                "size": 3
                            }
                        }
                    }
                }
            }
        }
        return query

    async def load_initial_products_for_modal(self, top_search_patterns, index_name):
        try:
            query = self.build_diversified_search_query(top_search_patterns)
            response = self.es.search(index=index_name, body=query)
            
            products = []
            seen_names = set()
            product_count = 0

            buckets = response['aggregations']['patterns']['buckets']
            bucket_values = list(buckets.values())

            while product_count < 6:
                added_product = False
                for bucket in bucket_values:
                    if product_count >= 6:
                        break
                    hits = bucket['top_hits']['hits']['hits']
                    for hit in hits:
                        source = hit['_source']
                        if source['name'] not in seen_names:
                            products.append(source)
                            seen_names.add(source['name'])
                            product_count += 1
                            added_product = True
                            break  # Move to the next pattern
                if not added_product:
                    break  # Avoid infinite loop if no new products are found

            return products

        except Exception as error:
            logging.error(f"Error loading initial products for modal: {error}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to load initial products for modal")


    def build_diversified_top_searches_query(self, prefix_size, size, top_term_per_prefix):
        return {
            "size": 0,
            "aggs": {
                "prefixes": {
                    "terms": {
                        "script": {
                            "source": (
                                "String searchTerm = doc['search_term'].value; "
                                "searchTerm = searchTerm.length() > params.prefixSize "
                                "? searchTerm.substring(0, params.prefixSize) : searchTerm;"
                            ),
                            "params": {"prefixSize": prefix_size}
                        },
                        "size": size
                    },
                    "aggs": {
                        "top_searches": {
                            "top_hits": {
                                "size": top_term_per_prefix,
                                "sort": [{"count": {"order": "desc"}}]
                            }
                        }
                    }
                }
            }
        }

    def process_diversified_top_searches_response(self, result):
        """
        Processes the result from Elasticsearch and returns a list of top searches with prefixes.
        """
        buckets = result['aggregations']['prefixes']['buckets']
        return [
            {
                'prefix': bucket['key'],
                'search_term': hit['_source']['search_term'],
                'count': hit['_source']['count'],
                'timestamp': hit['_source']['timestamp']
            }
            for bucket in buckets
            for hit in bucket['top_searches']['hits']['hits']
        ]

    async def get_diversified_top_searches(self, index_name, size=5, prefix_size=3, top_term_per_prefix=1):
        """
        Retrieves diversified top search terms from Elasticsearch.
        """
        try:
            query_body = self.build_diversified_top_searches_query(prefix_size, size, top_term_per_prefix)
            response = self.es.search(index=index_name, body=query_body)
            return self.process_diversified_top_searches_response(response)
        except Exception as error:
            logging.error(f"Failed to get diversified top searches: {error}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to get diversified top searches")