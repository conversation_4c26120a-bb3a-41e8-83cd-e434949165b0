from typing import List, Optional

from fastapi import Depends, HTTPException, status
import validators
from repositories.user_repository import UserRepository
from schemas.WebsiteSchema import WebsiteCreateSchema, WebsiteSchema, WebsiteSettingsSchema, WebsiteSettingsUpdateSchema, WebsiteUpdateSchema
from repositories.website_repository import WebsiteRepository
from utils.helper import generate_md5


def serialize_website(website: WebsiteSchema, depth: int = 0) -> WebsiteSchema:
    if depth < 0:
        return None
    else:
        return WebsiteSchema(
            website_id=website.website_id,
            website_key=website.website_key,
            website_url=website.website_url,
            created_at=website.created_at,
            updated_at=website.updated_at,
            is_active=website.is_active,
            is_paused=website.is_paused,
            owner=website.owner,
            website_settings=serialize_website_settings(
                website.website_settings, depth-1) if website.website_settings else None
        )


def serialize_website_settings(website_settings: WebsiteSettingsSchema, depth: int = 0) -> Optional[WebsiteSettingsSchema]:
    if depth < 0:
        return None
    else:
        return WebsiteSettingsSchema(
            website_settings_id=website_settings.website_settings_id,
            website_id=website_settings.website_id,
            results_container_selector=website_settings.results_container_selector,
            search_input_selector=website_settings.search_input_selector,
            csv_url=website_settings.csv_url,
            created_at=website_settings.created_at,
            updated_at=website_settings.updated_at,
            website=serialize_website(
                website_settings.website, depth-1) if website_settings.website else None
        )


class WebsiteService:
    def __init__(self, website_repository: WebsiteRepository = Depends(WebsiteRepository), user_repository: UserRepository = Depends(UserRepository)):
        self.website_repository = website_repository
        self.user_repository = user_repository

    def get_all_websites(self, page: int = None, per_page: int = None) -> List[WebsiteSchema]:
        try:
            websites = self.website_repository.get_all(page, per_page)
            return [serialize_website(website, depth=1) for website in websites]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching websites: {e}"
            )

    def get_by_id(self, website_id: int) -> WebsiteSchema:
        try:
            website = self.website_repository.get_by_id(website_id)
            return serialize_website(website, depth=1)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching website: {e}"
            )

    def get_by_key(self, website_key: str) -> WebsiteSchema:
        try:
            website = self.website_repository.get_by_key(website_key)
            return serialize_website(website, depth=1)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching website: {e}"
            )

    def get_by_user_id(self, user_id: int) -> List[WebsiteSchema]:
        try:
            websites = self.website_repository.get_by_user_id(user_id)
            return [serialize_website(website, depth=1) for website in websites]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching websites: {e}"
            )
    
    def get_website_by_url(self, website_url: str) -> WebsiteSchema:
        try:
            website = self.website_repository.get_by_website_url(website_url)
            return serialize_website(website, depth=1)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching website: {e}"
            )

    def get_website_settings_by_website_key(self, website_key: int) -> WebsiteSettingsSchema:
        try:
            website = self.website_repository.get_by_key(website_key)
            if not website:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Website not found")
            return serialize_website_settings(website.website_settings, depth=1)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching website settings: {e}"
            )

    def get_active_website(self, user_id: int) -> Optional[WebsiteSchema]:
        try:
            website = self.website_repository.get_active(user_id)
            return serialize_website(website, depth=1) if website else None
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching websites: {e}"
            )
    
    def set_active_website(self, user_id: int, website_id: int) -> WebsiteSchema:
        try:
            website = self.website_repository.set_active(website_id, user_id)
            return serialize_website(website, depth=1)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while setting active website: {e}"
            )

    def create_website(self, website: WebsiteCreateSchema) -> WebsiteSchema:
        # check if user exists
        user = self.user_repository.get_by_id(website.user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
        # check if website already exists
        _website = self.website_repository.get_by_website_url(
            website.website_url)
        if _website:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT, detail="Website already exists")
        # check if website url is valid
        if not validators.domain(website.website_url):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid website domain")
        # csv url is required and must be a valid url
        if not website.csv_url:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="CSV URL is required")
        if not validators.url(website.csv_url) or not website.csv_url.endswith('.csv'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid CSV URL")
        try:
            # create website
            new_website = website.model_dump(
                exclude_unset=True, exclude=["csv_url"])
            new_website['website_key'] = generate_md5(
                new_website['website_url'])
            new_website = self.website_repository.create_website(new_website)
            # create website settings
            new_website_settings = {
                'website_id': new_website.website_id,
                'csv_url': website.csv_url,
                'results_container_selector': '',
                'search_input_selector': ''
            }
            new_website_settings = self.website_repository.create_website_settings(
                new_website_settings)
            return serialize_website(new_website, depth=1)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while creating website: {e}"
            )

    def update_website(self, website_id: int, website_update: WebsiteUpdateSchema) -> WebsiteSchema:
        # check if website exists
        website = self.website_repository.get_by_id(website_id)
        if not website:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Website not found")
        # check if website url is valid
        if not website_update.website_url or not validators.domain(website_update.website_url):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid website domain")
        # if website url is different from old website url, check if it is unique
        if website_update.website_url != website.website_url:
            _website = self.website_repository.get_by_website_url(
                website_update.website_url)
            if _website:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT, detail="Website already exists")
        # if csv_url is provided, check if it is a valid url and is a valid csv file
        if website_update.csv_url and not validators.url(website_update.csv_url) or not website_update.csv_url.endswith('.csv'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid CSV URL")

        try:
            update_data = website_update.model_dump(
                exclude_unset=True, exclude=["csv_url"])
            website = self.website_repository.update_website(
                website_id, update_data)
            # update website settings if csv_url is provided
            if website_update.csv_url:
                website_settings_data = {
                    'csv_url': website_update.csv_url,
                }
                website_settings = self.website_repository.update_website_settings(
                    website.website_id, website_settings_data)
            return serialize_website(website, depth=1)
        except Exception as e:
            if isinstance(e, HTTPException):
                raise e
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while updating website: {e}"
            )

    
    def toggle_website_paused(self, website_id: int) -> WebsiteSchema:
        website = self.website_repository.get_by_id(website_id)
        if not website:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Website not found")
        try:
            update_data = {'is_paused': not website.is_paused}
            website = self.website_repository.update_website(
                website_id, update_data)
            return serialize_website(website, depth=1)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while toggling website pause state: {e}")

    def delete_website(self, website_id: int) -> bool:
        website = self.website_repository.get_by_id(website_id)
        if not website:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Website not found")
        try:
            return self.website_repository.delete_website(website_id)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while deleting website: {e}"
            )

    def update_website_selectors(self, website_settings_id: int, schema: WebsiteSettingsUpdateSchema) -> WebsiteSettingsSchema:
        #  results_container_selector and search_input_selector are required
        if not schema.results_container_selector or not schema.search_input_selector:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST,
                                detail="results_container_selector and search_input_selector are required")
        # check if website settings exists
        website_settings = self.website_repository.get_by_website_settings_id(
            website_settings_id)
        if not website_settings:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Website settings not found")
        try:
            update_data = schema.model_dump(
                exclude_unset=True, exclude=["website_domain"])
            website_settings = self.website_repository.update_website_settings(
                website_settings.website_id, update_data)
            return serialize_website_settings(website_settings)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while updating website selectors: {e}"
            )

    def update_website_settings(self, website_settings_id: int, update_website_settings: WebsiteSettingsUpdateSchema, is_selector_settings=False) -> WebsiteSettingsSchema:
        # check if website settings exists
        website_settings = self.website_repository.get_by_website_settings_id(
            website_settings_id)
        if not website_settings:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Website settings not found")
        # validate website url
        if (not update_website_settings.website_domain or not validators.domain(update_website_settings.website_domain)) and not is_selector_settings:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid website domain")
        # validate csv url
        if (not update_website_settings.csv_url or not validators.url(update_website_settings.csv_url) or not update_website_settings.csv_url.endswith('.csv')) and not is_selector_settings:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid CSV URL")
        try:
            update_data = update_website_settings.model_dump(
                exclude_unset=True, exclude=["website_domain"])
            website_settings = self.website_repository.update_website_settings(
                website_settings.website_id, update_data)
            return serialize_website_settings(website_settings)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while updating website settings: {e}"
            )
