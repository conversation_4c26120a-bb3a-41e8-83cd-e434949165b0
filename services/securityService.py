import json
from typing import Dict, Any

class SecurityService:
    def __init__(self):
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        with open('config/security_config.json', 'r') as config_file:
            return json.load(config_file)

    def _save_config(self) -> None:
        with open('config/security_config.json', 'w') as config_file:
            json.dump(self.config, config_file, indent=4)

    def get_config(self) -> Dict[str, Any]:
        return self.config

    def get_login_rate_limit(self) -> Dict[str, Any]:
        return self.config['login_rate_limit']

    def get_failed_login_attempts_config(self) -> Dict[str, Any]:
        return self.config['failed_login_attempts']

    def is_failed_login_attempts_enabled(self) -> bool:
        return self.config['failed_login_attempts']['enabled']

    def get_cooldown_time_config(self) -> Dict[str, Any]:
        return self.config['failed_login_attempts']['cooldown_time']

    def get_max_attempts_before_limit(self) -> int:
        return self.config['failed_login_attempts']['max_attempts_before_limit']

    def get_rate_limit_duration_hours(self) -> int:
        return self.config['login_rate_limit']['duration_hours']

    def update_login_rate_limit(self, new_config: Dict[str, Any]) -> None:
        if 'max_attempts' in new_config and 'duration_hours' in new_config:
            self.config['login_rate_limit'] = new_config
            self._save_config()
        else:
            raise ValueError("Invalid login rate limit configuration")

    def update_failed_login_attempts_config(self, new_config: Dict[str, Any]) -> None:
        if 'enabled' in new_config and 'cooldown_time' in new_config:
            self.config['failed_login_attempts'] = new_config
            self._save_config()
        else:
            raise ValueError("Invalid failed login attempts configuration")

    def update_failed_login_attempts_enabled(self, enabled: bool) -> None:
        if isinstance(enabled, bool):
            self.config['failed_login_attempts']['enabled'] = enabled
            self._save_config()
        else:
            raise ValueError("Enabled status must be a boolean")

    def update_cooldown_time_config(self, new_config: Dict[str, Any]) -> None:
        if 'type' in new_config and 'max_attempts' in new_config and 'duration_hours' in new_config:
            self.config['failed_login_attempts']['cooldown_time'] = new_config
            self._save_config()
        else:
            raise ValueError("Invalid cooldown time configuration")

    def update_max_attempts_before_limit(self, max_attempts: int) -> None:
        if isinstance(max_attempts, int) and max_attempts > 0:
            self.config['login_rate_limit']['max_attempts'] = max_attempts
            self._save_config()
        else:
            raise ValueError("Max attempts must be a positive integer")

    def update_rate_limit_duration_hours(self, duration_hours: int) -> None:
        if isinstance(duration_hours, int) and duration_hours > 0:
            self.config['login_rate_limit']['duration_hours'] = duration_hours
            self._save_config()
        else:
            raise ValueError("Duration hours must be a positive integer")

    def update_entire_config(self, new_config: Dict[str, Any]) -> None:
        required_keys = ['login_rate_limit', 'failed_login_attempts']
        required_login_rate_limit_keys = ['limit']
        required_failed_login_attempts_keys = ['enabled', 'cooldown_time', 'max_attempts_before_limit']
        required_cooldown_time_keys = ['unit', 'duration']

        if all(key in new_config for key in required_keys):
            if all(key in new_config['login_rate_limit'] for key in required_login_rate_limit_keys):
                if all(key in new_config['failed_login_attempts'] for key in required_failed_login_attempts_keys):
                    if all(key in new_config['failed_login_attempts']['cooldown_time'] for key in required_cooldown_time_keys):
                        self.config = new_config
                        self._save_config()
                    else:
                        raise ValueError("Invalid cooldown_time configuration structure")
                else:
                    raise ValueError("Invalid failed_login_attempts configuration structure")
            else:
                raise ValueError("Invalid login_rate_limit configuration structure")
        else:
            raise ValueError("Invalid configuration structure")
