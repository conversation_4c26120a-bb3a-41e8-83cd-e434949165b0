from enum import Enum

class LogEventTypesEnum(Enum):
    PRODUCT_INDEXATION_STARTED = "PRODUCT_INDEXATION_STARTED"
    PRODUCT_INDEXATION_FINISHED = "PRODUCT_INDEXATION_FINISHED"
    PRODUCT_INDEXATION_FAILED = "PRODUCT_INDEXATION_FAILED"
    DATABASE_READ_ERROR = "DATABASE_READ_ERROR"
    DATABASE_WRITE_ERROR = "DATABASE_WRITE_ERROR"
    ELASTICSEARCH_CONNECTION_ERROR = "ELASTICSEARCH_CONNECTION_ERROR"
    ELASTOCSEARCH_AUTHENTICATION_ERROR = "ELASTICSEARCH_AUTHENTICATION_ERROR"
    ELASTICSEARCH_GENERIC_ERROR = "ELASTICSEARCH_GENERIC_ERROR"
    CSV_FILE_DOWNLOAD_ERROR = "CSV_FILE_DOWNLOAD_ERROR"