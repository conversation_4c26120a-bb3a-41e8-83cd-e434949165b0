from jinja2 import nodes
from jinja2.ext import Extension

class I18nExtension(Extension):
    def __init__(self, environment):
        super().__init__(environment)
        environment.globals['_'] = self._gettext

    def _gettext(self, message):
        return self.environment.globals.get('gettext', lambda x: x)(message)

    def parse(self, parser):
        node = nodes.Call(
            nodes.Name('_', 'load'),
            [parser.parse_expression()],
            [],
            None,
            None
        )
        return node
