function showAlert(message, category = 'success') {
  // Create the alert box
  const alertBox = document.createElement('div');
  alertBox.id = 'alert-box';
  alertBox.className = `fixed top-5 right-5 text-white px-4 py-2 rounded shadow-lg transform translate-x-full opacity-0 transition-all duration-1000 ease-in-out ${category === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
  alertBox.style.zIndex = 1000;
  alertBox.textContent = message;

  // Append the alert box to the body
  document.body.appendChild(alertBox);

  // Slide the alert box in with a fade-in effect
  setTimeout(() => {
    alertBox.classList.remove('translate-x-full', 'opacity-0');
    alertBox.classList.add('translate-x-0', 'opacity-100');
  }, 100); // Delay to allow the alert box to be added to the DOM

  // Hide the alert after 10 seconds (slide-in + time displayed)
  setTimeout(() => {
    // Start the fade-out and slide-out animation
    alertBox.classList.remove('translate-x-0', 'opacity-100');
    alertBox.classList.add('translate-x-full', 'opacity-0');

    // Remove the element after the transition
    setTimeout(() => {
      alertBox.remove();
    }, 1000); // Match the duration of the transition
  }, 10000);
}


function confirmAction(message, callback, ...args) {
  // Create the confirm box
  const confirmBox = document.createElement('div');
  confirmBox.id = 'confirm-box';
  confirmBox.className = 'fixed z-50 inset-0 overflow-y-auto';

  // Create the overlay
  const overlay = document.createElement('div');
  overlay.className = 'flex items-center justify-center min-h-screen px-4 bg-black bg-opacity-50';

  // Create the modal
  const modal = document.createElement('div');
  modal.className = 'bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg max-w-md mx-auto';

  // Create the message
  const confirmMessage = document.createElement('p');
  confirmMessage.className = 'mb-4 text-center text-gray-800 dark:text-gray-200';
  confirmMessage.textContent = message;

  // Create the buttons
  const buttonContainer = document.createElement('div');
  buttonContainer.className = 'flex justify-center space-x-4';

  const cancelButton = document.createElement('button');
  cancelButton.className = 'bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-800 dark:text-gray-200 px-4 py-2 rounded';
  cancelButton.textContent = 'Cancel';

  const confirmButton = document.createElement('button');
  confirmButton.className = 'bg-blue-500 hover:bg-blue-700 text-white px-4 py-2 rounded';
  confirmButton.textContent = 'Confirm';

  // Add event listeners to the buttons
  cancelButton.addEventListener('click', () => {
    confirmBox.remove();
  });

  confirmButton.addEventListener('click', () => {
    confirmBox.remove();
    if (args.length === 0) {
      callback();
    } else {
      callback(...args);
    }
  });

  // Append the message and buttons to the modal
  modal.appendChild(confirmMessage);
  buttonContainer.appendChild(cancelButton);
  buttonContainer.appendChild(confirmButton);
  modal.appendChild(buttonContainer);

  // Append the modal to the overlay
  overlay.appendChild(modal);

  // Append the overlay to the confirm box
  confirmBox.appendChild(overlay);

  // Append the confirm box to the body
  document.body.appendChild(confirmBox);
}


