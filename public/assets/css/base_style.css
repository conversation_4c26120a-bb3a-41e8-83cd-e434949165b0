/* For Webkit Browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
    width: 12px;
}
::-webkit-scrollbar-track {
    background: #000;
}
::-webkit-scrollbar-thumb {
    background: #434343;
    border-radius: 6px;
    border: 3px solid #000;
}

/* For Firefox */
* {
    scrollbar-width: 12px;
    scrollbar-color: #434343 #000;
}


/* select2 */
.dark .select2.select2-container .select2-selection__rendered {
    color: white;
}
.dark .select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
    background-color: #4a5568;
}
.dark .select2-container--default .select2-results__option--selected {
    background-color: #333; /* adjust this color to match your design requirements */
}

