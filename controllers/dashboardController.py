import json
from fastapi import Depends, HTTPException, Request, status
from fastapi.encoders import jsonable_encoder
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse
from background_jobs.product_indexation import Integrator
from config import BASE_DIR
from enums.role_enum import RoleEnum
from schemas.WebsiteSchema import WebsiteSettingsUpdateSchema, WebsiteUpdateSchema
from services.authService import AuthService
from services.productsIndexationService import ProductIndexationService
from services.searchService import SearchService
from services.userServices import UserService
from services.websiteService import WebsiteService
from utils.authUtil import check_role
from utils.exceptionUtil import handle_exception

from config import templates


class DashboardController:
    def __init__(
        self,
        user_service: UserService = Depends(UserService),
        auth_service: AuthService = Depends(AuthService),
        website_service: WebsiteService = Depends(WebsiteService),
        product_indexation_service: ProductIndexationService = Depends(
            ProductIndexationService)
    ) -> None:
        self.user_service = user_service
        self.auth_service = auth_service
        self.website_service = website_service
        self.product_indexation_service = product_indexation_service
        self.search_service = SearchService()

    @check_role()
    async def user_home(self, request: Request):
        try:
            user = self.auth_service.get_current_user(request)
            if user.role.name == RoleEnum.ADMIN.value:
                return RedirectResponse("/admin", status_code=status.HTTP_302_FOUND)
            
            response = self.user_common_data(request)
            if isinstance(response, HTMLResponse):
                return response
            # do additional processing if needed and update the response
            return templates.TemplateResponse("client/home.html", response)
        except Exception as http_exc:
            return handle_exception(request, http_exc)

    @check_role()
    async def switch_website(self, request: Request, selectedWebsite: str):
        try:
            user = self.auth_service.get_current_user(request)
            website = self.website_service.get_website_by_url(selectedWebsite)
            # if not active, set it as active
            if not website.is_active:
                self.website_service.set_active_website(user.user_id, website.website_id)
            response = self.user_common_data(request)
            if isinstance(response, HTMLResponse):
                return response
            # do additional processing if needed and update the response
            return templates.TemplateResponse("client/home.html", response)
        except Exception as http_exc:
            return handle_exception(request, http_exc)

    @check_role(RoleEnum.ADMIN.value)
    async def admin_home(self, request: Request):
        try:
            return templates.TemplateResponse("admin/home.html", {"request": request})
        except Exception as http_exc:
            return handle_exception(request, http_exc)

    @check_role()
    async def data_synchronization(self, request: Request):
        try:
            response = self.user_common_data(request)
            if isinstance(response, HTMLResponse):
                return response
            # product indexation statistics
            statistics = self.product_indexation_service.product_indexation_statistics(
                response['current_website']['website_key'])
            response.update(**statistics)
            return templates.TemplateResponse("client/data/synchronize.html", {"request": request, **response})
        except Exception as http_exc:
            return handle_exception(request, http_exc)

    @check_role()
    async def frontend_integration(self, request: Request):
        try:
            response = self.user_common_data(request)
            if isinstance(response, HTMLResponse):
                return response
            response['code_snippet'] = self.search_engine_code_snippet(
                response['website_settings'])
            return templates.TemplateResponse("client/frontend/integration.html", {"request": request, **response})
        except Exception as http_exc:
            return handle_exception(request, http_exc)

    @check_role()
    async def frontend_customization(self, request: Request):
        try:
            response = self.user_common_data(request)
            if isinstance(response, HTMLResponse):
                return response
            # load filters
            customization_json = self.search_service.get_search_filters(
                response['current_website']['website_key'])
            return templates.TemplateResponse("client/frontend/customization.html", {"request": request, **response, "customization_json": customization_json})
        except Exception as http_exc:
            return handle_exception(request, http_exc)
    
    @check_role(RoleEnum.ADMIN.value)
    async def manage_sample_filters(self, request: Request):
        try:
            sample_filters = self.search_service.get_sample_search_filters()
            sample_filters = sample_filters['filters']
            return templates.TemplateResponse("admin/search_filter/manage.html", {"request": request, 'sample_filters': sample_filters})
        except Exception as http_exc:
            return handle_exception(request, http_exc)
    
    @check_role(RoleEnum.ADMIN.value)
    async def monitor_websites(self, request: Request, user_id: int = None):
        try:
            clients = jsonable_encoder(self.user_service.get_all_users_by_role(RoleEnum.USER.value))
            websites = self.website_service.get_all_websites()
            if user_id:
                websites = [website for website in websites if website.owner.user_id == user_id]
            websites = jsonable_encoder(websites)
            context = {"request": request, "users": clients, "websites": websites, "user_id": user_id}
            return templates.TemplateResponse("admin/clients/manage.html", context)
        except Exception as http_exc:
            import traceback
            traceback.print_exc()
            return handle_exception(request, http_exc)
    
    @check_role(RoleEnum.ADMIN.value)
    async def pause_website(self, request: Request, website_id: int):
        try:
            website = self.website_service.toggle_website_paused(website_id)
            website = jsonable_encoder(website)
            return JSONResponse(status_code=status.HTTP_200_OK, content=website)
        except Exception as http_exc:
            return handle_exception(request, http_exc)
        
    @check_role(RoleEnum.ADMIN.value)
    async def website_settings_view(self, request: Request, website_id: int):
        try:
            website = self.website_service.get_by_id(website_id)
            website = jsonable_encoder(website)
            return JSONResponse(status_code=status.HTTP_200_OK, content=website)
        except Exception as http_exc:
            return handle_exception(request, http_exc)

    @check_role(RoleEnum.ADMIN.value)
    async def website_settings_update(self, request: Request, website_id: int):
        try:
            update_data = await request.json()
            website = self.website_service.get_by_id(website_id)
            if not website:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Website not found for specified id")
            update_schema = WebsiteSettingsUpdateSchema(**update_data)
            update_schema.website_domain = website.website_url
            self.website_service.update_website_selectors(website.website_settings.website_settings_id, update_schema)
            self.website_service.update_website_settings(website.website_settings.website_settings_id, update_schema, is_selector_settings=True)
            return JSONResponse(status_code=status.HTTP_200_OK, content={"message": "Settings updated successfully"})
        except Exception as http_exc:
            return handle_exception(request, http_exc)
    
    @check_role(RoleEnum.ADMIN.value)
    async def manage_sample_filters_submit(self, request: Request):
        try:
            update_data = await request.json()
            if not update_data or not len(update_data.keys()):
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid request")
            self.search_service.save_sample_search_filters(update_data)
            return JSONResponse(status_code=status.HTTP_200_OK, content={"message": "Filters updated successfully"})
        except Exception as http_exc:
            return handle_exception(request, http_exc)
        
    @check_role()
    async def update_filter_order(self, request: Request):
        try:
            update_data = await request.json()
            customization_json = self.search_service.get_search_filters(
                update_data['website_key'])
            field_values = [filter['field']
                            for filter in customization_json['filters']]
            # validate incoming fields
            for filter in update_data['filters']:
                if filter['field'] not in field_values:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid filter id: {filter['field']}")
            # update the order in the json file
            for filter in update_data['filters']:
                for index, existing_filter in enumerate(customization_json['filters']):
                    if existing_filter['field'] == filter['field']:
                        customization_json['filters'][index]['order'] = filter['order']
            # sort the json file by order
            customization_json['filters'] = sorted(
                customization_json['filters'], key=lambda k: k['order'])
            # save the json file
            self.search_service.update_search_filters(
                update_data['website_key'], customization_json)
            return customization_json
        except Exception as http_exc:
            return handle_exception(request, http_exc)

    @check_role()
    async def update_filter_display_name(self, request: Request):
        try:
            update_data = await request.json()
            customization_json = self.search_service.get_search_filters(
                update_data['website_key'])
            for index, existing_filter in enumerate(customization_json['filters']):
                if existing_filter['field'] == update_data['filter_field']:
                    customization_json['filters'][index]['displayName'] = update_data['new_display_name']
            # save the json file
            self.search_service.update_search_filters(
                update_data['website_key'], customization_json)
            return customization_json
        except Exception as http_exc:
            return handle_exception(request, http_exc)

    async def get_search_filters(self, request: Request):
        try:
            # get website key as query param
            website_key = request.query_params.get('website_key')
            # load the json file
            return self.search_service.get_search_filters(website_key)
        except Exception as http_exc:
            return handle_exception(request, http_exc)

    @check_role()
    async def search_terms_report(self, request: Request):
        try:
            response = self.user_common_data(request)
            if isinstance(response, HTMLResponse):
                return response
            return templates.TemplateResponse("client/reports/search_terms.html", {"request": request, **response})
        except Exception as http_exc:
            return handle_exception(request, http_exc)

    @check_role()
    async def search_terms_listing(self, request: Request, websiteKey: str, draw: int, start: int, length: int, startDate: str, endDate: str, sort_by: list, search: str):
        try:
            integrator = Integrator()
            result = integrator.get_search_terms(
                websiteKey, start, length, startDate, endDate, sort_by, search)
            if result:
                response = {
                    "draw": draw,
                    "recordsTotal": result['total_hits'],
                    "recordsFiltered": result['total_hits'],
                    "data": result['search_terms']
                }
                return response
            return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content={"message": "An error occurred while fetching search terms"})
        except Exception as http_exc:
            return handle_exception(request, http_exc)

    def user_common_data(self, request: Request, selected_website: str = None):
        try:
            user = self.auth_service.is_authenticated(request)
            websites = jsonable_encoder(
                self.website_service.get_by_user_id(user.user_id))
            if not websites:
                return self._create_response(request, user, websites)

            current_website = self.website_service.get_active_website(user.user_id)
            current_website = jsonable_encoder(current_website) if current_website else None
            # if no website is selected, select the first one and update the database
            if not current_website:
                current_website = websites[0]
                self.website_service.set_active_website(user.user_id, current_website['website_id'])
            website_settings = self._get_website_settings(current_website)

            return self._create_response(request, user, websites, current_website, website_settings)

        except Exception as exc:
            raise exc

    def _create_response(self, request, user, websites, current_website=None, website_settings=None):
        return {
            "request": request,
            "websites": websites,
            "current_website": current_website,
            "user": user,
            "website_settings": website_settings
        }

    def _get_website_settings(self, website):
        if not website:
            return None
        settings = website.get('website_settings')
        if settings:
            settings['website'] = website
        return settings


    def search_engine_code_snippet(self, website_settings: dict):
        return self.user_service.code_snippet(website_settings)
