import traceback
from typing import List, Optional
from fastapi import HTTPException, status
from sqlalchemy.orm import Session
from models.models import ProductsIndexation, Website
from schemas.ProductIndexationSchema import ProductIndexationSchema, ProductIndexationCreateSchema

class ProductIndexationController:
    @classmethod
    def getAll(cls, db: Session, page: int = None, per_page: int = None) -> List[ProductIndexationSchema]:
        try:
            product_indexations = ProductsIndexation.get_all(db, page, per_page)
            return [ProductIndexationSchema.model_validate(product_indexation) for product_indexation in product_indexations]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching product indexations: {e}"
            )
    
    @classmethod
    def getById(cls, db: Session, product_indexation_id: int) -> ProductIndexationSchema:
        product_indexation = ProductsIndexation.get_by_id(db, product_indexation_id)
        if not product_indexation:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Product indexation not found")
        try:
            return ProductIndexationSchema.model_validate(product_indexation)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching product indexation: {e}"
            )

    @classmethod
    def getByWebsiteId(cls, db: Session, website_id: int) -> List[ProductIndexationSchema]:
        # check if website exists
        website = Website.get_by_id(db, website_id)
        if not website:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Website not found")
        # get product indexations by website_id
        try:
            product_indexations = ProductsIndexation.get_by_website_id(db, website_id)
            return [ProductIndexationSchema.model_validate(product_indexation) for product_indexation in product_indexations]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching product indexations: {e}"
            )
    
    @classmethod
    def getLastInserted(cls, db: Session, website_id: int) -> Optional[ProductIndexationSchema]:
        # check if website exists
        website = Website.get_by_id(db, website_id)
        if not website:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Website not found")
        # get product indexations by website_id
        try:
            product_indexation = ProductsIndexation.get_last_inserted_by_website_id(db, website_id)
            return ProductIndexationSchema.model_validate(product_indexation) if product_indexation else None
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching product indexation: {e}"
            )
    
    @classmethod
    def create(cls, db: Session, product_indexation_schema: ProductIndexationCreateSchema) -> ProductIndexationSchema:
        # check if website exists
        website = Website.get_by_id(db, product_indexation_schema.website_id)
        if not website:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Website not found")
        try:
            new_product_indexation = ProductsIndexation(**product_indexation_schema.model_dump(exclude_unset=True))
            new_product_indexation.website = website
            new_product_indexation = ProductsIndexation.create(db, new_product_indexation)
            return ProductIndexationSchema.model_validate(new_product_indexation)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while creating product indexation: {e}"
            )
    
    @classmethod
    def delete(cls, db: Session, product_indexation_id: int) -> bool:
        product_indexation = ProductsIndexation.get_by_id(db, product_indexation_id)
        if not product_indexation:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Product indexation not found")
        try:
            ProductsIndexation.delete(db, product_indexation)
            return True
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while deleting product indexation: {e}"
            )