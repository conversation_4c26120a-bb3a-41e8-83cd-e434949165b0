from typing import List, Optional
from fastapi import Depends, HTTPException, Request, status, Form
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.encoders import jsonable_encoder
from services.authService import AuthService
from services.schedulerService import SchedulerService
from services.websiteService import WebsiteService
from services.scheduledTaskService import ScheduledTaskService
from services.taskExecutionLogService import TaskExecutionLogService
from schemas.ScheduledTaskSchema import ScheduledTaskCreateSchema, ScheduledTaskUpdateSchema
from schemas.TaskExecutionLogSchema import TaskExecutionLogCreateSchema
from datetime import datetime
from enums.role_enum import RoleEnum
from enums.task_schedule import IndexingType, ScheduleFrequency
from functools import wraps
from dependencies.DependencyContainer import dependency_container

from config import templates

def admin_required(func):
    @wraps(func)
    async def wrapper(self, request: Request, *args, **kwargs):
        try:
            user = self.auth_service.is_authenticated(request)
            if user.role.name != RoleEnum.ADMIN.value:
                raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Unauthorized")
        except Exception as exc:
            return self.handle_exception(request, exc)  # Use the unified handler
        return await func(self, request, *args, **kwargs)
    return wrapper

class BaseController:
    def __init__(self, auth_service: AuthService = Depends(AuthService)):
        self.auth_service = auth_service

    def _handle_http_exception(self, request: Request, exc: HTTPException):
        if request.headers.get("accept") == "application/json":
            return JSONResponse(
                status_code=exc.status_code,
                content={"detail": exc.detail}
            )
        
        if exc.status_code == status.HTTP_401_UNAUTHORIZED:
            return templates.TemplateResponse("account/login.html", {"request": request})
        elif exc.status_code == status.HTTP_403_FORBIDDEN:
            return templates.TemplateResponse("errors/error403.html", {"request": request})
        return templates.TemplateResponse("errors/error500.html", {"request": request})

    def _handle_general_exception(self, request: Request, exc: Exception):
        if request.headers.get("accept") == "application/json":
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"detail": "An unexpected error occurred"}
            )
        return templates.TemplateResponse("errors/error500.html", {"request": request})
    
    def handle_exception(self, request: Request, exc: Exception):
        if isinstance(exc, HTTPException):
            return self._handle_http_exception(request, exc)
        return self._handle_general_exception(request, exc)

    async def _handle_request(self, request: Request, action):
        try:
            response = await action()
            if request.headers.get("accept") == "application/json":
                if isinstance(response, (dict, list)):
                    return JSONResponse(content=jsonable_encoder(response))
                elif isinstance(response, JSONResponse):
                    return response
            return response
        except Exception as exc:
            import traceback
            traceback.print_exc()
            return self.handle_exception(request, exc)

class SchedulerController(BaseController):
    def __init__(
        self,
        auth_service: AuthService = Depends(AuthService),
        website_service: WebsiteService = Depends(WebsiteService),
        scheduled_task_service: ScheduledTaskService = Depends(ScheduledTaskService),
        task_execution_log_service: TaskExecutionLogService = Depends(TaskExecutionLogService),
        scheduler_service: SchedulerService = Depends(SchedulerService)
    ):
        super().__init__(auth_service)
        self.website_service = website_service
        self.scheduled_task_service = scheduled_task_service
        self.task_execution_log_service = task_execution_log_service
        self.scheduler_service = scheduler_service

    @admin_required
    async def create_task_form(self, request: Request):
        async def action():
            scheduleFrequency = [freq.value for freq in ScheduleFrequency]
            indexingType = [index.value for index in IndexingType]
            websites = self.website_service.get_all_websites()
            websites = jsonable_encoder(websites)
            return templates.TemplateResponse("admin/scheduler/create_task.html", {"request": request, "scheduleFrequency": scheduleFrequency, "indexingType": indexingType, "websites": websites})
        return await self._handle_request(request, action)

    @admin_required
    async def create_task_submit(self, request: Request):
        async def action():
            form_data = await request.form()
            task_data = ScheduledTaskCreateSchema(**form_data)
            task = self.scheduled_task_service.create_task(task_data)
            self.scheduler_service.schedule_and_update_task(task=task)
            return {"message": "Task created successfully", "task_id": task.task_id}
        return await self._handle_request(request, action)
    
    @admin_required
    async def reschedule_task(self, request: Request, task_id: int):
        async def action():
            form_data = await request.json()
            result = self.scheduler_service.reschedule_task(task_id=task_id, task_data=form_data)
            message = "Task rescheduled successfully" if result else "Task rescheduling failed"
            if not result:
                return JSONResponse(content=message, status_code=status.HTTP_400_BAD_REQUEST)
            task = jsonable_encoder(self.scheduled_task_service.get_task_by_id(task_id))
            return {"message": message, "task": task}
        return await self._handle_request(request, action)
    
    @admin_required
    async def deactivate_task(self, request: Request, task_id: int):
        async def action():
            result = self.scheduler_service.deactivate_task(task_id)
            message = "Task deactivated successfully" if result else "Task deactivation failed"
            return {"message": message}
        return await self._handle_request(request, action)
    
    @admin_required
    async def activate_task(self, request: Request, task_id: int):
        async def action():
            update_data = ScheduledTaskUpdateSchema(is_active=True)
            result = self.scheduled_task_service.update_task(task_id, update_data)
            result = jsonable_encoder(result)
            return {"task": result}
        return await self._handle_request(request, action)
    
    @admin_required
    async def delete_task(self, request: Request, task_id: int):
        async def action():
            self.scheduler_service.unschedule_task(task_id)
            result = self.scheduled_task_service.delete_task(task_id)
            message = "Task deleted successfully" if result else "Task deletion failed"
            return {"message": message}
        return await self._handle_request(request, action)

    @admin_required
    async def refresh_scheduler(self, request: Request):
        async def action():
            self.scheduler_service.refresh_scheduler()
            return {"message": "Tasks refreshed successfully"}
        return await self._handle_request(request, action)

    @admin_required
    async def get_all_tasks(self, request: Request, page: int = None, per_page: int = None):
        async def action():
            tasks = self.scheduled_task_service.get_all_tasks(page, per_page)
            return jsonable_encoder(tasks)
        return await self._handle_request(request, action)

    @admin_required
    async def tasks_monitor(self, request: Request, page: int = None, per_page: int = None):
        async def action():
            tasks = self.scheduled_task_service.get_all_tasks(page, per_page)
            return templates.TemplateResponse("admin/scheduler/tasks_monitor.html", {"request": request, "tasks": jsonable_encoder(tasks)})
        return await self._handle_request(request, action)

    @admin_required
    async def get_task_by_id(self, request: Request, task_id: int):
        async def action():
            task = self.scheduled_task_service.get_task_by_id(task_id)
            return jsonable_encoder(task)
        return await self._handle_request(request, action)

    @admin_required
    async def update_task(self, request: Request, task_id: int):
        async def action():
            form_data = await request.form()
            task_data = ScheduledTaskUpdateSchema(**form_data)
            updated_task = await self.scheduled_task_service.update_task(task_id, task_data)
            return JSONResponse(content={"message": "Task updated successfully", "task": jsonable_encoder(updated_task)})
        return await self._handle_request(request, action)

    @admin_required
    async def get_logs_for_all_websites(self, request: Request, page: int = 1, per_page: int = 10):
        async def action():
            logs = await self.task_execution_log_service.get_logs_for_all_websites(page, per_page)
            return templates.TemplateResponse("admin/scheduler/view_logs.html", {"request": request, "logs": jsonable_encoder(logs)})
        return await self._handle_request(request, action)

    @admin_required
    async def search_logs(self, request: Request):
        async def action():
            return templates.TemplateResponse("admin/scheduler/search_logs.html", {"request": request})
        return await self._handle_request(request, action)

    @admin_required
    async def search_logs_submit(self, request: Request, start_date: str = Form(...), end_date: str = Form(...), website_id: Optional[int] = Form(None)):
        async def action():
            logs = await self.task_execution_log_service.search_logs(start_date, end_date, website_id)
            return JSONResponse(content={"logs": jsonable_encoder(logs)})
        return await self._handle_request(request, action)

    @admin_required
    async def get_log_by_id(self, request: Request, log_id: int):
        async def action():
            log = await self.task_execution_log_service.get_log_by_id(log_id)
            return templates.TemplateResponse("admin/scheduler/view_log.html", {"request": request, "log": jsonable_encoder(log)})
        return await self._handle_request(request, action)