
from fastapi import Depends, Request
from fastapi.encoders import jsonable_encoder
from typing import List
from schemas.LogSchema import LogCreateSchema, LogSchema
from enums.role_enum import RoleEnum
from services.authService import AuthService
from services.logService import LogService
from utils.authUtil import check_role
from utils.exceptionUtil import handle_exception

from config import templates

class LogController:

    def __init__(
            self, 
            log_service: LogService = Depends(LogService),
            auth_service: AuthService = Depends(AuthService),
    ) -> None:
        self.log_service = log_service
        self.auth_service = auth_service
    
    @check_role(RoleEnum.ADMIN.value)
    async def get_logs(self, request: Request, draw: int, start: int = None, length: int = None) -> List[LogSchema]:
        try:
            page = (start // length) + 1 if start is not None and length is not None else 1
            result, count = self.log_service.get_all_logs(page=page, per_page=length)
            result = jsonable_encoder(result)

            response = {
                "draw": draw,
                "recordsTotal": count,
                "recordsFiltered": count,
                "data": result
            }
            return response
        except Exception as e:
            import traceback
            traceback.print_exc()
            return handle_exception(request, e)
    
    @check_role(RoleEnum.ADMIN.value)
    async def get_by_id(self, request: Request, log_id: int) -> LogSchema:
        try:
            log = self.log_service.get_log_by_id(log_id)
            log = jsonable_encoder(log)
            return log
        except Exception as e:
            return handle_exception(request, e)

    @check_role(RoleEnum.ADMIN.value)    
    async def create(self, request: Request) -> LogSchema:
        try:
            form_data = await request.form()
            log_schema = LogCreateSchema(**form_data)
            log = self.log_service.create_log(log_schema)
            log = jsonable_encoder(log)
            response = {"request": request, "log": log, "message": "Log created successfully"}
            return templates.TemplateResponse("log/create.html", response)
        except Exception as e:
            return handle_exception(request, e)
    
    @check_role(RoleEnum.ADMIN.value)
    async def update(self, request: Request, log_id: int, log_update_schema: LogCreateSchema) -> LogSchema:
        try:
            log = self.log_service.update_log(log_id, log_update_schema)
            return LogSchema.model_validate(log)
        except Exception as e:
            return handle_exception(request, e)

    @check_role(RoleEnum.ADMIN.value)
    async def delete(self, request: Request, log_id: int) -> bool:
        try:
            response = self.log_service.delete_log(log_id)
            return response
        except Exception as e:
            return handle_exception(request, e)

    @check_role(RoleEnum.ADMIN.value)
    def refresh_logs(self, request: Request):
        try:
            logs = self.log_service.get_all_logs()
            logs = jsonable_encoder(logs)
            return templates.TemplateResponse("log/partials/log_table.html", {"request": request, "logs": logs})
        except Exception as http_exc:
            return handle_exception(request, http_exc)
