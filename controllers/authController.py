from datetime import <PERSON><PERSON><PERSON>, datetime
import re
from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, Request, status, Response
from fastapi.responses import RedirectResponse
from fastapi.security import OA<PERSON>2P<PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from jose import JW<PERSON>rror, jwt
from typing import Optional
from sqlalchemy.orm import Session
from pydantic import BaseModel
from models.models import User

from config import COOKIE_NAME, SECRET_KEY, ALGORITH<PERSON>, ACCESS_TOKEN_EXPIRE_MINUTES
from enums.role_enum import RoleEnum

app = FastAPI()

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

credentials_exception = HTTPException(
    status_code=status.HTTP_401_UNAUTHORIZED,
    detail="Could not validate credentials",
    headers={
        "WWW-Authenticate": 'Bearer, Cookie realm="Access to the protected resource", charset="UTF-8"'
    },
)

class TokenData(BaseModel):
    email: Optional[str] = None

def decode_token(token: str):
    """Decodes the JW<PERSON> token and returns the user data."""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception
        token_data = TokenData(email=email)
        return token_data
    except JWTError:
        raise credentials_exception

def is_authenticated(Request: Request, db: Session):
    token = Request.cookies.get(COOKIE_NAME)
    if not token:
        raise credentials_exception
    # Strip the 'Bearer ' prefix from the token if present
    token = re.sub(r'^Bearer\s*', '', token)
    token_data = decode_token(token)
    # get and return the user
    user = User.get_by_email(db, email=token_data.email)
    if not user:
        raise credentials_exception
    return user

def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def login_for_access_token(db: Session, form_data: OAuth2PasswordRequestForm = Depends()):
    email: str = form_data.username
    password: str = form_data.password

    user: User = User.get_by_email(db, email)
    if not user or not User.verify_password(password, user.password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": email}, expires_delta=access_token_expires
    )

    # Convert minutes to seconds for the cookie expiration
    max_age_seconds = ACCESS_TOKEN_EXPIRE_MINUTES * 60

    # Instead of returning a JSONResponse, redirect the user after login
    url  = user.role.name == RoleEnum.USER.value and "/" or "/admin"
    redirect_response = RedirectResponse(url=url, status_code=status.HTTP_302_FOUND)
    redirect_response.set_cookie(
        key=COOKIE_NAME,
        value=f"Bearer {access_token}",
        httponly=True,
        max_age=max_age_seconds,
        expires=max_age_seconds,
    )
    return redirect_response