from fastapi import HTTPException, status
from models.models import Role, Permission, RolePermission
from typing import List
from sqlalchemy.orm import Session
from schemas.UserSchema import RoleSchema, RolePermissionSchema

class RoleController:
    @classmethod
    def get_all(cls, db: Session) -> List[RoleSchema]:
        try:
            roles = Role.get_all(db)
            return [RoleSchema.model_validate(role) for role in roles]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching roles: {e}"
            )
    
    @classmethod
    def get_by_id(cls, db: Session, role_id: int) -> RolePermissionSchema:
        try:
            role = Role.get_by_id(db, role_id)
            if not role:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Role not found")
            return RoleSchema.model_validate(role)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching role: {e}"
            )

    @classmethod
    def get_by_name(cls, db: Session, name: str) -> RoleSchema:
        try:
            role = Role.get_by_name(db, name)
            if not role:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Role not found")
            return RoleSchema.model_validate(role)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching role: {e}"
            )

    @classmethod
    def create(cls, db: Session, role_name: str) -> RoleSchema:
        try:
            # check if role exists
            existing_role = Role.get_by_name(db, role_name)
            if existing_role:
                raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="Role already exists")
            # create role
            new_role = Role(name=role_name)
            new_role = Role.create(db, new_role)
            return RoleSchema.model_validate(new_role)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while creating role: {e}"
            )
    
    @classmethod
    def delete(cls, db: Session, role_id: int) -> bool:
        try:
            role = Role.get_by_id(db, role_id)
            if not role:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Role not found")
            return Role.delete(db, role_id)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while deleting role: {e}"
            )
        
    # Junction methods for RolePermission
    @classmethod
    def create_junction(cls, db: Session, role_id: int, permission_id: int) -> RolePermission:
        try:
            # check if role exists
            role = Role.get_by_id(db, role_id)
            if not role:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Role not found")
            # check if permission exists
            permission = Permission.get_by_id(db, permission_id)
            if not permission:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Permission not found")
            # create junction
            new_junction = RolePermission(role_id=role_id, permission_id=permission_id)
            new_junction = RolePermission.create(db, new_junction)
            return RolePermissionSchema.model_validate(new_junction)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while creating junction: {e}"
            )
    
    @classmethod
    def delete_junction(cls, db: Session, role_id: int, permission_id: int) -> bool:
        try:
            junction = RolePermission.get_by_role_id_and_permission_id(db, role_id, permission_id)
            if not junction:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Junction not found")
            return RolePermission.delete(db, role_id, permission_id)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while deleting junction: {e}"
            )
    
    @classmethod
    def get_all_junctions_by_role_id(cls, db: Session, role_id: int) -> List[RolePermissionSchema]:
        try:
            junctions = RolePermission.get_all_by_role_id(db, role_id)
            return [RolePermissionSchema.model_validate(junction) for junction in junctions]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching junctions: {e}"
            )