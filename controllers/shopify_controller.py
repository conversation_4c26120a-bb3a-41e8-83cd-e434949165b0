import logging
from fastapi import HTTPException
from services.shopifyElasticsearchService import ShopifyElasticsearchService

class ShopifyController:
    """
    Shopify-specific controller for handling product variant search operations.
    Follows the same interface pattern as the ToolBrothers ElasticsearchController.
    """

    def __init__(self):
        # Configure the logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # Initialize Shopify ElasticsearchService
        self.shopify_service = ShopifyElasticsearchService()

    async def search_products(self, index: str, pattern: str, current_page: int, page_size: int, args: dict):
        """
        Search Shopify product variants.

        Args:
            index: Elasticsearch index name
            pattern: Search query (barcode, SKU, product name, etc.)
            current_page: Page number (0-based)
            page_size: Number of results per page
            args: Dictionary of filters (vendor, product_type, price, etc.)
        """
        try:
            self.logger.info(f"Shopify search: pattern='{pattern}', index='{index}'")

            result = self.shopify_service.search_products(
                index=index,
                pattern=pattern,
                current_page=current_page,
                page_size=page_size,
                args=args
            )

            return result

        except Exception as e:
            self.logger.error(f"Shopify search error: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Shopify search failed: {str(e)}"
            )
