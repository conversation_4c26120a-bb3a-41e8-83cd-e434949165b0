from fastapi import HTTPException, status
from models.models import Permission
from typing import List
from sqlalchemy.orm import Session
from schemas.UserSchema import PermissionSchema

class PermissionController:
    @classmethod
    def get_all(cls, db: Session, page: int = None, per_page: int = None) -> List[PermissionSchema]:
        try:
            permissions = Permission.get_all(db, page, per_page)
            return [PermissionSchema.model_validate(permission) for permission in permissions]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching permissions: {e}"
            )

    @classmethod
    def get_by_id(cls, db: Session, permission_id: int) -> PermissionSchema:
        try:
            permission = Permission.get_by_id(db, permission_id)
            if not permission:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Permission not found")
            return PermissionSchema.model_validate(permission)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching permission: {e}"
            )
    
    @classmethod
    def get_by_name(cls, db: Session, name: str) -> PermissionSchema:
        try:
            permission = Permission.get_by_name(db, name)
            if not permission:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Permission not found")
            return PermissionSchema.model_validate(permission)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching permission: {e}"
            )

    @classmethod
    def create(cls, db: Session, permission_name: str) -> PermissionSchema:
        try:
            # check if permission exists
            existing_permission = Permission.get_by_name(db, permission_name)
            if existing_permission:
                raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="Permission already exists")
            # create permission
            new_permission = Permission(name=permission_name)
            new_permission = Permission.create(db, new_permission)
            return PermissionSchema.model_validate(new_permission)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while creating permission: {e}"
            )

    @classmethod
    def delete(cls, db: Session, permission_id: int) -> bool:
        try:
            permission = Permission.get_by_id(db, permission_id)
            if not permission:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Permission not found")
            return Permission.delete(db, permission_id)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while deleting permission: {e}"
            )