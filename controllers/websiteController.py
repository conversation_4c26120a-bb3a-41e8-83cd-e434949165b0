from fastapi import Depends, HTTPException, Request, status
from fastapi.encoders import jsonable_encoder
import cors
from enums.role_enum import RoleEnum
from typing import List
from schemas.WebsiteSchema import WebsiteCreateSchema, WebsiteSettingsUpdateSchema, WebsiteUpdateSchema
from services.userServices import UserService
from utils.authUtil import check_role
from utils.exceptionUtil import handle_exception
from services.websiteService import WebsiteService
from services.authService import AuthService

from config import templates

class WebsiteController:

    def __init__(self, website_service: WebsiteService = Depends(WebsiteService), auth_service: AuthService = Depends(AuthService), user_service: UserService = Depends(UserService)) -> None:
        self.website_service = website_service
        self.auth_service = auth_service
        self.user_service = user_service
    
    @check_role(RoleEnum.ADMIN.value)
    async def load_website_registration_form(self, request: Request):
        try:
            # get the list of clients
            clients = self.user_service.get_all_users_by_role(RoleEnum.USER.value)
            clients = jsonable_encoder(clients)
            return templates.TemplateResponse("websites/register.html", {"request": request, "clients": clients})
        except HTTPException as http_exc:
            return handle_exception(request, http_exc)

    @check_role(RoleEnum.ADMIN.value)
    async def websites_review(self, request: Request):
        try:
            websites = self.website_service.get_all_websites()
            websites = jsonable_encoder(websites)
            return templates.TemplateResponse("websites/review.html", {"request": request, "websites": websites})
        except HTTPException as http_exc:
            return handle_exception(request, http_exc)

    @check_role(RoleEnum.ADMIN.value)
    async def create_website(self, request: Request):
        try:
            form_data = await request.form()
            form_data = WebsiteCreateSchema(**form_data)
            error_form_data = jsonable_encoder(form_data)
            # get the list of clients
            clients = self.user_service.get_all_users_by_role(RoleEnum.USER.value)
            clients = jsonable_encoder(clients)
            website = self.website_service.create_website(form_data)
            success_massage = "Website created successfully"
            # update cors allowed origins
            cors.update_allowed_origins({form_data.website_url})
            return templates.TemplateResponse("websites/register.html", {"request": request, "clients": clients, "message": success_massage, "form_data": error_form_data})
        except Exception as http_exc:
            return handle_exception(request, http_exc)

    @check_role()    
    async def update(self, request: Request, website_id: int):
        try:
            form_data = await request.form()
            form_data = WebsiteUpdateSchema(**form_data)
            return self.website_service.update_website(website_id, form_data)
        except Exception as e:
            return handle_exception(request, e)

    @check_role(RoleEnum.ADMIN.value)
    async def delete_website(self, request: Request, website_id: int):
        try:
            deleted = self.website_service.delete_website(website_id)
            if not deleted:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Website not found")
            return {"message": "Website deleted successfully"}
        except HTTPException as http_exc:
            return handle_exception(request, http_exc)


    # Website Settings
    
    @check_role()
    async def update_website_settings(self, request: Request, website_settings_id: int):
        try:
            form_data = await request.form()
            form_data = WebsiteSettingsUpdateSchema(**form_data)
            # update the website
            website_settings = self.website_service.update_website_settings(website_settings_id, form_data)
            return website_settings
        except HTTPException as http_exc:
            return handle_exception(request, http_exc)
        
    # update website selectors settings
    @check_role()
    async def update_website_selectors(self, request: Request, website_settings_id: int):
        try:
            form_data = await request.form()
            form_data = WebsiteSettingsUpdateSchema(**form_data)
            website_settings = self.website_service.update_website_settings(website_settings_id, form_data, True)
            return website_settings
        except Exception as e:
            return handle_exception(request, e)