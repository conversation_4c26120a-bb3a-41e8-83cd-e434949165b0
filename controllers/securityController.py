import json
from fastapi import Depends, Request
from fastapi.encoders import jsonable_encoder
from typing import Dict, Any
from enums.role_enum import RoleEnum
from services.authService import AuthService
from services.securityService import SecurityService
from utils.authUtil import check_role
from utils.exceptionUtil import handle_exception

from config import templates

class SecurityController:

    def __init__(
            self, 
            security_service: SecurityService = Depends(SecurityService),
            auth_service: AuthService = Depends(AuthService),
    ) -> None:
        self.security_service = security_service
        self.auth_service = auth_service
    
    @check_role(RoleEnum.ADMIN.value)
    async def index(self, request: Request) -> Dict[str, Any]:
        try:
            config = self.security_service.get_config()
            return templates.TemplateResponse("admin/security/index.html", {"request": request, "config": config})
        except Exception as e:
            import traceback
            traceback.print_exc()
            return handle_exception(request, e)

    @check_role(RoleEnum.ADMIN.value)
    async def get_security_config(self, request: Request) -> Dict[str, Any]:
        try:
            config = self.security_service.get_config()
            return jsonable_encoder(config)
        except Exception as e:
            return handle_exception(request, e)

    @check_role(RoleEnum.ADMIN.value)
    async def get_login_rate_limit(self, request: Request) -> Dict[str, Any]:
        try:
            rate_limit = self.security_service.get_login_rate_limit()
            return jsonable_encoder(rate_limit)
        except Exception as e:
            return handle_exception(request, e)

    @check_role(RoleEnum.ADMIN.value)
    async def update_login_rate_limit(self, request: Request) -> Dict[str, str]:
        try:
            form_data = await request.form()
            new_config = {
                "max_attempts": int(form_data["max_attempts"]),
                "duration_hours": int(form_data["duration_hours"])
            }
            self.security_service.update_login_rate_limit(new_config)
            return {"message": "Login rate limit updated successfully"}
        except Exception as e:
            return handle_exception(request, e)

    @check_role(RoleEnum.ADMIN.value)
    async def get_failed_login_attempts_config(self, request: Request) -> Dict[str, Any]:
        try:
            config = self.security_service.get_failed_login_attempts_config()
            return jsonable_encoder(config)
        except Exception as e:
            return handle_exception(request, e)

    @check_role(RoleEnum.ADMIN.value)
    async def update_failed_login_attempts_config(self, request: Request) -> Dict[str, str]:
        try:
            form_data = await request.form()
            new_config = {
                "enabled": form_data["enabled"].lower() == "true",
                "cooldown_time": {
                    "type": form_data["cooldown_type"],
                    "max_attempts": int(form_data["cooldown_max_attempts"]),
                    "duration_hours": int(form_data["cooldown_duration_hours"])
                }
            }
            self.security_service.update_failed_login_attempts_config(new_config)
            return {"message": "Failed login attempts configuration updated successfully"}
        except Exception as e:
            return handle_exception(request, e)

    @check_role(RoleEnum.ADMIN.value)
    async def update_failed_login_attempts_enabled(self, request: Request) -> Dict[str, str]:
        try:
            form_data = await request.form()
            enabled = form_data["enabled"].lower() == "true"
            self.security_service.update_failed_login_attempts_enabled(enabled)
            return {"message": "Failed login attempts enabled status updated successfully"}
        except Exception as e:
            return handle_exception(request, e)

    @check_role(RoleEnum.ADMIN.value)
    async def get_cooldown_time_config(self, request: Request) -> Dict[str, Any]:
        try:
            config = self.security_service.get_cooldown_time_config()
            return jsonable_encoder(config)
        except Exception as e:
            return handle_exception(request, e)

    @check_role(RoleEnum.ADMIN.value)
    async def update_cooldown_time_config(self, request: Request) -> Dict[str, str]:
        try:
            form_data = await request.form()
            new_config = {
                "type": form_data["type"],
                "max_attempts": int(form_data["max_attempts"]),
                "duration_hours": int(form_data["duration_hours"])
            }
            self.security_service.update_cooldown_time_config(new_config)
            return {"message": "Cooldown time configuration updated successfully"}
        except Exception as e:
            return handle_exception(request, e)

    @check_role(RoleEnum.ADMIN.value)
    async def get_max_attempts_before_limit(self, request: Request) -> Dict[str, int]:
        try:
            max_attempts = self.security_service.get_max_attempts_before_limit()
            return {"max_attempts": max_attempts}
        except Exception as e:
            return handle_exception(request, e)

    @check_role(RoleEnum.ADMIN.value)
    async def update_max_attempts_before_limit(self, request: Request) -> Dict[str, str]:
        try:
            form_data = await request.form()
            max_attempts = int(form_data["max_attempts"])
            self.security_service.update_max_attempts_before_limit(max_attempts)
            return {"message": "Max attempts before limit updated successfully"}
        except Exception as e:
            return handle_exception(request, e)

    @check_role(RoleEnum.ADMIN.value)
    async def get_rate_limit_duration_hours(self, request: Request) -> Dict[str, int]:
        try:
            duration_hours = self.security_service.get_rate_limit_duration_hours()
            return {"duration_hours": duration_hours}
        except Exception as e:
            return handle_exception(request, e)

    @check_role(RoleEnum.ADMIN.value)
    async def update_rate_limit_duration_hours(self, request: Request) -> Dict[str, str]:
        try:
            form_data = await request.form()
            duration_hours = int(form_data["duration_hours"])
            self.security_service.update_rate_limit_duration_hours(duration_hours)
            return {"message": "Rate limit duration updated successfully"}
        except Exception as e:
            return handle_exception(request, e)

    @check_role(RoleEnum.ADMIN.value)
    async def update_entire_config(self, request: Request) -> Dict[str, str]:
        try:
            form_data = await request.form()
            new_config = {
                "login_rate_limit": {
                    "limit": f"{form_data['login_rate_limit_value']}/{form_data['login_rate_limit_unit']}"
                },
                "failed_login_attempts": {
                    "enabled": form_data.get('failed_login_attempts_enabled') == 'on',
                    "cooldown_time": {
                        "unit": form_data['cooldown_time_unit'],
                        "duration": int(form_data['cooldown_time'])
                    },
                    "max_attempts_before_limit": int(form_data['max_attempts_before_limit'])
                }
            }
            self.security_service.update_entire_config(new_config)
            config = self.security_service.get_config()
            message = "Entire security configuration updated successfully"
            return templates.TemplateResponse("admin/security/index.html", {"request": request, "config": config, "message": message, "status": "success"})
        except Exception as e:
            import traceback
            traceback.print_exc()
            return handle_exception(request, e)