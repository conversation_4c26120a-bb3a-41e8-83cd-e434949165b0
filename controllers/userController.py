import json
from fastapi import Depends, HTT<PERSON>Exception, Request, status
from fastapi.encoders import jsonable_encoder
from typing import List
from schemas.UserSchema import UserCreateSchema, UserSchema, UserUpdateSchema
from enums.role_enum import RoleEnum
from services.authService import AuthService
from services.roleService import RoleService
from services.userServices import UserService
from services.websiteService import WebsiteService
from utils.authUtil import check_role
from utils.exceptionUtil import handle_exception

from config import templates

class UserController:

    def __init__(
            self, 
            user_service: UserService = Depends(UserService), 
            auth_service: AuthService = Depends(AuthService),
            website_service: WebsiteService = Depends(WebsiteService),
            role_service: RoleService = Depends(RoleService)
    ) -> None:
        self.user_service = user_service
        self.auth_service = auth_service
        self.website_service = website_service
        self.role_service = role_service
    
    @check_role(RoleEnum.ADMIN.value)
    async def get_all(self, request: Request, page: int = None, per_page: int = None) -> List[UserSchema]:
        try:
            users = self.user_service.get_all_users(page, per_page)
            users = jsonable_encoder(users)
            return users
        except Exception as e:
            return handle_exception(request, e)
        
    @check_role(RoleEnum.ADMIN.value)
    async def get_all_by_role(self, request: Request, role_name: int, page: int = None, per_page: int = None) -> List[UserSchema]:
        try:
            users = self.user_service.get_all_users_by_role(role_name, page, per_page)
            users = jsonable_encoder(users)
            return users
        except Exception as e:
            return handle_exception(request, e)
    
    @check_role(RoleEnum.ADMIN.value)
    async def get_by_id(self, request: Request, user_id: int) -> UserSchema:
        try:
            user = self.user_service.get_user_by_id(user_id)
            user = jsonable_encoder(user)
            return user
        except Exception as e:
            return handle_exception(request, e)
    
    @check_role(RoleEnum.ADMIN.value)
    async def get_by_username(self, request: Request, username: str) -> UserSchema:
        try:
            user = self.user_service.get_user_by_username(username)
            user = jsonable_encoder(user)
            return user
        except Exception as e:
            return handle_exception(request, e)
    
    @check_role(RoleEnum.ADMIN.value)
    async def get_by_email(self, request: Request, email: str) -> UserSchema:
        try:
            user = self.user_service.get_user_by_email(email)
            user = jsonable_encoder(user)
            return user
        except Exception as e:
            return handle_exception(request, e)

    @check_role(RoleEnum.ADMIN.value)    
    async def create(self, request: Request) -> UserSchema:
        try:
            form_data = await request.form()
            user_schema = UserCreateSchema(**form_data)
            user = self.user_service.create_user(user_schema)
            user = jsonable_encoder(user)
            roles = self.role_service.get_all_roles()
            roles = jsonable_encoder(roles)
            response = {"request": request, "user": user, "message": "User created successfully", "roles": roles}
            return templates.TemplateResponse("account/register.html", response)
        except Exception as e:
            return handle_exception(request, e)
    
    @check_role(RoleEnum.ADMIN.value)
    async def update(self, request: Request, user_id: int, user_update_schema: UserUpdateSchema) -> UserSchema:
        try:
            # check if user exists
            user = self.user_service.get_user_by_id(user_id)
            if not user:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
            # update user
            update_data = user_update_schema.model_dump(exclude_unset=True)
            for key, value in update_data.items():
                setattr(user, key, value)
            user = self.user_service.update_user(user_id, update_data)
            return UserSchema.model_validate(user)
        except Exception as e:
            return handle_exception(request, e)

    @check_role(RoleEnum.ADMIN.value)
    async def account_update(self, request: Request, user_id: int):
        try:
            try:
                form_data = await request.json()
            except json.JSONDecodeError:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Request body is empty or not valid JSON")
            
            print(form_data)
            form_data = UserUpdateSchema(**form_data)
            return self.user_service.update_user(user_id, form_data)
        except Exception as e:
            import traceback
            traceback.print_exc()
            return handle_exception(request, e)
    
    @check_role(RoleEnum.ADMIN.value)
    def refresh_users(self, request: Request):
        try:
            users = self.user_service.get_all_users()
            users = jsonable_encoder(users)
            return templates.TemplateResponse("account/partials/account_review_table.html", {"request": request, "users": users})
        except Exception as http_exc:
            return handle_exception(request, http_exc)


    @check_role(RoleEnum.ADMIN.value)
    async def delete(self, request: Request, user_id: int) -> bool:
        user = self.user_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
        # cannot delete currently logged in user
        current_user = self.auth_service.get_current_user(request)
        if current_user.user_id == user_id:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Cannot delete currently logged in user")
        try:
            response = self.user_service.delete_user(user_id)
            return response
        except Exception as e:
            return handle_exception(request, e)

    def email_validation(self, email: str) ->str:
        validated = self.user_service.email_validation(email)
        return validated

    def username_validation(self, username: str) ->str:
        validated = self.user_service.username_validation(username)
        return validated