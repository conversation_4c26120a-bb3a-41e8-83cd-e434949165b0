import logging
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request, Response
from httpx import As<PERSON><PERSON><PERSON>
from config import <PERSON><PERSON>ST<PERSON><PERSON><PERSON>CH_URL, ELASTICSEARCH_USERNAME, ELASTICSEARCH_PASSWORD
from services.elasticsearchService import ElasticsearchService

class ElasticsearchController:
    def __init__(self):
        # Configure the logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # Initialize ElasticsearchService
        self.es_service = ElasticsearchService()

    async def forward_to_elasticsearch(self, path: str, method: str, headers: dict, body: bytes) -> Response:
        if 'Authorization' in headers:
            del headers['Authorization']
        async with <PERSON>ync<PERSON><PERSON>(verify=True) as client:
            try:
                response = await client.request(
                    method=method,
                    url=f"{ELASTICSEARCH_URL}/{path}",
                    headers=headers,
                    content=body,
                    auth=(ELASTICSEARCH_USERNAME, ELASTICSEARCH_PASSWORD)
                )
                return Response(content=response.content, status_code=response.status_code, headers=dict(response.headers))
            except Exception as e:
                # self.logger.exception("Failed to connect to Elasticsearch: %s", e)
                raise HTTPException(status_code=500, detail="All connection attempts failed")

    async def search_products(self, index: str, pattern: str, current_page: int, page_size: int, args: dict):
        return self.es_service.search_products(index, pattern, current_page, page_size, args)

    async def update_top_searches(self, request: Request):
        try:
            # Parse JSON body from the request
            data = await request.json()
            
            # Extract required fields with error checking
            search_term = data.get('search_term')
            index_name = data.get('index_name')

            if not search_term or not index_name:
                raise HTTPException(status_code=400, detail="Missing 'search_term' or 'index_name' in request body")

            # Call the service to update the top searches
            result = await self.es_service.update_top_searches(search_term, index_name)
            return {"message": "Top search updated successfully", "result": result}

        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid JSON in request body")

        except HTTPException as http_exc:
            raise http_exc

        except Exception as e:
            import traceback
            traceback.print_exc()
            raise HTTPException(status_code=500, detail="An unexpected error occurred")

    async def load_initial_products_for_modal(self, top_search_patterns: list, index_name: str):
        return await self.es_service.load_initial_products_for_modal(top_search_patterns, index_name)

    async def get_diversified_top_searches(self, index_name: str, size: int = 5, prefix_size: int = 3, top_term_per_prefix: int = 1):
        return await self.es_service.get_diversified_top_searches(index_name, size, prefix_size, top_term_per_prefix)