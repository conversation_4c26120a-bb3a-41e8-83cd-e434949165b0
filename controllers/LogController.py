from typing import List
from fastapi import HTTPException, status
from sqlalchemy.orm import Session
from models.models import Log, Website
from schemas.LogSchema import LogSchema, LogCreateSchema

class LogController:

    @classmethod
    def get_all(cls, db: Session, page: int = None, per_page: int = None) -> List[LogSchema]:
        try:
            logs = Log.get_all(db, page, per_page)
            return [LogSchema.model_validate(log) for log in logs]
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching logs: {e}"
            )

    @classmethod
    def get_by_id(cls, db: Session, log_id: int) -> LogSchema:
        log = Log.get_by_id(db, log_id)
        if not log:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Log not found")
        try:
            return LogSchema.model_validate(log)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while fetching log: {e}"
            )
    
    @classmethod
    def create(cls, db: Session, log_schema: LogCreateSchema) -> LogSchema:
        try:
            create_data = log_schema.model_dump(exclude_unset=True)
            log = Log(**create_data)
            log = Log.create(db, log)
            return LogSchema.model_validate(log)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An error occurred while creating log: {e}"
            )