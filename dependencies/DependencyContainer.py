from fastapi import Depends
from background_jobs.product_indexation import Integrator
from repositories.scheduled_task_repository import ScheduledTaskRepository
from repositories.task_execution_log_repository import TaskExecutionLogRepository
from repositories.user_repository import UserRepository
from repositories.website_repository import WebsiteRepository
from services.scheduledTaskService import ScheduledTaskService
from services.taskExecutionLogService import TaskExecutionLogService
from services.websiteService import WebsiteService

class DependencyContainer:
    def __init__(self):
        self.website_repository = WebsiteRepository()
        self.scheduled_task_repository = ScheduledTaskRepository()
        self.task_execution_log_repository = TaskExecutionLogRepository()
        self.user_repository = UserRepository()
        
    def get_scheduled_task_service(self):
        return ScheduledTaskService(
            task_repository=self.scheduled_task_repository,
            website_repository=self.website_repository
        )
    
    def get_website_service(self):
        return WebsiteService(
            website_repository=self.website_repository,
            user_repository=self.user_repository
        )
    
    def get_task_execution_log_service(self):
        return TaskExecutionLogService(
            log_repository=self.task_execution_log_repository
        )
    
    def get_integrator(self):
        return Integrator()

dependency_container = DependencyContainer()