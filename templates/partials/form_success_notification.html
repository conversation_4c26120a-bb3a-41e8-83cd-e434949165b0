<div id="register-success" class="m-4 bg-green-800 border border-green-700 text-white px-4 py-3 rounded relative hidden"
    role="alert">
    <strong class="font-bold mr-1"> {{ _('Success!') }} </strong>
    <span class="block sm:inline success-message"></span>
    <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
        <svg class="fill-current h-6 w-6 text-white" role="button" xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20">
            <title> {{ _('Close') }} </title>
            <path
                d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697l2.652 3.031 2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.697z">
            </path>
        </svg>
    </span>
</div>

<script>

    // success alert close button
    var registerSuccess = document.querySelector('#register-success');
    if (registerSuccess) {
        registerSuccess.addEventListener('click', function () {
            this.classList.add('hidden');
        });
    }

    // listen for form-success show event with success message
    window.addEventListener('form-success:show', function (event) {
        document.querySelector('.success-message').textContent = event.detail;
        document.getElementById('register-success').classList.remove('hidden');
    })

    // listen for form-success hide event
    window.addEventListener('form-success:hide', function () {
        document.getElementById('register-success').classList.add('hidden');
    })
</script>