<!-- The Modal -->
<div id="account-update-modal"
    class="fixed inset-0 flex items-center justify-center overflow-y-auto hidden transition-opacity duration-500 ease-in-out"
    style="z-index: 1000;" onclick="handleBackdropClick(event)">
    <div class="bg-gray-200 dark:bg-gray-500 rounded-lg overflow-hidden shadow-xl transform transition-all sm:w-3/4 sm:max-w-lg"
        onclick="event.stopPropagation();">
        <div class="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-center justify-between">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                    {{ _('Update User Account') }}
                </h3>
                <!-- Close button -->
                <button onclick="closeModal()"
                    class="text-gray-400 hover:text-gray-500 dark:text-white dark:hover:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 p-1 rounded-full">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>

            </div>
            <div
                class="mt-3 mb-2 p-4 bg-gray-100 dark:bg-gray-700 transition-colors duration-500 rounded-lg border-2 border-gray-300 dark:border-gray-600">
                <div class="flex items-center justify-between">
                    <!-- Is Active Switch -->
                    <label for="is_active" class="inline-flex items-center cursor-pointer">
                        <div class="relative rounded-full bg-gray-600 p-1 dark:bg-gray-700">
                            <input type="checkbox" name="is_active" id="is_active" class="sr-only peer" />
                            <div
                                class="w-8 h-4 bg-gray-200 rounded-full peer peer-focus:ring-2 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 dark:bg-gray-500 peer-checked:after:translate-x-5 peer-checked:after:border-white after:content-[''] after:absolute after:top-1/2 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-3 after:w-3 after:-translate-y-1/2 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600">
                            </div>
                        </div>
                        <span class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300"> {{ _('Account Status') }} </span>
                    </label>

                    <!-- Is Admin Switch -->
                    <label for="is_admin" class="inline-flex items-center cursor-pointer">
                        <div class="relative rounded-full bg-gray-600 p-1 dark:bg-gray-700">
                            <input type="checkbox" name="is_admin" id="is_admin" class="sr-only peer" />
                            <div
                                class="w-8 h-4 bg-gray-200 rounded-full peer peer-focus:ring-2 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 dark:bg-gray-500 peer-checked:after:translate-x-5 peer-checked:after:border-white after:content-[''] after:absolute after:top-1/2 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-3 after:w-3 after:-translate-y-1/2 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600">
                            </div>
                        </div>
                        <span class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300"> {{ _('Admin Account') }} </span>
                    </label>
                </div>
                <!-- username -->
                <div class="mt-4">
                    <label for="username"
                        class="block ml-2 text-sm font-medium text-gray-900 dark:text-gray-300"> {{ _('Username') }} </label>
                    <input type="text" name="username" id="username"
                        class="block w-full mt-1 text-sm border-gray-600 bg-white dark:bg-gray-600 text-gray-900 dark:text-white focus:border-purple-400 focus:ring-2 focus:ring-purple-400 focus:outline-none focus:ring-opacity-75 focus:shadow-outline-purple form-input p-2 rounded-lg transition duration-200 ease-in-out hover:scale-105 focus:scale-105"
                        placeholder="{{ _('Username') }}" />
                </div>
                <!-- email -->
                <div class="mt-4">
                    <label for="email"
                        class="block ml-2 text-sm font-medium text-gray-900 dark:text-gray-300"> {{ _('Email') }} </label>
                    <input type="text" name="email" id="email"
                        class="block w-full mt-1 text-sm border-gray-600 bg-white dark:bg-gray-600 text-gray-900 dark:text-white focus:border-purple-400 focus:ring-2 focus:ring-purple-400 focus:outline-none focus:ring-opacity-75 focus:shadow-outline-purple form-input p-2 rounded-lg transition duration-200 ease-in-out hover:scale-105 focus:scale-105"
                        placeholder="{{ _('Email') }}" />
                </div>
                <!-- password -->
                <div class="mt-4">
                    <label for="password" class="block ml-2 text-sm font-medium text-gray-900 dark:text-gray-300"> {{ _('New Password') }} </label>
                    <input type="password" name="password" id="password"
                        class="block w-full mt-1 text-sm border-gray-600 bg-white dark:bg-gray-600 text-gray-900 dark:text-white focus:border-purple-400 focus:ring-2 focus:ring-purple-400 focus:outline-none focus:ring-opacity-75 focus:shadow-outline-purple form-input p-2 rounded-lg transition duration-200 ease-in-out hover:scale-105 focus:scale-105"
                        placeholder="{{ _('Leave blank to keep current password') }}" />
                </div>
                <!-- confirm password -->
                <div class="mt-4">
                    <label for="confirm_password"
                        class="block ml-2 text-sm font-medium text-gray-900 dark:text-gray-300"> {{ _('Confirm Password') }} </label>
                    <input type="password" name="confirm_password" id="confirm_password"
                        class="block w-full mt-1 text-sm border-gray-600 bg-white dark:bg-gray-600 text-gray-900 dark:text-white focus:border-purple-400 focus:ring-2 focus:ring-purple-400 focus:outline-none focus:ring-opacity-75 focus:shadow-outline-purple form-input p-2 rounded-lg transition duration-200 ease-in-out hover:scale-105 focus:scale-105"
                        placeholder="{{ _('Confirm your new password') }}" />
                </div>

            </div>

            <!-- error message -->
            <div id="register-error"
                class="bg-red-800 border border-red-700 text-white px-4 py-3 rounded relative hidden" role="alert">
                <strong class="font-bold mr-1"> {{ _('Error!') }} </strong>
                <span class="block sm:inline error-message"></span>
                <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                    <svg class="fill-current h-6 w-6 text-white" role="button" xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20">
                        <title> {{ _('Close') }} </title>
                        <path
                            d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697l2.652 3.031 2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.697z">
                        </path>
                    </svg>
                </span>
            </div>
            <!-- success message -->
            <div id="register-success"
                class="bg-green-800 border border-green-700 text-white px-4 py-3 rounded relative hidden" role="alert">
                <strong class="font-bold mr-1"> {{ _('Success!') }} </strong>
                <span class="block sm:inline success-message"></span>
                <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                    <svg class="fill-current h-6 w-6 text-white" role="button" xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20">
                        <title> {{ _('Close') }} </title>
                        <path
                            d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697l2.652 3.031 2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.697z">
                        </path>
                    </svg>
                </span>
            </div>
            <div class="bg-gray-200 dark:bg-gray-500 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse sm:items-center">
                <!-- Button to close the modal -->
                <button onclick="handleCancel()" type="button"
                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {{ _('Cancel') }}
                </button>
                <button onclick="handleSave()" type="button"
                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                    {{ _('Save') }}
                </button>
                <!-- loading indicator -->
                <div
                    class="loading-indicator w-6 h-6 border-4 border-gray-300 border-t-4 border-t-blue-500 rounded-full animate-spin-slow hidden">
                </div>
            </div>
        </div>
    </div>

    <script>
        let user_data = null
        //the function to close the modal
        function closeModal(success = false) {
            const modal = document.getElementById('account-update-modal');
            modal.style.opacity = 0; // Start the transition to opacity 0
            setTimeout(() => {
                modal.classList.add('hidden');
                if (success) {
                    console.log("triggering close event");
                    const event = new Event('refresh-users');
                    const table = document.querySelector('#accountReviewTable tbody');
                    console.log("table", table);
                    table.dispatchEvent(event);
                }
            }, 500); // After the transition ends, add the 'hidden' class
        }
        // event to open modal
        window.addEventListener('account-update-modal:open', function (event) {
            // get user data from the event
            user_data = event.detail.user_data;
            // initialize the modal
            init_form_fields();
            // open the modal
            const modal = document.getElementById('account-update-modal');
            modal.style.opacity = 1; // Start the transition to opacity 1
            setTimeout(() => modal.classList.remove('hidden'), 500);
        })
        // listen for account-update-modal:open event
        document.addEventListener('account-update-modal:open', function () {
            const modal = document.getElementById('account-update-modal');
            modal.classList.remove('hidden');
        })
        // handle cancel button
        function handleCancel() {
            closeModal();
        }
        async function handleSave() {
            // Hide error message
            const errorElement = document.getElementById('register-error');
            if (errorElement) {
                errorElement.classList.add('hidden');
            }
            // Hide success message
            const successElement = document.getElementById('register-success');
            if (successElement) {
                successElement.classList.add('hidden');
            }

            const passwordInput = document.querySelector('input[name="password"]');
            const confirmPasswordInput = document.querySelector('input[name="confirm_password"]');

            if (passwordInput && confirmPasswordInput) {
                const password = passwordInput.value.trim();
                const confirmPassword = confirmPasswordInput.value.trim();

                // Validate that the new password matches the confirm password field
                if ((password || confirmPassword) && password !== confirmPassword) {
                    if (errorElement) {
                        errorElement.classList.remove('hidden');
                    }
                    const errorMessage = document.querySelector('.error-message');
                    if (errorMessage) {
                        errorMessage.textContent = "{{ _('Passwords do not match. Please try again.') }}";
                    }
                    return; // Stop the save process
                }

                // Send data to server
                const url = '/account/update/' + user_data.user_id;
                // Update data
                const data = {
                    is_active: document.querySelector('input[name="is_active"]')?.checked ?? false,
                    is_admin: document.querySelector('input[name="is_admin"]')?.checked ?? false,
                    username: document.querySelector('input[name="username"]')?.value ?? '',
                    email: document.querySelector('input[name="email"]')?.value ?? '',
                }
                // Only include password if it's provided
                if (password) {
                    data.password = password;
                }

                // Show loading indicator
                const loadingIndicator = document.querySelector('.loading-indicator');
                if (loadingIndicator) {
                    loadingIndicator.classList.remove('hidden');
                }

                try {
                    const response = await fetch(url, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });

                    if (response.ok) {
                        // Show success message
                        if (successElement) {
                            successElement.classList.remove('hidden');
                        }
                        const successMessage = document.querySelector('.success-message');
                        if (successMessage) {
                            successMessage.textContent = "{{ _('Account updated successfully.') }}";
                        }
                        // Hide loading indicator
                        if (loadingIndicator) {
                            loadingIndicator.classList.add('hidden');
                        }
                        setTimeout(() => {
                            if (successElement) {
                                successElement.classList.add('hidden');
                            }
                            closeModal(true);
                        }, 3000);
                    } else {
                        const errorData = await response.json();
                        if (errorElement) {
                            errorElement.classList.remove('hidden');
                        }
                        const errorMessage = document.querySelector('.error-message');
                        if (errorMessage) {
                            if (errorData && errorData.detail) {
                                errorMessage.textContent = errorData.detail;
                            } else {
                                errorMessage.textContent = "{{ _('An error occurred. Please try again.') }}";
                            }
                        }
                        // Hide loading indicator
                        if (loadingIndicator) {
                            loadingIndicator.classList.add('hidden');
                        }
                    }
                } catch (error) {
                    console.log(error);
                    if (errorElement) {
                        errorElement.classList.remove('hidden');
                    }
                    const errorMessage = document.querySelector('.error-message');
                    if (errorMessage) {
                        errorMessage.textContent = "{{ _('An error occurred. Please try again.') }}";
                    }
                    // Hide loading indicator
                    if (loadingIndicator) {
                        loadingIndicator.classList.add('hidden');
                    }
                }
            } else {
                console.error(_('Password or confirm password input not found'));
            }
        }
        function init_form_fields() {
            // init is active
            document.getElementById('is_active').checked = user_data.is_active;
            // init is admin
            document.getElementById('is_admin').checked = user_data.is_admin;
            // init username
            document.getElementById('username').value = user_data.username;
            // init email
            document.getElementById('email').value = user_data.email;
            // clear password field
            document.getElementById('password').value = '';
        }

        // handle backdrop click
        function handleBackdropClick(event) {
            // closeModal();
        }
    </script>