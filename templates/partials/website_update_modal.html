<!-- The Modal -->
<div id="website-update-modal"
    class="fixed inset-0 flex items-center justify-center overflow-y-auto hidden transition-opacity duration-500 ease-in-out"
    style="z-index: 1000;" onclick="handleBackdropClick(event)">
    <div class="bg-gray-200 dark:bg-gray-800 rounded-lg overflow-hidden shadow-xl transform transition-all sm:w-3/4 sm:max-w-lg"
        onclick="event.stopPropagation();">
        <div class="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-center justify-between">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                    {{ _('Update Website') }}
                </h3>
                <!-- Close button -->
                <button onclick="closeModal()"
                    class="text-gray-400 hover:text-gray-500 dark:text-white dark:hover:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 p-1 rounded-full">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>

            </div>
            <div
                class="mt-3 mb-2 p-4 bg-gray-100 dark:bg-gray-700 transition-colors duration-500 rounded-lg border-2 border-gray-300 dark:border-gray-600">
                <!-- Website key -->
                <div class="mt-4">
                    <label for="website_key"
                        class="block ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">{{ _('Website key') }}</label>
                    <input type="text" name="website_key" id="website_key"
                        class="block w-full mt-1 text-sm border-gray-600 bg-white dark:bg-gray-600 text-gray-900 dark:text-white focus:border-purple-400 focus:ring-2 focus:ring-purple-400 focus:outline-none focus:ring-opacity-75 focus:shadow-outline-purple form-input p-2 rounded-lg transition duration-200 ease-in-out hover:scale-105 focus:scale-105"
                        disabled />
                </div>
                <!-- Website Url -->
                <div class="mt-4">
                    <label for="website_url"
                        class="block ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">{{ _('Website Url') }}</label>
                    <input type="text" name="website_url" id="website_url"
                        class="block w-full mt-1 text-sm border-gray-600 bg-white dark:bg-gray-600 text-gray-900 dark:text-white focus:border-purple-400 focus:ring-2 focus:ring-purple-400 focus:outline-none focus:ring-opacity-75 focus:shadow-outline-purple form-input p-2 rounded-lg transition duration-200 ease-in-out hover:scale-105 focus:scale-105"
                        placeholder="{{ _('Website Url') }}" />
                </div>
                <!-- CSV Url -->
                <div class="mt-4">
                    <label for="csv_url" class="block ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">{{ _('CSV Url') }}</label>
                    <input type="text" name="csv_url" id="csv_url"
                        class="block w-full mt-1 text-sm border-gray-600 bg-white dark:bg-gray-600 text-gray-900 dark:text-white focus:border-purple-400 focus:ring-2 focus:ring-purple-400 focus:outline-none focus:ring-opacity-75 focus:shadow-outline-purple form-input p-2 rounded-lg transition duration-200 ease-in-out hover:scale-105 focus:scale-105"
                        placeholder="{{ _('CSV Url') }}" />
                </div>
            </div>

            <!-- error message -->
            <div id="register-error"
                class="bg-red-800 border border-red-700 text-white px-4 py-3 rounded relative hidden" role="alert">
                <strong class="font-bold mr-1">{{ _('Error!') }}</strong>
                <span class="block sm:inline error-message"></span>
                <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                    <svg class="fill-current h-6 w-6 text-white" role="button" xmlns="http://www.w3.org/2000/svg"
                        onclick="handle_close_errorMessage(event)"
                        viewBox="0 0 20 20">
                        <title>{{ _('Close') }}</title>
                        <path
                            d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697l2.652 3.031 2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.697z">
                        </path>
                    </svg>
                </span>
            </div>
            <!-- success message -->
            <div id="register-success"
                class="bg-green-800 border border-green-700 text-white px-4 py-3 rounded relative hidden" role="alert">
                <strong class="font-bold mr-1">{{ _('Success!') }}</strong>
                <span class="block sm:inline success-message"></span>
                <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                    <svg class="fill-current h-6 w-6 text-white" role="button" xmlns="http://www.w3.org/2000/svg"
                        onclick="handle_close_successMessage(event)"
                        viewBox="0 0 20 20">
                        <title>{{ _('Close') }}</title>
                        <path
                            d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697l2.652 3.031 2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.697z">
                        </path>
                    </svg>
                </span>
            </div>
            <div class="bg-gray-200 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse sm:items-center">
                <!-- Button to close the modal -->
                <button onclick="handleCancel()" type="button"
                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {{ _('Cancel') }}
                </button>
                <button onclick="handleSave()" type="button"
                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 dark:bg-blue-800 text-base font-medium text-white hover:bg-blue-700 dark:hover:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                    {{ _('Save') }}
                </button>
                <!-- loading indicator -->
                <div
                    class="loading-indicator w-6 h-6 border-4 border-gray-300 dark:border-gray-600 border-t-4 border-t-blue-500 rounded-full dark:border-blue-800 animate-spin-slow hidden">
                </div>
            </div>
        </div>
    </div>

    <script>
        let website_data = null
        //the function to close the modal
        function closeModal() {
            const modal = document.getElementById('website-update-modal');
            modal.style.opacity = 0; // Start the transition to opacity 0
            setTimeout(() => modal.classList.add('hidden'), 500); // After the transition ends, add the 'hidden' class
        }
        // event to open modal
        window.addEventListener('website-update-modal:open', function (event) {
            // get website data from the event
            website_data = event.detail.website_data;
            // initialize the modal
            init_form_fields();
            // open the modal
            const modal = document.getElementById('website-update-modal');
            modal.style.opacity = 1; // Start the transition to opacity 1
            setTimeout(() => modal.classList.remove('hidden'), 500);
        })
        // handle cancel button
        function handleCancel() {
            closeModal();
        }
        function handleSave() {
            // hide error message
            document.getElementById('register-error').classList.add('hidden');
            // hide success message
            document.getElementById('register-success').classList.add('hidden');

            // send data to server
            const url = '/user/website/' + website_data.website_id;
            // update data
            const data = {
                website_url: document.getElementById('website_url').value,
                csv_url: document.getElementById('csv_url').value,
            }
            // show loading indicator
            document.querySelector('.loading-indicator').classList.remove('hidden');
            $.ajax({
                url: url,
                type: 'PUT',
                data: data,
                success: function (data) {
                    // show success message
                    document.getElementById('register-success').classList.remove('hidden');
                    document.querySelector('.success-message').textContent = '{{ _("Account updated successfully.") }}';
                    // hide loading indicator
                    document.querySelector('.loading-indicator').classList.add('hidden');
                    setTimeout(() => {
                        document.getElementById('register-success').classList.add('hidden');
                        closeModal();
                    }, 3000);
                },
                error: function (error) {
                    console.log(error);
                    document.getElementById('register-error').classList.remove('hidden');
                    if (error.responseJSON) {
                        document.querySelector('.error-message').textContent = error.responseJSON.detail;
                    }
                    else {
                        document.querySelector('.error-message').textContent = '{{ _("An error occurred. Please try again.") }}';
                    }
                    // hide loading indicator
                    document.querySelector('.loading-indicator').classList.add('hidden');
                }
            });
        }

        function init_form_fields() {
            //  init website key
            document.getElementById('website_key').value = website_data.website_key;
            //  init website url
            document.getElementById('website_url').value = website_data.website_url;
            //  init csv url
            document.getElementById('csv_url').value = website_data.website_settings.csv_url;
        }

        // handle backdrop click
        function handleBackdropClick(event) {
            closeModal();
        }
        function handle_close_errorMessage(event) {
            document.getElementById('register-error').classList.add('hidden');
        }
        function handle_close_successMessage(event) {
            document.getElementById('register-success').classList.add('hidden');
        }
    </script>