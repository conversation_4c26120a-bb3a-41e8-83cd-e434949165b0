<header class="h-16 bg-white dark:bg-gray-900 shadow py-4 fixed top-0 left-0 w-full z-50">
    <div class="w-full px-4 flex justify-between items-center h-full">
        <!-- Container for the logo and brand name -->
        <div class="flex items-center">
            <!-- Brand name visible on screens smaller than 768px -->
            <a href="/" class="text-sm sm:text-base font-bold text-gray-900 dark:text-white md:hidden">
                {{ _('Toolbrothers') }}
            </a>
            <!-- Logo image visible on screens 768px and larger -->
            <a href="/admin" class="hidden md:flex text-xl font-bold text-gray-900 dark:text-white">
                <img id="logo" class="h-auto w-32 md:w-48 lg:w-64 object-contain mx-auto"
                    src="{{ url_for('public', path='/assets/images/logo/logo.png') }}"
                    data-light="{{ url_for('public', path='/assets/images/logo/logo-dark.jpeg') }}"
                    data-dark="{{ url_for('public', path='/assets/images/logo/logo.png') }}" alt="{{ _('Logo') }}">
            </a>
        </div>

        <!-- Dropdown Menu Button for smaller screens -->
        <button id="mobileMenuButton" class="sm:hidden px-4 py-2 text-gray-700 dark:text-gray-200 focus:outline-none">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Container for language selection and user profile -->
        <div class="flex items-center">
            <!-- Language Selection Dropdown -->
            <div class="relative mr-4">
                <button id="languageDropdownButton" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 focus:outline-none">
                    {{ _('Change Language') }} <i class="fas fa-chevron-down ml-2"></i>
                </button>
                <div id="languageDropdownMenu" class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-700 rounded-md shadow-lg hidden transition duration-300 ease-in-out">
                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600" data-lang="en">
                        <span class="fi fi-gb mr-2"></span> English
                    </a>
                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600" data-lang="de">
                        <span class="fi fi-de mr-2"></span> Deutsch
                    </a>
                </div>
            </div>

            <!-- User Profile -->
            <div class="relative">
                <img class="h-8 w-8 rounded-full cursor-pointer" id="userProfileButton"
                    src="{{ url_for('public', path='/assets/images/avatar.jpg') }}" alt="{{ _('User profile') }}" />

                <!-- User dropdown menu -->
                <div id="userDropdownMenu"
                    class="hidden absolute right-0 mt-2 py-2 w-48 bg-white dark:bg-gray-700 rounded-md shadow-xl z-20 transition duration-300 ease-in-out">
                    <!-- Dark Mode Toggle -->
                    <div class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600">
                        <span class="text-gray-900 dark:text-gray-300 mr-2">{{ _('Dark Mode') }}</span>
                        <label for="dark-mode-toggle" class="inline-flex relative items-center cursor-pointer">
                            <input type="checkbox" id="dark-mode-toggle" class="sr-only peer">
                            <div class="w-8 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[1px] after:left-[1px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600">
                            </div>
                        </label>
                    </div>
                    <a href="#"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600">
                        <i class="fas fa-user mr-3"></i>{{ _('Profile') }}
                    </a>
                    <a href="#"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600">
                        <i class="fas fa-cog mr-3"></i>{{ _('Settings') }}
                    </a>
                    <div class="border-t border-gray-100 dark:border-gray-600"></div>
                    <a href="{{ url_for('user_logout') }}"
                        class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600">
                        <i class="fas fa-sign-out-alt mr-3"></i>{{ _('Logout') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- Collapsible Menu for smaller screens -->
    <div id="mobileMenu" class="sm:hidden absolute w-full bg-white dark:bg-gray-900 shadow-lg z-40 left-0 hidden">
        <!-- ... Place navigation links here ... -->
        <nav class="py-2">
            <a href="#"
                class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600">{{ _('Home') }}</a>
            <a href="#"
                class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600">{{ _('About') }}</a>
            <a href="#"
                class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600">{{ _('Services') }}</a>
            <a href="#"
                class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600">{{ _('Contact') }}</a>
        </nav>
    </div>
</header>

<script>
    // JavaScript to handle the dropdown functionality
    const userProfileButton = document.getElementById('userProfileButton');
    const userDropdownMenu = document.getElementById('userDropdownMenu');

    // JavaScript for the mobile menu toggle
    document.getElementById('mobileMenuButton').addEventListener('click', function () {
        var mobileMenu = document.getElementById('mobileMenu');
        mobileMenu.classList.toggle('hidden');
    });

    // Toggle user dropdown menu
    userProfileButton.addEventListener('click', function (event) {
        userDropdownMenu.classList.toggle('hidden');
        event.stopPropagation(); // Prevent click event from reaching the window listener
    });

    // Optional: Hide dropdown if clicked outside
    window.addEventListener('click', function (event) {
        if (!userDropdownMenu.classList.contains('hidden')) {
            userDropdownMenu.classList.add('hidden');
        }
    });

    // Function to change language
    async function changeLanguage(language) {
        const formData = new FormData();
        formData.append('lang', language);

        try {
            const response = await fetch('/account/change_language', { // Adjust this endpoint based on your routing
                method: 'POST',
                body: formData,
            });

            if (response.ok) {
                window.location.reload(); // Reload the page to apply the new language
            } else {
                console.error('Failed to change language');
            }
        } catch (error) {
            console.error('Error:', error);
        }
    }

    // Toggle language dropdown
    document.getElementById('languageDropdownButton').addEventListener('click', function() {
        const dropdownMenu = document.getElementById('languageDropdownMenu');
        dropdownMenu.classList.toggle('hidden');
    });

    // Event listeners for language options
    document.querySelectorAll('#languageDropdownMenu a').forEach(item => {
        item.addEventListener('click', function(event) {
            event.preventDefault();
            const selectedLanguage = this.getAttribute('data-lang');
            changeLanguage(selectedLanguage);
        });
    });

    // Optional: Hide dropdown if clicked outside
    window.addEventListener('click', function(event) {
        const dropdownMenu = document.getElementById('languageDropdownMenu');
        if (!document.getElementById('languageDropdownButton').contains(event.target) && !dropdownMenu.contains(event.target)) {
            dropdownMenu.classList.add('hidden');
        }
    });
</script>