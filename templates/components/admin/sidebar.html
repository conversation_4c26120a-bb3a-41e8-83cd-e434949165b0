<div class="hidden md:flex h-screen bg-gray-200 dark:bg-gray-800">
    <div class="flex flex-col w-64 h-full px-4 py-8 bg-white border-r dark:bg-gray-900 dark:border-gray-600 overflow-y-auto transform transition duration-300 ease-in-out">
        <h2 class="text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl 2xl:text-2xl 3xl:text-3xl font-semibold text-gray-800 dark:text-white">
            {{ _('Toolbrothers') }}
        </h2>

        <div class="flex flex-col justify-between flex-1 mt-6">
            <nav>
                <a class="flex items-center px-4 py-2 text-gray-700 rounded-md dark:text-gray-200 {{ 'bg-gray-100 dark:bg-gray-800' if request.url.path == '/admin' else '' }}"
                    href="{{ url_for('admin_home') }}">
                    <span class="mx-4 font-medium">{{ _('Dashboard') }}</span>
                </a>

                <a class="flex items-center px-4 py-2 text-gray-700 rounded-md dark:text-gray-200 {{ 'bg-gray-100 dark:bg-gray-800' if request.url.path == '/admin/filter/manage' else '' }}" href="{{ url_for('manage_filter') }}">
                    <span class="mx-4 font-medium">{{ _('Manage filters') }}</span>
                </a>

                <a class="flex items-center px-4 py-2 text-gray-700 rounded-md dark:text-gray-200 {{ 'bg-gray-100 dark:bg-gray-800' if request.url.path == '/admin/websites/monitor' else '' }}" href="{{ url_for('monitor_websites') }}">
                    <span class="mx-4 font-medium">{{ _('Monitor Websites') }}</span>
                </a>

                <a class="flex items-center px-4 py-2 text-gray-700 rounded-md dark:text-gray-200 {{ 'bg-gray-100 dark:bg-gray-800' if request.url.path == '/admin/security' else '' }}" href="{{ url_for('load_security') }}">
                    <span class="mx-4 font-medium">{{ _('Security') }}</span>
                </a>

                <!-- Accounts Section -->
                <div>
                    <a class="flex justify-between px-4 py-2 mt-5 text-gray-600 transition-colors duration-200 transform rounded-md dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 dropdownButton"
                        href="#">
                        <span class="mx-4 font-medium">{{ _('Accounts') }}</span>
                        <svg class="w-5 h-5 chevronIcon {{ 'rotate-180' if request.url.path.startswith('/account') else '' }}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </a>

                    <div class="mt-2 space-y-2 px-7 dropdownMenu {{ '' if request.url.path.startswith('/account') else 'hidden' }}">
                        <a class="block px-4 py-2 text-sm text-gray-600 transition-colors duration-200 transform rounded-md dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 {{ 'bg-gray-200 dark:bg-gray-700' if request.url.path == '/account/register' else '' }}" href="{{ url_for('register') }}">{{ _('Register') }}</a>
                        <a class="block px-4 py-2 text-sm text-gray-600 transition-colors duration-200 transform rounded-md dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 {{ 'bg-gray-200 dark:bg-gray-700' if request.url.path == '/account/review' else '' }}" href="{{ url_for('account_review') }}">{{ _('Review') }}</a>
                    </div>
                </div>

                <!-- Websites Section -->
                <div>
                    <a class="flex justify-between px-4 py-2 mt-5 text-gray-600 transition-colors duration-200 transform rounded-md dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 dropdownButton"
                        href="#">
                        <span class="mx-4 font-medium">{{ _('Websites') }}</span>
                        <svg class="w-5 h-5 chevronIcon {{ 'rotate-180' if request.url.path.startswith('/admin/website') else '' }}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </a>
                    <div class="mt-2 space-y-2 px-7 dropdownMenu {{ '' if request.url.path.startswith('/admin/website') else 'hidden' }}">
                        <a class="block px-4 py-2 text-sm text-gray-600 transition-colors duration-200 transform rounded-md dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 {{ 'bg-gray-200 dark:bg-gray-700' if request.url.path == '/admin/website/register' else '' }}" href="{{ url_for('website_register') }}">{{ _('Register') }}</a>
                        <a class="block px-4 py-2 text-sm text-gray-600 transition-colors duration-200 transform rounded-md dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 {{ 'bg-gray-200 dark:bg-gray-700' if request.url.path == '/admin/website/review' else '' }}" href="{{ url_for('websites_review') }}">{{ _('Review') }}</a>
                    </div>
                </div>

                <!-- Scheduled Tasks Section -->
                <div>
                    <a class="flex justify-between px-4 py-2 mt-5 text-gray-600 transition-colors duration-200 transform rounded-md dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 dropdownButton"
                        href="#">
                        <span class="mx-4 font-medium">{{ _('Scheduled Tasks') }}</span>
                        <svg class="w-5 h-5 chevronIcon {{ 'rotate-180' if request.url.path.startswith('/admin/tasks') else '' }}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </a>
                    <div class="mt-2 space-y-2 px-7 dropdownMenu {{ '' if request.url.path.startswith('/admin/tasks') else 'hidden' }}">
                        <a class="block px-4 py-2 text-sm text-gray-600 transition-colors duration-200 transform rounded-md dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 {{ 'bg-gray-200 dark:bg-gray-700' if request.url.path == '/admin/tasks/create' else '' }}" href="{{ url_for('create_task') }}">{{ _('Create Task') }}</a>
                        <a class="block px-4 py-2 text-sm text-gray-600 transition-colors duration-200 transform rounded-md dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 {{ 'bg-gray-200 dark:bg-gray-700' if request.url.path == '/admin/tasks/monitor' else '' }}" href="{{ url_for('tasks_monitor') }}">{{ _('Monitor') }}</a>
                    </div>
                </div>

                <!-- Task Execution Logs Section -->
                <a class="flex items-center px-4 py-2 text-gray-700 rounded-md dark:text-gray-200 {{ 'bg-gray-100 dark:bg-gray-800' if request.url.path == '/admin/logs' else '' }}" href="{{ url_for('get_logs') }}">
                    <span class="mx-4 font-medium">{{ _('View Logs') }}</span>
                </a>
            </nav>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', (event) => {
        const dropdownButtons = document.querySelectorAll('.dropdownButton');
        dropdownButtons.forEach(dropdownButton => {
            dropdownButton.addEventListener('click', function () {
                const chevronIcon = this.querySelector('.chevronIcon');
                chevronIcon.classList.toggle('rotate-180');
                // Toggle the dropdown menu hidden class. it is the sibling of the dropdown button
                this.nextElementSibling.classList.toggle('hidden');
            });
        });
    });
</script>