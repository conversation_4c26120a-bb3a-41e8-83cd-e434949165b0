<!-- Outer div, initially hidden, visible on medium screens and larger -->
<div class="hidden md:flex h-screen bg-gray-200 dark:bg-gray-800">
    <!-- Inner div with sidebar content -->
    <div class="flex flex-col w-64 h-full px-4 py-8 bg-white border-r dark:bg-gray-900 dark:border-gray-600 overflow-y-auto transform transition duration-300 ease-in-out">
        <h2 class="text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl 2xl:text-2xl 3xl:text-3xl font-semibold text-gray-800 dark:text-white">
            {{ _('Toolbrothers') }}
        </h2>

        <div class="flex flex-col justify-between flex-1 mt-6">
            <nav>
                <!-- Use responsive text size and padding classes if needed -->
                <a class="flex items-center px-4 py-2 text-gray-700 rounded-md dark:text-gray-200 {{ 'bg-gray-100 dark:bg-gray-800' if request.url.path == '/' else '' }}" href="{{ url_for('user_home') }}">
                    <span class="mx-4 font-medium">{{ _('Dashboard') }}</span>
                </a>

                <div>
                    <a class="flex justify-between px-4 py-2 mt-5 text-gray-600 transition-colors duration-200 transform rounded-md dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 dropdownButton" href="#">
                        <span class="mx-4 font-medium">{{ _('Data') }}</span>
                        <svg class="w-5 h-5 chevronIcon {{ 'rotate-180' if request.url.path.startswith('/data') else '' }}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </a>

                    <div class="mt-2 space-y-2 px-7 dropdownMenu {{ '' if request.url.path.startswith('/data') else 'hidden' }}">
                        <a class="block px-4 py-2 text-sm text-gray-600 transition-colors duration-200 transform rounded-md dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 {{ 'bg-gray-200 dark:bg-gray-700' if request.url.path == '/data/synchronize' else '' }}" href="{{ url_for('data_synchronize') }}">{{ _('Synchronization') }}</a>
                        <a class="block px-4 py-2 text-sm text-gray-600 transition-colors duration-200 transform rounded-md dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700" href="#">{{ _('Search Stats') }}</a>
                    </div>
                </div>

                <div>
                    <a class="flex justify-between px-4 py-2 mt-5 text-gray-600 transition-colors duration-200 transform rounded-md dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 dropdownButton" href="#">
                        <span class="mx-4 font-medium">{{ _('Frontend') }}</span>
                        <svg class="w-5 h-5 chevronIcon {{ 'rotate-180' if request.url.path.startswith('/frontend') else '' }}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </a>

                    <div class="mt-2 space-y-2 px-7 dropdownMenu {{ '' if request.url.path.startswith('/frontend') else 'hidden' }}">
                        <a class="block px-4 py-2 text-sm text-gray-600 transition-colors duration-200 transform rounded-md dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 {{ 'bg-gray-200 dark:bg-gray-700' if request.url.path == '/frontend/integration' else '' }}" href="{{ url_for('frontend_integration') }}">{{ _('Integration') }}</a>
                        <a class="block px-4 py-2 text-sm text-gray-600 transition-colors duration-200 transform rounded-md dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 {{ 'bg-gray-200 dark:bg-gray-700' if request.url.path == '/frontend/customization' else '' }}" href="{{ url_for('frontend_customization') }}">{{ _('Customization') }}</a>
                    </div>
                </div>

                <div>
                    <a class="flex justify-between px-4 py-2 mt-5 text-gray-600 transition-colors duration-200 transform rounded-md dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 dropdownButton" href="#">
                        <span class="mx-4 font-medium">{{ _('Reports') }}</span>
                        <svg class="w-5 h-5 chevronIcon {{ 'rotate-180' if request.url.path.startswith('/reports') else '' }}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </a>

                    <div class="mt-2 space-y-2 px-7 dropdownMenu {{ '' if request.url.path.startswith('/reports') else 'hidden' }}">
                        <a class="block px-4 py-2 text-sm text-gray-600 transition-colors duration-200 transform rounded-md dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 {{ 'bg-gray-200 dark:bg-gray-700' if request.url.path == '/reports/search_terms' else '' }}" href="{{ url_for('reports_search_terms') }}">{{ _('Search Terms') }}</a>
                    </div>
                </div>
            </nav>
        </div>
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', (event) => {
        const dropdownButtons = document.querySelectorAll('.dropdownButton');
        dropdownButtons.forEach(dropdownButton => {
            dropdownButton.addEventListener('click', function () {
                const chevronIcon = this.querySelector('.chevronIcon');
                chevronIcon.classList.toggle('rotate-180');
                // Toggle the dropdown menu hidden class. it is the sibling of the dropdown button
                this.nextElementSibling.classList.toggle('hidden');
            });
        });
    });
</script>
