<header class="h-16 bg-white dark:bg-gray-900 shadow py-4 fixed top-0 left-0 w-full z-50">
    <div class="w-full px-4 flex justify-between items-center h-full">

        <!-- Container for the logo and brand name -->
        <div class="flex items-center">
            <!-- Brand name visible on screens smaller than 768px -->
            <a href="/" class="text-sm sm:text-base font-bold text-gray-900 dark:text-white md:hidden">
                {{ _('Toolbrothers') }}
            </a>
            <!-- Logo image visible on screens 768px and larger -->
            <a href="/" class="hidden md:flex text-xl font-bold text-gray-900 dark:text-white">
                <img id="logo" class="h-auto w-32 md:w-48 lg:w-64 object-contain mx-auto"
                    src="{{ url_for('public', path='/assets/images/logo/logo.png') }}"
                    data-light="{{ url_for('public', path='/assets/images/logo/logo-dark.jpeg') }}"
                    data-dark="{{ url_for('public', path='/assets/images/logo/logo.png') }}" alt="{{ _('Logo') }}">
            </a>
        </div>

        <!-- Dropdown Menu Button for smaller screens -->
        <button id="mobileMenuButton" class="sm:hidden px-4 py-2 text-gray-700 dark:text-gray-200 focus:outline-none">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Dropdown and other navbar content -->
        <div class="hidden sm:block sm:flex sm:items-center sm:justify-between sm:w-full">
            <!-- Custom Dropdown select with default selection and consistent styling -->
            <div class="flex-grow flex justify-center">
                {% if websites %}
                <div class="mx-4 relative ">
                    <button id="dropdownButton"
                        class="block w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-white py-2 px-4 rounded leading-tight focus:outline-none focus:border-gray-500 text-left flex justify-between items-center">
                        <span class="flex-grow">
                            <div class="font-bold truncate">{{ current_website.website_url }}</div>
                            <div class="text-sm truncate text-gray-400">{{ current_website.website_key | upper }}</div>
                        </span>
                        <!-- Chevron icon with toggleable rotate class -->
                        <svg id="chevronIcon" class="h-4 w-4 fill-current transform ml-4"
                            xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                            <path
                                d="M5.95 6.95a.5.5 0 01.7 0L10 10.29l3.35-3.34a.5.5 0 11.7.7l-4 4a.5.5 0 01-.7 0l-4-4a.5.5 0 010-.7z" />
                        </svg>
                    </button>
                    <div id="dropdownMenu"
                        class="absolute z-10 w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded shadow-lg mt-1 hidden">
                        {% for website in websites %}
                        <a href="{{ url_for('switch_website') }}?selectedWebsite={{ website.website_url }}"
                            class="block p-4 hover:bg-gray-100 dark:hover:bg-gray-800 {{ 'bg-blue-500 dark:bg-blue-700 text-white' if website.website_url== current_website.website_url }}">
                            <div class="font-bold truncate">{{ website.website_url }}</div>
                            <div class="text-sm truncate">{{ website.website_key | upper }}</div>
                        </a>
                        {% endfor %}
                    </div>
                    <!-- Hidden input to store the selected value -->
                    <input type="hidden" id="selectedValue" name="website_domain">
                </div>
                {% endif %}
            </div>

            <!-- Navigation options and user profile to the right -->
            <div class="flex justify-end space-x-4">
                <!-- Navigation options here -->
                <a href="#" class="text-gray-900 dark:text-white">{{ _('Contact Us') }}</a>

                <!-- User profile and language selection -->
                <div class="flex items-center space-x-4">
                    <!-- Language Selection Dropdown -->
                    <div class="relative">
                        <button id="languageDropdownButton"
                            class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200">
                            {{ _('Change Language') }} <i class="fas fa-chevron-down ml-2"></i>
                        </button>
                        <div id="languageDropdownMenu"
                            class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-700 rounded-md shadow-lg hidden transition duration-300 ease-in-out">
                            <a href="#"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600"
                                data-lang="en">
                                <span class="fi fi-gb mr-2"></span> English
                            </a>
                            <a href="#"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600"
                                data-lang="de">
                                <span class="fi fi-de mr-2"></span> Deutsch
                            </a>
                        </div>
                    </div>

                    <!-- User profile -->
                    <div class="relative">
                        <img class="h-8 w-8 rounded-full cursor-pointer" id="userProfileButton"
                            src="{{ url_for('public', path='/assets/images/avatar.jpg') }}"
                            alt="{{ _('User profile') }}" />

                        <!-- User dropdown menu -->
                        <div id="userDropdownMenu"
                            class="hidden absolute right-0 mt-2 py-2 w-48 bg-white dark:bg-gray-700 rounded-md shadow-xl z-20">
                            <!-- Dark Mode Toggle -->
                            <div
                                class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600">
                                <span class="text-gray-900 dark:text-gray-300 mr-2">{{ _('Dark Mode') }}</span>
                                <label for="dark-mode-toggle" class="inline-flex relative items-center cursor-pointer">
                                    <input type="checkbox" id="dark-mode-toggle" class="sr-only peer">
                                    <div
                                        class="w-8 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[1px] after:left-[1px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600">
                                    </div>
                                </label>
                            </div>
                            <a href="#"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600">
                                <i class="fas fa-user mr-3"></i>{{ _('Profile') }}
                            </a>
                            <a href="#"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600">
                                <i class="fas fa-cog mr-3"></i>{{ _('Settings') }}
                            </a>
                            <div class="border-t border-gray-100 dark:border-gray-600"></div>
                            <a href="{{ url_for('user_logout') }}"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600">
                                <i class="fas fa-sign-out-alt mr-3"></i>{{ _('Logout') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Collapsible Menu for smaller screens -->
    <div id="mobileMenu" class="sm:hidden absolute w-full bg-white dark:bg-gray-900 shadow-lg z-40 left-0 hidden">
        <!-- ... Place navigation links here ... -->
        <nav class="py-2">
            <a href="#"
                class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600">{{
                _('Home') }}</a>
            <a href="#"
                class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600">{{
                _('About') }}</a>
            <a href="#"
                class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600">{{
                _('Services') }}</a>
            <a href="#"
                class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600">{{
                _('Contact') }}</a>
        </nav>
    </div>
</header>

<style>
    /* Animation for the dropdown menu */
    @keyframes slideDown {
        0% {
            opacity: 0;
            transform: translateY(-10px);
        }

        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Animation for the dropdown menu items */
    @keyframes slideDownItems {
        0% {
            opacity: 0;
            transform: translateY(-10px);
        }

        50% {
            opacity: 0.5;
        }

        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    #dropdownMenu {
        animation: slideDown 0.3s ease-in-out forwards;
    }

    #dropdownMenu a {
        animation: slideDownItems 0.3s ease-in-out forwards;
        animation-delay: 0.1s;
        /* Stagger the item animations */
    }
</style>

<script>
    // JavaScript to handle the dropdown functionality
    const dropdownButton = document.getElementById('dropdownButton');
    const dropdownMenu = document.getElementById('dropdownMenu');
    const chevronIcon = document.getElementById('chevronIcon'); // Ensure you have the chevron icon element
    const userProfileButton = document.getElementById('userProfileButton');
    const userDropdownMenu = document.getElementById('userDropdownMenu');


    // JavaScript for the mobile menu toggle
    document.getElementById('mobileMenuButton').addEventListener('click', function () {
        var mobileMenu = document.getElementById('mobileMenu');
        mobileMenu.classList.toggle('hidden');
    });

    // Toggle dropdown
    if (dropdownButton && dropdownMenu) {
        dropdownButton.addEventListener('click', function () {
            dropdownMenu.classList.toggle('hidden');
            // Toggle the chevron icon to rotate
            chevronIcon.classList.toggle('rotate-180');

            // Add a cascading effect to the menu items
            const items = dropdownMenu.querySelectorAll('a');
            items.forEach((item, index) => {
                item.style.animationDelay = `${index * 50}ms`; // Delay each item by 50ms
            });
        });
    }

    // Toggle user dropdown menu
    userProfileButton.addEventListener('click', function (event) {
        userDropdownMenu.classList.toggle('hidden');
        event.stopPropagation(); // Prevent click event from reaching the window listener
    });

    // Optional: Hide dropdown if clicked outside
    window.addEventListener('click', function (event) {
        if (dropdownButton && dropdownMenu) {
            if (!dropdownButton.contains(event.target) && !dropdownMenu.contains(event.target)) {
                if (!dropdownMenu.classList.contains('hidden')) {
                    dropdownMenu.classList.add('hidden');
                    // If dropdown is being hidden, ensure the chevron icon is reset to original state
                    chevronIcon.classList.remove('rotate-180');
                }
            }
            if (!userDropdownMenu.classList.contains('hidden')) {
                userDropdownMenu.classList.add('hidden');
            }
        }
    });


    document.addEventListener('DOMContentLoaded', (event) => {
        // dark mode
        const toggle = document.getElementById('dark-mode-toggle');
        const htmlElement = document.documentElement;
        const darkMode = localStorage.getItem('darkMode');
        const logoElement = document.getElementById('logo');

        if (darkMode === 'true') {
            toggle.checked = true;
            htmlElement.classList.add('dark');

            // If dark mode is enabled, switch to the dark mode logo
            if (logoElement) {
                logoElement.src = logoElement.getAttribute('data-dark');
            }
        }
        else {
            // If dark mode is enabled, switch to the dark mode logo
            if (logoElement) {
                logoElement.src = logoElement.getAttribute('data-light');
            }
        }


        // Event listener for the toggle checkbox
        toggle.addEventListener('change', function () {
            if (this.checked) {
                htmlElement.classList.add('dark');
                localStorage.setItem('darkMode', 'true');
                logoElement.src = logoElement.getAttribute('data-dark');
            } else {
                htmlElement.classList.remove('dark');
                localStorage.setItem('darkMode', 'false');
                logoElement.src = logoElement.getAttribute('data-light');
            }
        });
    });

    // Function to change language
    async function changeLanguage(language) {
        const formData = new FormData();
        formData.append('lang', language);

        try {
            const response = await fetch('/account/change_language', { // Adjust this endpoint based on your routing
                method: 'POST',
                body: formData,
            });

            if (response.ok) {
                window.location.reload(); // Reload the page to apply the new language
            } else {
                console.error('Failed to change language');
            }
        } catch (error) {
            console.error('Error:', error);
        }
    }

    // Toggle language dropdown
    document.getElementById('languageDropdownButton').addEventListener('click', function () {
        const dropdownMenu = document.getElementById('languageDropdownMenu');
        dropdownMenu.classList.toggle('hidden');
    });

    // Event listeners for language options
    document.querySelectorAll('#languageDropdownMenu a').forEach(item => {
        item.addEventListener('click', function (event) {
            event.preventDefault();
            const selectedLanguage = this.getAttribute('data-lang');
            changeLanguage(selectedLanguage);
        });
    });

    // Optional: Hide dropdown if clicked outside
    window.addEventListener('click', function (event) {
        const dropdownMenu = document.getElementById('languageDropdownMenu');
        if (!document.getElementById('languageDropdownButton').contains(event.target) && !dropdownMenu.contains(event.target)) {
            dropdownMenu.classList.add('hidden');
        }
    });
</script>