{% extends "base.html" %}

{% block title %}{{ _('Login') }}{% endblock %}

{% block content %}
<div class="flex items-center justify-center min-h-[85vh] bg-gray-100 dark:bg-gray-800">
    <div class="w-full max-w-md px-4 sm:px-6 lg:px-8">
        <div>
            <!-- Ensure the logo is set for dark mode -->
            <img id="logo" class="mx-auto h-12 w-auto"
                src="{{ url_for('public', path='/assets/images/logo/logo-dark.jpeg') }}"
                data-light="{{ url_for('public', path='/assets/images/logo/logo-dark.jpeg') }}"
                data-dark="{{ url_for('public', path='/assets/images/logo/logo.png') }}"
                alt="{{ _('Logo') }}">
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
                {{ _('Sign in to your account') }}
            </h2>
            <!-- Display login errors -->
            {% if error %}
            {{ form_error(error) }}
            {% endif %}
        </div>
        <form class="mt-8 space-y-6" action="{{ url_for('login') }}" method="POST">
            {{csrf_input}}
            <input type="hidden" name="remember" value="true">

            <div class="rounded-md shadow-sm -space-y-px">
                <div class="mb-4">
                    <label for="email-address" class="sr-only">{{ _('Email address') }}</label>
                    <input id="email-address" name="email" type="email" autocomplete="email" required
                        class="appearance-none rounded relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm dark:bg-gray-700 dark:text-white dark:border-gray-600"
                        placeholder="{{ _('Email address') }}" value="{{ email|default('', true) }}">
                </div>
                <div class="mb-4">
                    <label for="password" class="sr-only">{{ _('Password') }}</label>
                    <input id="password" name="password" type="password" autocomplete="current-password" required
                        class="appearance-none rounded relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm dark:bg-gray-700 dark:text-white dark:border-gray-600"
                        placeholder="{{ _('Password') }}">
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="text-sm">
                    <a href="#" class="font-medium text-indigo-600 hover:text-indigo-500">
                        {{ _('Forgot your password?') }}
                    </a>
                </div>
            </div>

            <div>
                <button type="submit"
                    class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    {{ _('Sign in') }}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block javascript %}
<script>
    document.addEventListener('DOMContentLoaded', (event) => {
        const logoElement = document.getElementById('logo');
        // Check the dark mode setting and set the appropriate logo
        if (document.documentElement.classList.contains('dark')) {
            logoElement.src = logoElement.getAttribute('data-dark');
        } else {
            logoElement.src = logoElement.getAttribute('data-light');
        }
    });
</script>
{% endblock %}
