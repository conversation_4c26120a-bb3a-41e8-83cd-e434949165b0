<table class="w-full text-sm text-left text-gray-500 dark:text-gray-400" 
    id="accountReviewTable"
    xh-trigger="refresh-users" xh-get="{{ url_for('refresh_users') }}" xh-swap="outerHTML"
>
    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
        <tr>
            <th scope="col" class="px-6 py-3">
                {{ _('Username') }}
            </th>
            <th scope="col" class="px-6 py-3">
                {{ _('Email') }}
            </th>
            <th scope="col" class="px-6 py-3">
                {{ _('Active') }}
            </th>
            <th scope="col" class="px-6 py-3">
                {{ _('Actions') }}
            </th>
        </tr>
    </thead>
    <tbody>
        {% if users %}
        {% for user in users %}
        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                {{user.username}}
            </th>
            <td class="px-6 py-4">
                {{user.email}}
            </td>
            <td class="px-6 py-4">
                <span
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                    {% if user.is_active %} bg-green-100 text-green-800 {% else %} bg-red-100 text-red-800 {% endif %}">
                    {{ _('Active') if user.is_active else _('Inactive') }}
                </span>
            </td>

            <td class="px-6 py-4">
                <div class="flex space-x-2">
                    <button data-user='{{ user | tojson | safe }}'
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs action-button"
                        onclick="handle_update_user(event)">
                        {{ _('Update') }}
                    </button>

                    <button data-id="{{user.user_id}}" onclick="confirm_delete_user(event)"
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs action-button">
                        {{ _('Delete') }}
                    </button>
                    {% include 'partials/form_submit_spinner.html' %}
                </div>
            </td>
        </tr>
        {% endfor %}
        {% endif %}
    </tbody>
</table>