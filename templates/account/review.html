{% extends "layouts/admin_base.html" %}

{% block title %}{{ _('Accounts Review') }}{% endblock %}

{% block admin_content %}
<section class="dark:bg-gray-800 py-4 px-4 sm:px-6 lg:px-8 h-full">
    <div class="flex items-center justify-between mb-8 flex-wrap md:flex-nowrap">
        <h1 class="ml-3 text-3xl font-semibold text-gray-900 dark:text-white">{{ _('Accounts Review') }}</h1>
    </div>
    <!-- a datatable for search terms along with occurrences -->
    <div class="w-full overflow-hidden rounded-lg shadow-xs">
        <div class="w-full overflow-x-auto">
            <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400" xh-trigger="refresh-users"
                xh-get="{{ url_for('refresh_users') }}" xh-swap="outerHTML" hx-target="#accountReviewTable" id="accountReviewTable">
                <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                        <th scope="col" class="px-6 py-3">
                            {{ _('Username') }}
                        </th>
                        <th scope="col" class="px-6 py-3">
                            {{ _('Email') }}
                        </th>
                        <th scope="col" class="px-6 py-3">
                            {{ _('Active') }}
                        </th>
                        <th scope="col" class="px-6 py-3">
                            {{ _('Actions') }}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {% if users %}
                    {% for user in users %}
                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                            {{user.username}}
                        </th>
                        <td class="px-6 py-4">
                            {{user.email}}
                        </td>
                        <td class="px-6 py-4">
                            <span
                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                {% if user.is_active %} bg-green-100 text-green-800 {% else %} bg-red-100 text-red-800 {% endif %}">
                                {{ _('Active') if user.is_active else _('Inactive') }}
                            </span>
                        </td>

                        <td class="px-6 py-4">
                            <div class="flex space-x-2">
                                <button data-user='{{ user | tojson | safe }}'
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs action-button"
                                    onclick="handle_update_user(event)">
                                    {{ _('Update') }}
                                </button>

                                <button data-id="{{user.user_id}}" onclick="confirm_delete_user(event)"
                                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs action-button">
                                    {{ _('Delete') }}
                                </button>
                                {% include 'partials/form_submit_spinner.html' %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                    {% endif %}
                </tbody>
            </table>

        </div>
    </div>
</section>
<!-- Loading overlay div -->
<div id="loading-overlay"
    class="hidden fixed inset-0 bg-white dark:bg-gray-800 bg-opacity-80 dark:bg-opacity-80 z-50 flex items-center justify-center">
    <div id="loading-spinner" class="text-2xl font-bold text-black dark:text-white">
        {{ _('Reloading...') }}
    </div>
</div>
<!-- account update modal -->
{% include 'partials/account_update_modal.html' %}

{% endblock %}

{% block admin_javascript %}
<script>
    document.addEventListener('DOMContentLoaded', function () {

    });

    function handle_update_user(event) {
        event.preventDefault();
        const user = JSON.parse(event.target.dataset.user);
        // console.log(user);
        // show the modal
        window.dispatchEvent(new CustomEvent('account-update-modal:open', {
            detail: {
                user_data: user,
            }
        }))
    }

    function confirm_delete_user(event) {
        event.preventDefault();
        const confirm_message = `{{ _('Are you sure you want to delete this user?') }}`;
        confirmAction(confirm_message, handle_delete_user, event);
    }

    function handle_delete_user(event) {
        event.preventDefault();
        let id = event.target.dataset.id;
        // show the loading spinner of the current button
        toggle_loading_spinner(event.target);
        id = parseInt(id);
        toggle_action_buttons()
        $.ajax({
            url: '/account/delete/' + id,
            type: 'DELETE',
            success: function (response) {
                // handle successful deletion
                console.log(response);
                // hide the loading spinner
                toggle_loading_spinner(event.target);
                toggle_action_buttons();
                reloadPage();
            },
            error: function (error) {
                console.log(error.responseJSON.detail);
                showAlert(error.responseJSON.detail, 'error');
                // hide the loading spinner
                toggle_loading_spinner(event.target);
                toggle_action_buttons();
            }
        });
    }

    // Show the loading overlay before reloading
    function reloadPage() {
        document.getElementById('loading-overlay').classList.remove('hidden');
        setTimeout(function () {
            location.reload();
        }, 1000);
    }
    function toggle_loading_spinner(element, data) {
        element.nextSibling.nextSibling.classList.toggle('hidden');
    }

    function toggle_action_buttons() {
        let buttons = document.getElementsByClassName('action-button');
        for (let i = 0; i < buttons.length; i++) {
            buttons[i].disabled = !buttons[i].disabled;
        }
    }
    console.log("triggering close event");
    const event = new Event('refresh-users');
    const table = document.querySelector('#accountReviewTable');
    document.addEventListener('refresh-users', () => {
        console.log("triggered");
    })
    console.log("table", table);
    document.dispatchEvent(event);
    setTimeout(() => {
    }, 2000);
</script>
{% endblock %}
