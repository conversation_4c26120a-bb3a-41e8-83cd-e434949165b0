{% extends "layouts/admin_base.html" %}

{% block title %}{{ _('Register') }}{% endblock %}

{% block admin_content %}
<div class="flex items-center justify-center min-h-[85vh] bg-gray-100 dark:bg-gray-800">
  <div class="w-full max-w-md px-4 sm:px-6 lg:px-8">
    <div>
      <img id="logo" class="mx-auto h-12 w-auto" src="{{ url_for('public', path='/assets/images/logo/logo.png') }}"
        data-light="{{ url_for('public', path='/assets/images/logo/logo-dark.jpeg') }}"
        data-dark="{{ url_for('public', path='/assets/images/logo/logo.png') }}" alt="{{ _('Logo') }}">
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
        {{ _('Create your account') }}
      </h2>
    </div>
    {% if error %}
    {{ form_error(error) }}
    {% endif %}

    <form class="mt-8 space-y-6" action="{{ url_for('register') }}" method="POST">
      {{csrf_input}}
      <div class="rounded-md shadow-sm -space-y-px">
        <div class="rounded-md shadow-sm">
          <div class="mb-4 flex items-center">
            <label for="email-address" class="sr-only">{{ _('Email address') }}</label>
            <input id="email-address" name="email" type="email"
              class="appearance-none rounded relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:border-transparent focus:z-10 sm:text-sm transition duration-300 ease-in-out dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:focus:border-gray-500 dark:focus:ring-indigo-400 dark:placeholder-gray-400 hover:border-gray-400 dark:hover:border-gray-500"
              placeholder="{{ _('Email address') }}"
              value="{{ (form_data if form_data is defined else {}).email|default('', true) }}"
              hx-trigger="keyup changed delay:500ms" hx-swap="innerHTML" hx-target="#email-icon-feedback"
              hx-post={{url_for('validate_email')}} />
            <div id="email-icon-feedback" class="ml-2 hidden"></div>
          </div>

          <div class="mb-4 flex items-center">
            <label for="username" class="sr-only">{{ _('Username') }}</label>
            <input id="username" name="username" type="text" required
              class="appearance-none rounded relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:border-transparent focus:z-10 sm:text-sm transition duration-300 ease-in-out dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:focus:border-gray-500 dark:focus:ring-indigo-400 dark:placeholder-gray-400 hover:border-gray-400 dark:hover:border-gray-500"
              placeholder="{{ _('Username') }}"
              value="{{ (form_data if form_data is defined else {}).username|default('', true) }}"
              hx-trigger="keyup changed delay:500ms" hx-swap="innerHTML" hx-target="#username-icon-feedback"
              hx-post={{url_for('validate_username')}} />
            <div id="username-icon-feedback" class="ml-2 hidden"></div>
          </div>
          <div class="mb-4">
            <label for="password" class="sr-only">{{ _('Password') }}</label>
            <input id="password" name="password" type="password" autocomplete="current-password" required
              class="appearance-none rounded relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:border-transparent focus:z-10 sm:text-sm transition duration-300 ease-in-out dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:focus:border-gray-500 dark:focus:ring-indigo-400 dark:placeholder-gray-400 hover:border-gray-400 dark:hover:border-gray-500"
              placeholder="{{ _('Password') }}">
          </div>
          <div class="mb-4 flex items-center">
            <label for="confirm-password" class="sr-only">{{ _('Confirm Password') }}</label>
            <input id="confirm-password" name="confirm_password" type="password" autocomplete="current-password"
              required
              class="appearance-none rounded relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:border-transparent focus:z-10 sm:text-sm transition duration-300 ease-in-out dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:focus:border-gray-500 dark:focus:ring-indigo-400 dark:placeholder-gray-400 hover:border-gray-400 dark:hover:border-gray-500"
              placeholder="{{ _('Confirm Password') }}">
            <div id="confirm-password-icon-feedback" class="ml-2 hidden"></div>
          </div>
          <div class="mb-4 flex items-center">
            <!-- Hidden field to ensure a value is sent when checkbox is not checked -->
            <input type="hidden" name="is_admin" value="false">
            <!-- Checkbox field; when checked, it will override the hidden field's value -->
            <input id="is_admin" name="is_admin" type="checkbox"
              class="form-checkbox h-5 w-5 text-indigo-600 transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:ring-offset-gray-300 dark:focus:ring-offset-gray-800"
              value="1" {% if (form_data if form_data is defined else {}).is_admin %} checked {% endif %}>
            <label for="is_admin"
              class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400 cursor-pointer">
              {{ _('Is Admin') }}
            </label>
          </div>

          <div class="mb-4">
            <label for="role_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ _('Role') }}</label>
            <select id="role_id" name="role_id" required
              class="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 bg-white text-gray-900 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent focus:z-10 sm:text-sm transition duration-150 ease-in-out dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:border-gray-500">
              <option value="" disabled {% if not (form_data if form_data is defined else {}).role_id %} selected {% endif %}>
                {{ _('Please select a role') }}
              </option>
              {% for role in roles %}
              <option value="{{ role.role_id }}" {% if role.role_id==(form_data if form_data is defined else {}).role_id
                %} selected {% endif %}>
                {{ role.name }}
              </option>
              {% endfor %}
            </select>
          </div>
        </div>

        <div>
          <button type="submit"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 active:bg-indigo-800 transition duration-150 ease-in-out disabled:bg-indigo-400 disabled:cursor-not-allowed"
            >
            {{ _('Register') }}
          </button>
        </div>
    </form>
  </div>
</div>


<!-- alert code -->
{% if message %}
{{ alert(message, 'success') }}
{% endif %}
<script>
  document.addEventListener('DOMContentLoaded', function () {
    // Check local storage for dark mode setting
    const logoElement = document.getElementById('logo');
    if (localStorage.getItem('darkMode') === 'false') {
      // If dark mode is enabled, switch to the light mode logo
      logoElement.src = logoElement.getAttribute('data-light');
    }
    else {
      // If dark mode is enabled, switch to the dark mode logo
      logoElement.src = logoElement.getAttribute('data-dark');
    }

    document.addEventListener('htmx:afterSwap', function (event) {
      const targetElement = event.detail.target;
      const responseText = event.detail.xhr.response;
      if (targetElement.id === 'email-icon-feedback') {
        handleCommonValidation(responseText, 'email-icon-feedback');
      }
      if (targetElement.id === 'username-icon-feedback') {
        handleCommonValidation(responseText, 'username-icon-feedback');
      }
    });

    function handleCommonValidation(response, selector) {
      const iconFeedbackElement = document.getElementById(selector);
      iconFeedbackElement.classList.remove('hidden');
      response = JSON.parse(response);
      if (response === true) {
        iconFeedbackElement.innerHTML = ok_icon();
        iconFeedbackElement.classList.remove('text-red-500');
        iconFeedbackElement.classList.add('text-green-500');
      } else if (response === false) {
        iconFeedbackElement.innerHTML = not_ok_icon();
        iconFeedbackElement.classList.remove('text-green-500');
        iconFeedbackElement.classList.add('text-red-500');
      } else {
        // Clear the feedback
        iconFeedbackElement.innerHTML = '';
        iconFeedbackElement.classList.remove('text-green-500', 'text-red-500');
        iconFeedbackElement.classList.add('hidden');
      }
    }


    function ok_icon() {
      return `
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polyline points="20 6 9 17 4 12"></polyline>
        </svg>`.trim();
    }

    function not_ok_icon() {
      return `
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <line x1="18" y1="6" x2="6" y2="18"></line>
        <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>`.trim();
    }
    document.getElementById('confirm-password').addEventListener('input', handle_password_confirm_change);
    function handle_password_confirm_change(event) {
      const password = document.getElementById('password').value;
      const password_confirm = event.target.value;
      if (password !== password_confirm) {
        console.log('passwords do not match');
        const confirm_password_icon_feedback = document.getElementById('confirm-password-icon-feedback');
        confirm_password_icon_feedback.innerHTML = not_ok_icon();
        confirm_password_icon_feedback.classList.remove('text-green-500');
        confirm_password_icon_feedback.classList.add('text-red-500');
        confirm_password_icon_feedback.classList.remove('hidden');
      }
      else {
        console.log('passwords match');
        const confirm_password_icon_feedback = document.getElementById('confirm-password-icon-feedback');
        confirm_password_icon_feedback.innerHTML = "";
        confirm_password_icon_feedback.classList.remove('text-red-500');
        confirm_password_icon_feedback.classList.add('hidden');
      }
    }
  })
</script>
{% endblock %}


{% block admin_javascript %}
{% endblock %}