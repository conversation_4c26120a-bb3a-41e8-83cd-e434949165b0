{% extends "layouts/admin_base.html" %}

{% block admin_content %}
<section class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 flex flex-col py-8 px-4 sm:px-6 lg:px-8 h-full overflow-y-auto">
    <h1 class="text-3xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-indigo-500 to-purple-600 mb-8">{{ _('Security Settings') }}</h1>
    
    {% if message %}
        <div id="notification" class="mb-4 p-4 rounded-md {% if status and status == 'success' %}bg-green-500 text-white{% else %}bg-red-100 text-red-700{% endif %} relative">
            {{ message }}
            <button onclick="dismissNotification()" class="absolute top-2 right-2 text-white hover:text-gray-200">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    {% endif %}

    <form method="POST" action="{{ url_for('update_entire_config') }}" class="space-y-8" onsubmit="return validateForm()">
        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-8 transition-all duration-300 hover:shadow-xl">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">{{ _('Login Rate Limit') }}</h2>
            <div class="flex items-center space-x-4">
                <label for="login_rate_limit_value" class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ _('Limit:') }}</label>
                <input type="number" name="login_rate_limit_value" id="login_rate_limit_value" value="{{ config.login_rate_limit.limit.split('/')[0] }}" min="1" class="w-20 mt-1 px-4 py-2 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white transition-all duration-300 ease-in-out" onchange="updateMaxAttempts(this.value)">
                <select name="login_rate_limit_unit" id="login_rate_limit_unit" class="mt-1 px-4 py-2 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white transition-all duration-300 ease-in-out">
                    <option value="second" {% if config.login_rate_limit.limit.endswith('/second') %}selected{% endif %}>{{ _('per second') }}</option>
                    <option value="minute" {% if config.login_rate_limit.limit.endswith('/minute') %}selected{% endif %}>{{ _('per minute') }}</option>
                    <option value="hour" {% if config.login_rate_limit.limit.endswith('/hour') %}selected{% endif %}>{{ _('per hour') }}</option>
                    <option value="day" {% if config.login_rate_limit.limit.endswith('/day') %}selected{% endif %}>{{ _('per day') }}</option>
                </select>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-xl p-8 transition-all duration-300 hover:shadow-xl">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">{{ _('Failed Login Attempts') }}</h2>
            <div class="space-y-6">
                <div class="flex items-center">
                    <label class="inline-flex items-center cursor-pointer">
                        <input type="checkbox" name="failed_login_attempts_enabled" id="failed_login_attempts_enabled" {% if config.failed_login_attempts.enabled %}checked{% endif %} class="sr-only peer">
                        <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600"></div>
                        <span class="ml-3 text-sm font-medium text-gray-900 dark:text-gray-300">{{ _('Enable Failed Login Attempts Protection') }}</span>
                    </label>
                </div>
                <div class="flex items-center space-x-4">
                    <label for="cooldown_time" class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ _('Cooldown Time:') }}</label>
                    <input type="number" name="cooldown_time" id="cooldown_time" value="{{ config.failed_login_attempts.cooldown_time.duration }}" min="1" class="mt-1 px-4 py-2 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-24 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white transition-all duration-300 ease-in-out">
                    <select name="cooldown_time_unit" id="cooldown_time_unit" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white transition-all duration-300 ease-in-out">
                        <option value="minutes" {% if config.failed_login_attempts.cooldown_time.unit == 'minutes' %}selected{% endif %}>{{ _('Minutes') }}</option>
                        <option value="hours" {% if config.failed_login_attempts.cooldown_time.unit == 'hours' %}selected{% endif %}>{{ _('Hours') }}</option>
                    </select>
                </div>
                <div class="flex items-center space-x-4">
                    <label for="max_attempts" class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ _('Max Attempts Before Limit:') }}</label>
                    <input type="number" name="max_attempts_before_limit" id="max_attempts" value="{{ config.failed_login_attempts.max_attempts_before_limit }}" min="1" class="mt-1 px-4 py-2 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-24 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white transition-all duration-300 ease-in-out" onchange="updateLoginRateLimit(this.value)" readonly>
                </div>
            </div>
        </div>

        <div class="flex justify-end">
            <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300 ease-in-out transform hover:-translate-y-1 hover:shadow-lg">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path></svg>
                {{ _('Save Changes') }}
            </button>
        </div>
    </form>
</section>

<script>
function updateMaxAttempts(value) {
    document.getElementById('max_attempts').value = value;
}

function updateLoginRateLimit(value) {
    document.getElementById('login_rate_limit_value').value = value;
}

function validateForm() {
    var loginRateLimit = document.getElementById('login_rate_limit_value').value;
    var cooldownTime = document.getElementById('cooldown_time').value;
    var maxAttempts = document.getElementById('max_attempts').value;

    if (loginRateLimit <= 0 || cooldownTime <= 0 || maxAttempts <= 0) {
        showAlert('{{ _("All numeric values must be greater than zero.") }}', 'error');
        return false;
    }

    if (loginRateLimit != maxAttempts) {
        showAlert('{{ _("Login rate limit and max attempts before limit must be the same.") }}', 'error');
        return false;
    }

    return true;
}

function dismissNotification() {
    var notification = document.getElementById('notification');
    if (notification) {
        notification.style.display = 'none';
    }
}

// Auto-hide notification after 5 seconds
window.addEventListener('load', function() {
    var notification = document.getElementById('notification');
    if (notification) {
        setTimeout(function() {
            notification.style.display = 'none';
        }, 5000);
    }
});
</script>
{% endblock %}