{% extends "layouts/admin_base.html" %}

{% block admin_content %}
<section class="dark:bg-gray-800 flex flex-col py-4 px-4 sm:px-6 lg:px-8 h-full overflow-y-auto">
    <div class="mb-4 flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">{{ _("Scheduled Tasks Monitor") }}</h1>
        <a href="{{ url_for('tasks_monitor') }}"
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            {{ _("Monitor Tasks") }}
        </a>
    </div>

    <h1 class="font-bold text-4xl text-white mb-4">{{ _("Create a New Task") }}</h1>

    <!-- Error Message -->
    {% if error %}
    <div id="error-message" class="bg-red-500 text-white p-4 mb-4 rounded-lg">
        {{ error }}
    </div>
    {% endif %}

    <form method="post" class="bg-gray-900 p-6 rounded-lg shadow-md" id="create-task-form">

        <!-- Task Name -->
        <div class="mb-4">
            <label for="name" class="block text-white text-sm font-bold mb-2">{{ _("Task Name:") }}</label>
            <input type="text" id="name" name="name"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline dark:bg-gray-700 dark:border-gray-600 dark:text-white">
            <p id="name-error" class="text-red-500 text-sm hidden">{{ _("Task name is required.") }}</p>
        </div>

        <!-- Description -->
        <div class="mb-4">
            <label for="description" class="block text-white text-sm font-bold mb-2">{{ _("Description:") }}</label>
            <textarea id="description" name="description" rows="3"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline dark:bg-gray-700 dark:border-gray-600 dark:text-white"></textarea>
            <p id="description-error" class="text-red-500 text-sm hidden">{{ _("Description is required.") }}</p>
        </div>

        <!-- Frequency -->
        <div class="mb-4">
            <label for="frequency" class="block text-white text-sm font-bold mb-2">{{ _("Frequency:") }}</label>
            <select id="frequency" name="frequency"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                {% for freq in scheduleFrequency %}
                <option value="{{ freq }}">{{ freq }}</option>
                {% endfor %}
            </select>
            <p id="frequency-error" class="text-red-500 text-sm hidden">{{ _("Frequency is required.") }}</p>
        </div>

        <!-- Indexing Type -->
        <div class="mb-4">
            <label for="indexing_type" class="block text-white text-sm font-bold mb-2">{{ _("Indexing Type:") }}</label>
            <select id="indexing_type" name="indexing_type"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                {% for index in indexingType %}
                <option value="{{ index }}">{{ index }}</option>
                {% endfor %}
            </select>
            <p id="indexing-type-error" class="text-red-500 text-sm hidden">{{ _("Indexing type is required.") }}</p>
        </div>

        <!-- Time -->
        <div class="mb-4">
            <label for="time" class="block text-white text-sm font-bold mb-2">{{ _("Time:") }}</label>
            <input type="datetime-local" id="time" name="time"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline dark:bg-gray-700 dark:border-gray-600 dark:text-white">
            <p id="time-error" class="text-red-500 text-sm hidden">{{ _("Time is required.") }}</p>
        </div>

        <!-- Website -->
        <div id="website_field" class="mb-4 transition-opacity duration-500 ease-in-out">
            <label for="website_id" class="block text-white text-sm font-bold mb-2">{{ _("Website:") }}</label>
            <select id="website_id" name="website_id"
                class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                <option value="">{{ _("Select Website") }}</option>
                {% for website in websites %}
                <option value="{{ website.website_id }}">{{ website.website_url }}</option>
                {% endfor %}
            </select>
            <p id="website-error" class="text-red-500 text-sm hidden">{{ _("Website is required when Indexing Type is not 'Full'.") }}</p>
        </div>

        <!-- Hour interval -->
        <div class="mb-4" id="hours_interval_field">
            <label for="hours_interval" class="block text-white text-sm font-bold mb-2">{{ _("Hour Interval:") }}</label>
            <input type="text" id="hours_interval" name="hours_interval" pattern="\d*" inputmode="numeric" minlength="1"
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                title="{{ _('Task hours interval must be a positive integer.') }}" />
            <p id="hours_interval-error" class="text-red-500 text-sm hidden">{{ _("Task hours interval must be a positive integer.") }}</p>
        </div>

        <!-- Submit Button -->
        <button type="submit" id="create-task-button"
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-500 ease-in-out transform hover:scale-105">
            {{ _("Create Task") }}
        </button>
    </form>
</section>
<script>
    document.addEventListener("DOMContentLoaded", function () {
        const form = document.getElementById("create-task-form");
        const createTaskButton = form.querySelector('#create-task-button');
        const indexingTypeSelect = document.getElementById("indexing_type");
        const frequencySelect = document.getElementById("frequency");
        const websiteField = document.getElementById("website_field");
        const websiteSelect = document.getElementById("website_id");

        const hoursIntervalInput = document.getElementById('hours_interval');
        const hoursIntervalError = document.getElementById('hours_interval-error');
        const hoursIntervalField = document.getElementById('hours_interval_field');

        // Prevent any input that's not a digit
        hoursIntervalInput.addEventListener('keydown', function (event) {
            // Allow only backspace, tab, arrow keys, or numbers
            if (!/^\d$/.test(event.key) && !['Backspace', 'Tab', 'ArrowLeft', 'ArrowRight', 'Delete'].includes(event.key)) {
                event.preventDefault();
            }
        });

        // Validate the input on the fly
        hoursIntervalInput.addEventListener('input', function () {
            const value = hoursIntervalInput.value;

            // Check if the current value is a positive integer
            if (/^\d+$/.test(value)) {
                // Hide the error message if the value is a valid positive integer
                hoursIntervalError.classList.add('hidden');
            } else {
                // Show the error message if the value is not valid
                hoursIntervalError.classList.remove('hidden');
            }
        });

        hoursIntervalInput.addEventListener('blur', function () {
            const value = hoursIntervalInput.value;
            if (!value || !parseInt(value) || parseInt(value) <= 0) {
                hoursIntervalError.classList.remove('hidden');
                createTaskButton.disabled = true;
            } else {
                hoursIntervalError.classList.add('hidden');
                createTaskButton.disabled = false;
            }
        });

        function updateWebsiteField() {
            if (indexingTypeSelect.value.toLowerCase() === "full") {
                websiteField.classList.add("hidden", "pointer-events-none");
                websiteSelect.value = "";
            } else {
                websiteField.classList.remove("hidden", "pointer-events-none");
            }
        }

        function updateHoursIntervalField() {
            if (frequencySelect.value.toLowerCase() === "every_x_hours") {
                hoursIntervalField.classList.remove("hidden", "pointer-events-none");
            } else {
                hoursIntervalField.classList.add("hidden", "pointer-events-none");
            }
        }

        indexingTypeSelect.addEventListener("change", updateWebsiteField);
        updateWebsiteField();  // Initial check based on default selection

        frequencySelect.addEventListener("change", updateHoursIntervalField);
        updateHoursIntervalField();

        form.addEventListener("submit", function (event) {
            event.preventDefault();

            if (validateForm()) {
                const formData = new FormData(form);

                const timeInput = formData.get('time');

                if (!timeInput) {
                    showAlert('{{ _("Please enter a new time for the task") }}', 'error');
                    return;
                }

                // Create a Date object from the input
                const taskDate = new Date(timeInput);

                // Convert the date to an ISO string with timezone offset
                const isoString = taskDate.toISOString();

                createTaskButton.disabled = true;
                createTaskButton.textContent = "{{ _('Creating...') }}";

                // Set the ISO string as the time value
                formData.set('time', isoString);

                // Handle the optional website_id field
                if (formData.get('indexing_type').toLowerCase() === 'full') {
                    formData.delete('website_id');
                }

                // handle the optional hours_interval field
                if (formData.get('frequency').toLowerCase() !== 'every_x_hours') {
                    formData.delete('hours_interval');
                }

                fetch('/admin/tasks/create', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Accept': 'application/json',
                    }
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Handle successful response
                        showMessage('{{ _("Task created successfully!") }}', 'success');
                        form.reset();
                        updateWebsiteField();  // Reset the website field visibility
                    })
                    .catch(error => {
                        // Handle errors
                        console.error('Error:', error);
                        showMessage('{{ _("Failed to create task. Please try again.") }}', 'error');
                    })
                    .finally(() => {
                        createTaskButton.disabled = false;
                        createTaskButton.textContent = "{{ _('Create Task') }}";
                    });
            }
        });

        function showMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.textContent = message;
            messageDiv.className = `p-4 rounded-lg mb-4 ${type === 'success' ? 'bg-green-500' : 'bg-red-500'} text-white`;

            const container = document.querySelector('section');
            container.insertBefore(messageDiv, form);

            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }

        function validateForm() {
            let valid = true;
            const name = document.getElementById("name");
            const description = document.getElementById("description");
            const frequency = document.getElementById("frequency");
            const indexingType = document.getElementById("indexing_type");
            const time = document.getElementById("time");
            const website = document.getElementById("website_id");
            const hours_interval = document.getElementById("hours_interval");

            if (!name.value.trim()) {
                document.getElementById("name-error").classList.remove("hidden");
                valid = false;
            } else {
                document.getElementById("name-error").classList.add("hidden");
            }

            if (!description.value.trim()) {
                document.getElementById("description-error").classList.remove("hidden");
                valid = false;
            } else {
                document.getElementById("description-error").classList.add("hidden");
            }

            if (!frequency.value) {
                document.getElementById("frequency-error").classList.remove("hidden");
                valid = false;
            } else {
                document.getElementById("frequency-error").classList.add("hidden");
            }

            if (!indexingType.value) {
                document.getElementById("indexing-type-error").classList.remove("hidden");
                valid = false;
            } else {
                document.getElementById("indexing-type-error").classList.add("hidden");
            }

            if (!time.value) {
                document.getElementById("time-error").classList.remove("hidden");
                valid = false;
            } else {
                document.getElementById("time-error").classList.add("hidden");
            }

            if (indexingType.value.toLowerCase() !== "full" && !website.value) {
                document.getElementById("website-error").classList.remove("hidden");
                valid = false;
            } else {
                document.getElementById("website-error").classList.add("hidden");
            }
            if (frequency.value.toLowerCase() === "every_x_hours" && (!hours_interval.value || !parseInt(hours_interval.value) || parseInt(hours_interval.value) <= 0)) {
                hoursIntervalError.classList.remove('hidden');
                valid = false;
            }
            else {
                hoursIntervalError.classList.add('hidden');
            }

            return valid;
        }
    });
</script>
{% endblock %}