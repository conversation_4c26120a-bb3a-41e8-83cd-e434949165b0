{% extends "layouts/admin_base.html" %}

{% block admin_content %}
<section class="dark:bg-gray-800 flex flex-col py-4 px-4 sm:px-6 lg:px-8 h-full overflow-y-auto">
    <div class="mb-4 flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-100">{{ _('Scheduled Tasks Monitor') }}</h1>
        <div class="mt-6 flex gap-4">
            <a href="{{ url_for('create_task') }}"
                class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded transition duration-150 ease-in-out inline-flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                </svg>
                {{ _('Add New Task') }}
            </a>
            <button id="refreshSchedulerButton"
                class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded transition duration-150 ease-in-out inline-flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                </svg>
                {{ _('Refresh Scheduler') }}
            </button>
        </div>
    </div>

    <div class="overflow-x-auto shadow-md sm:rounded-lg">
        {% if tasks %}
        <table class="min-w-full bg-white dark:bg-gray-700 text-white border-collapse">
            <thead>
                <tr class="text-left bg-gray-800">
                    <th class="py-3 px-4 border-b border-gray-600">{{ _('Task ID') }}</th>
                    <th class="py-3 px-4 border-b border-gray-600">{{ _('Name') }}</th>
                    <th class="py-3 px-4 border-b border-gray-600">{{ _('Frequency') }}</th>
                    <th class="py-3 px-4 border-b border-gray-600">{{ _('Status') }}</th>
                    <th class="py-3 px-4 border-b border-gray-600">{{ _('Actions') }}</th>
                </tr>
            </thead>
            <tbody id="tasksTableBody">
                {% for task in tasks %}
                <tr id="task-row-{{ task.task_id }}" class="border-b border-gray-700 hover:bg-gray-600">
                    <td class="py-2 px-4">{{ task.task_id }}</td>
                    <td class="py-2 px-4">{{ task.name }}</td>
                    <td class="py-2 px-4">{{ task.frequency }}</td>
                    <td class="py-2 px-4" id="task-status-{{ task.task_id }}">{{ _('Active') if task.is_active else _('Inactive') }}</td>
                    <td class="py-2 px-4 flex gap-2">
                        <button class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-1 px-2 rounded transition duration-150 ease-in-out" onclick="openDetailsModal({{ task.task_id }})">{{ _('Details') }}</button>
                        {% if task.is_active %}
                        <button class="bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-1 px-2 rounded transition duration-150 ease-in-out" onclick="deactivateTask({{ task.task_id }})">{{ _('Deactivate') }}</button>
                        {% else %}
                        <button class="bg-green-500 hover:bg-green-600 text-white font-medium py-1 px-2 rounded transition duration-150 ease-in-out" onclick="activateTask({{ task.task_id }})">{{ _('Activate') }}</button>
                        {% endif %}
                        <button class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-1 px-2 rounded transition duration-150 ease-in-out" onclick="openModal({{ task.task_id }})">{{ _('Reschedule') }}</button>
                        <button class="bg-red-500 hover:bg-red-600 text-white font-medium py-1 px-2 rounded transition duration-150 ease-in-out" onclick="deleteTask({{ task.task_id }})">{{ _('Delete') }}</button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div class="flex flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gray-800 rounded-lg">
            <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-300">{{ _('No tasks') }}</h3>
                <p class="mt-1 text-sm text-gray-400">{{ _('Get started by creating a new task.') }}</p>
                <div class="mt-6">
                    <a href="{{ url_for('create_task') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" />
                        </svg>
                        {{ _('Add new task') }}
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</section>

<!-- Modal for rescheduling tasks -->
<div id="taskModal" class="hidden fixed inset-0 z-10 overflow-y-auto bg-black bg-opacity-50">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div
            class="bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 p-6 rounded-lg shadow-xl w-full max-w-lg">
            <h2 id="modalTitle" class="text-lg font-semibold mb-4">{{ _('Reschedule Task') }}</h2>
            <form id="taskForm">
                <div class="mb-4">
                    <label for="taskTime" class="block text-sm font-medium text-gray-900 dark:text-gray-200">{{ _('New Time') }}</label>
                    <input type="datetime-local" id="taskTime" name="taskTime"
                        class="mt-1 p-2 border border-gray-300 rounded w-full dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300">
                </div>
                <div class="flex justify-end space-x-2">
                    <button type="button" id="cancelButton"
                        class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded transition duration-150 ease-in-out">{{ _('Cancel') }}</button>
                    <button type="submit"
                        class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded transition duration-150 ease-in-out">{{ _('Save') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal for viewing task details -->
<div id="taskDetailsModal" class="hidden fixed inset-0 z-50 overflow-y-auto" aria-labelledby="detailsModalTitle"
    role="dialog">
    <div class="flex items-center justify-center min-h-screen px-4 text-center">
        <div
            class="inline-block align-bottom bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-gray-900 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 id="detailsModalTitle" class="text-lg leading-6 font-medium text-gray-100"
                            aria-describedby="modal-headline">
                            {{ _('Task Details') }}
                        </h3>
                        <div class="mt-2">
                            <div id="taskDetailsContent" class="text-sm text-gray-300 space-y-2">
                                <!-- Task details will be dynamically populated here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-700 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                <button type="button" id="closeDetailsButton"
                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-gray-600 text-base font-medium text-white hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:ml-3 sm:w-auto sm:text-sm">
                    {{ _('Close') }}
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        document.getElementById("refreshSchedulerButton").addEventListener("click", function () {
            refreshScheduler();
        });

        document.getElementById("taskForm").addEventListener("submit", function (event) {
            event.preventDefault();
            rescheduleTask();
        });

        document.getElementById("cancelButton").addEventListener("click", function () {
            closeModal();
        });

        document.getElementById("closeDetailsButton").addEventListener("click", function () {
            closeDetailsModal();
        });
    });

    function openModal(taskId) {
        document.getElementById('modalTitle').textContent = '{{ _("Reschedule Task") }}';
        document.getElementById('taskTime').value = '';

        document.getElementById('taskForm').dataset.taskId = taskId;

        document.getElementById('taskModal').classList.remove('hidden');
    }

    function closeModal() {
        document.getElementById('taskModal').classList.add('hidden');
    }

    function rescheduleTask() {
        const taskId = document.getElementById('taskForm').dataset.taskId;
        let taskTime = document.getElementById('taskTime').value;

        if (!taskTime) {
            showAlert('{{ _("Please enter a new time for the task") }}', 'error');
            return;
        }
        // if (new Date(taskTime) < new Date()) {
        //     showAlert('{{ _("Task time cannot be in the past") }}', 'error');
        //     return;
        // }

        taskTime = new Date(taskTime).toISOString();

        fetch(`/admin/tasks/${taskId}/reschedule`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ time: taskTime })
        })
            .then(response => {
                if (!response.ok) {
                    // Throw the response object for later handling
                    throw response;
                }
                return response.json();
            })
            .then(data => {
                closeModal();
                const next_run = data.task['next_run'];
                showAlert('{{ _("Task rescheduled successfully. Next run: ") }}' + next_run, 'success');
            })
            .catch(responseOrError => {
                // Check if the caught object is a Response instance
                if (responseOrError instanceof Response) {
                    // Handle JSON responses from the server
                    responseOrError.json().then(err => {
                        console.error('Error rescheduling task:', err);
                        showAlert(err, 'error');
                    }).catch(jsonError => {
                        // Handle cases where the response is not JSON
                        console.error('Error parsing JSON from response:', jsonError);
                        showAlert('{{ _("An error occurred while processing the server response") }}', 'error');
                    });
                } else {
                    // Handle generic errors (e.g., network errors)
                    console.error('Error rescheduling task:', responseOrError);
                    showAlert('{{ _("An error occurred while rescheduling the task") }}', 'error');
                }
            });
    }

    function deactivateTask(taskId) {
        fetch(`/admin/tasks/${taskId}/deactivate`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('{{ _("Failed to deactivate task") }}');
                }
                return response.json();
            })
            .then(data => {
                document.getElementById(`task-status-${taskId}`).innerText = '{{ _("Inactive") }}';
                const taskRow = document.querySelector(`#task-row-${taskId} td:last-child`);
                taskRow.innerHTML = `
            <button class="bg-green-500 hover:bg-green-600 text-white font-medium py-1 px-2 rounded transition duration-150 ease-in-out" onclick="activateTask(${taskId})">{{ _('Activate') }}</button>
            ${getRemainingActionsHTML(taskId)}
        `;
                showAlert('{{ _("Task deactivated successfully") }}', 'success');
            })
            .catch(error => {
                console.error('Error deactivating task:', error);
                showAlert('{{ _("An error occurred while deactivating the task") }}', 'error');
            });
    }

    function activateTask(taskId) {
        fetch(`/admin/tasks/${taskId}/activate`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('{{ _("Failed to activate task") }}');
                }
                return response.json();
            })
            .then(data => {
                document.getElementById(`task-status-${taskId}`).innerText = '{{ _("Active") }}';
                const taskRow = document.querySelector(`#task-row-${taskId} td:last-child`);
                taskRow.innerHTML = `
            <button class="bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-1 px-2 rounded transition duration-150 ease-in-out" onclick="deactivateTask(${taskId})">{{ _('Deactivate') }}</button>
            ${getRemainingActionsHTML(taskId)}
        `;
                showAlert('{{ _("Task activated successfully") }}', 'success');
            })
            .catch(error => {
                console.error('Error activating task:', error);
                showAlert('{{ _("An error occurred while activating the task") }}', 'error');
            });
    }

    function getRemainingActionsHTML(taskId) {
        return `
        <button class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-1 px-2 rounded transition duration-150 ease-in-out" onclick="openDetailsModal(${taskId})">{{ _('Details') }}</button>
        <button class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-1 px-2 rounded transition duration-150 ease-in-out" onclick="openModal(${taskId})">{{ _('Reschedule') }}</button>
        <button class="bg-red-500 hover:bg-red-600 text-white font-medium py-1 px-2 rounded transition duration-150 ease-in-out" onclick="deleteTask(${taskId})">{{ _('Delete') }}</button>
    `;
    }


    function deleteTask(taskId) {
        fetch(`/admin/tasks/${taskId}/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new response;
                }
                return response.json();
            })
            .then(data => {
                document.getElementById(`task-row-${taskId}`).remove();
                showAlert('{{ _("Task deleted successfully") }}', 'success');
            })
            .catch(error => {
                console.error('Error deleting task:', error);
                showAlert('{{ _("An error occurred while deleting the task") }}', 'error');
            });
    }

    function refreshScheduler() {
        fetch('/admin/scheduler/refresh', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw response;  // Throw the whole response object
                }
                return response.json();
            })
            .then(data => {
                showAlert('{{ _("Scheduler refreshed successfully") }}', 'success');
            })
            .catch(response => {
                if (response instanceof Response) {
                    // Handle responses thrown due to being not okay
                    response.json().then(err => {
                        console.error(err);
                        showAlert(`{{ _("An error occurred while refreshing the scheduler: ") }}${err.message}`, 'error');
                    }).catch(jsonError => {
                        // Handle JSON parsing errors
                        console.error('Error parsing JSON from response:', jsonError);
                        showAlert('{{ _("An error occurred while processing the server response") }}', 'error');
                    });
                } else {
                    // Handle other errors (e.g., network errors)
                    console.error(response);
                    showAlert('{{ _("An error occurred while refreshing the scheduler") }}', 'error');
                }
            });
    }

    function openDetailsModal(taskId) {
        fetch(`/admin/tasks/${taskId}/view`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('{{ _("Failed to fetch task details") }}');
                }
                return response.json();
            })
            .then(task => {
                const booleanBadge = (value) => value ?
                    '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-500 text-white">{{ _("Yes") }}</span>' :
                    '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-500 text-white">{{ _("No") }}</span>';

                document.getElementById('detailsModalTitle').textContent = `{{ _("Task Details: ") }}${task.name}`;
                document.getElementById('taskDetailsContent').innerHTML = `
                <div class="space-y-4 p-4 bg-gray-900 rounded-lg">
                    <div class="flex justify-between">
                        <span class="font-semibold text-gray-100">{{ _('Task ID:') }}</span>
                        <span>${task.task_id}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-semibold text-gray-100">{{ _('Name:') }}</span>
                        <span>${task.name}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-semibold text-gray-100">{{ _('Description:') }}</span>
                        <span>${task.description}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-semibold text-gray-100">{{ _('Frequency:') }}</span>
                        <span>${task.frequency}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-semibold text-gray-100">{{ _('Indexing Type:') }}</span>
                        <span>${task.indexing_type}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-semibold text-gray-100">{{ _('Time:') }}</span>
                        <span>${new Date(task.time).toLocaleString()}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-semibold text-gray-100">{{ _('Active:') }}</span>
                        <span>${booleanBadge(task.is_active)}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-semibold text-gray-100">{{ _('Scheduled:') }}</span>
                        <span>${booleanBadge(task.is_scheduled)}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-semibold text-gray-100">{{ _('Hours Interval:') }}</span>
                        <span>${task.hours_interval || '{{ _("N/A") }}'}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-semibold text-gray-100">{{ _('Last Run:') }}</span>
                        <span>${task.last_run ? new Date(task.last_run).toLocaleString() : '{{ _("N/A") }}'}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-semibold text-gray-100">{{ _('Next Run:') }}</span>
                        <span>${task.next_run ? new Date(task.next_run).toLocaleString() : '{{ _("N/A") }}'}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-semibold text-gray-100">{{ _('Website ID:') }}</span>
                        <span>${task.website_id || '{{ _("N/A") }}'}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-semibold text-gray-100">{{ _('Created At:') }}</span>
                        <span>${new Date(task.created_at).toLocaleString()}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-semibold text-gray-100">{{ _('Updated At:') }}</span>
                        <span>${new Date(task.updated_at).toLocaleString()}</span>
                    </div>
                </div>
            `;
                document.getElementById('taskDetailsModal').classList.remove('hidden');
            })
            .catch(error => {
                console.error('Error fetching task details:', error);
                showAlert('{{ _("An error occurred while fetching task details") }}', 'error');
            });
    }


    document.getElementById('closeDetailsButton').addEventListener('click', () => {
        document.getElementById('taskDetailsModal').classList.add('hidden');
    });

    function closeDetailsModal() {
        document.getElementById('taskDetailsModal').classList.add('hidden');
    }

</script>
{% endblock %}