{% extends "layouts/admin_base.html" %}

{% block admin_content %}
<section class="dark:bg-gray-800 flex flex-col py-4 px-4 sm:px-6 lg:px-8 h-full overflow-y-auto">
    <h1 class="font-bold text-4xl mb-6 text-gray-900 dark:text-white">{{ _('Search Filters Base Template') }}</h1>

    <div class="bg-white dark:bg-gray-700 rounded-lg shadow-md p-6 mb-6">
        <h2 class="text-2xl font-semibold mb-4 text-gray-800 dark:text-white">{{ _('Current Template Filters') }}</h2>
        <div class="overflow-x-auto">
            <table id="template-filter-list" class="min-w-full divide-y divide-gray-200 dark:divide-gray-600"
                data-headerkeys="{{ ','.join(sample_filters[0].keys()) }}">
                <thead class="bg-gray-50 dark:bg-gray-800">
                    <tr>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            {{ _('Display Name') }}</th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            {{ _('Filter Type ID') }}</th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            {{ _('Field') }}</th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            {{ _('Type') }}</th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            {{ _('Order') }}</th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            {{ _('Actions') }}</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-700 dark:divide-gray-600">
                    {% for filter in sample_filters %}
                    <tr data-filter-id="{{ filter.id }}">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300 display-name">{{
                            filter.displayName }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300 filter-type-id">
                            {{ filter.filter_type_id }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300 field">{{
                            filter.field }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300 type">{{
                            filter.type }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300 order">{{
                            filter.order }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button type="button"
                                class="edit-filter bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 ease-in-out">
                                {{ _('Edit') }}
                            </button>
                            <button type="button"
                                class="delete-filter bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 ease-in-out ml-2">
                                {{ _('Delete') }}
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <div class="flex space-x-4 mb-6">
        <button id="add-template-filter"
            class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 ease-in-out">
            {{ _('Add New Filter') }}
        </button>
        <button id="save-template"
            class="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 ease-in-out">
            {{ _('Save Template') }}
        </button>
    </div>

    <div id="template-filter-form" class="hidden bg-white dark:bg-gray-700 rounded-lg shadow-md p-8">
        <h2 id="form-title" class="text-2xl font-semibold mb-6 text-gray-800 dark:text-white">{{ _('Add/Edit Template Filter') }}</h2>
        <form id="filter-form" class="space-y-6">
            <input type="hidden" name="totalElements" id="totalElements" value="{{ sample_filters | length + 1 }}">
            <div>
                <label for="displayName" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ _('Display Name') }}</label>
                <input type="text" id="displayName" name="displayName" required
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-600 dark:border-gray-500 dark:text-white text-lg py-2 px-4">
            </div>
            <div>
                <label for="filterTypeId" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ _('Filter Type ID') }}</label>
                <input type="text" id="filterTypeId" name="filterTypeId" required
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-600 dark:border-gray-500 dark:text-white text-lg py-2 px-4">
            </div>
            <div>
                <label for="field" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ _('Field') }}</label>
                <input type="text" id="field" name="field" required
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-600 dark:border-gray-500 dark:text-white text-lg py-2 px-4">
            </div>
            <div>
                <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ _('Type') }}</label>
                <select id="type" name="type" required
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-600 dark:border-gray-500 dark:text-white text-lg py-2 px-4">
                    <option value="checkbox">{{ _('Checkbox') }}</option>
                    <option value="input_range">{{ _('Input Range') }}</option>
                </select>
            </div>
            <div>
                <label for="order" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ _('Order') }}</label>
                <input type="number" id="order" name="order" value="{{ sample_filters|length + 1 }}" disabled
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-600 dark:border-gray-500 dark:text-white text-lg py-2 px-4">
            </div>
            <div class="flex space-x-4">
                <button type="submit"
                    class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-lg focus:outline-none focus:shadow-outline transition duration-150 ease-in-out">
                    {{ _('Save Filter') }}
                </button>
                <button type="button" id="cancel-filter"
                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded-lg focus:outline-none focus:shadow-outline transition duration-150 ease-in-out">
                    {{ _('Cancel') }}
                </button>
            </div>
        </form>
    </div>
</section>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const addButton = document.getElementById('add-template-filter');
        const saveButton = document.getElementById('save-template');
        const cancelButton = document.getElementById('cancel-filter');
        const filterFormContainer = document.getElementById('template-filter-form');
        const filterForm = document.getElementById('filter-form');
        const formTitle = document.getElementById('form-title');

        let formMode = 'add'; // Tracks whether the form is in 'add' or 'edit' mode
        let editingFilterId = null; // Tracks the ID of the filter being edited

        addButton.addEventListener('click', function () {
            formMode = 'add';
            formTitle.textContent = '{{ _("Add Template Filter") }}';
            filterForm.reset();
            filterForm.order.value = document.getElementById('totalElements').value;
            editingFilterId = null;
            filterFormContainer.classList.remove('hidden');
        });

        cancelButton.addEventListener('click', function () {
            filterFormContainer.classList.add('hidden');
        });

        filterForm.addEventListener('submit', function (event) {
            event.preventDefault(); // Prevent the form from submitting via the browser

            const formData = {
                displayName: document.getElementById('displayName').value,
                filterTypeId: document.getElementById('filterTypeId').value,
                field: document.getElementById('field').value,
                type: document.getElementById('type').value,
                order: document.getElementById('order').value
            };

            if (formMode === 'add') {
                addNewFilter(formData);
            } else if (formMode === 'edit') {
                updateFilter(editingFilterId, formData);
            }

            filterFormContainer.classList.add('hidden');
            filterForm.reset();
        });

        function addNewFilter(filterData) {
            const filterList = document.getElementById('template-filter-list').querySelector('tbody');
            const newRow = document.createElement('tr');

            const newFilterId = Date.now(); // Use current timestamp as a unique ID for the new filter

            newRow.dataset.filterId = newFilterId;
            newRow.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300 display-name">${filterData.displayName}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300 filter-type-id">${filterData.filterTypeId}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300 field">${filterData.field}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300 type">${filterData.type}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300 order">${filterList.children.length + 1}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button type="button" class="edit-filter bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 ease-in-out">
                        {{ _('Edit') }}
                    </button>
                    <button type="button" class="delete-filter bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 ease-in-out ml-2">
                        {{ _('Delete') }}
                    </button>
                </td>
            `;
            filterList.appendChild(newRow);

            // Add event listeners to the new edit and delete buttons
            newRow.querySelector('.edit-filter').addEventListener('click', function () {
                editFilter(newRow);
            });
            newRow.querySelector('.delete-filter').addEventListener('click', function () {
                deleteFilter(newRow);
            });

            // Update the total elements count
            document.getElementById('totalElements').value = filterList.children.length + 1;
        }

        function updateFilter(filterId, filterData) {
            const existingRow = document.querySelector(`[data-filter-id="${filterId}"]`);
            existingRow.querySelector('.display-name').textContent = filterData.displayName;
            existingRow.querySelector('.filter-type-id').textContent = filterData.filterTypeId;
            existingRow.querySelector('.field').textContent = filterData.field;
            existingRow.querySelector('.type').textContent = filterData.type;
            // Order field is not editable, so no need to update it
        }

        function editFilter(row) {
            formMode = 'edit';
            formTitle.textContent = '{{ _("Edit Template Filter") }}';
            editingFilterId = row.dataset.filterId;

            document.getElementById('displayName').value = row.querySelector('.display-name').textContent.trim();
            document.getElementById('filterTypeId').value = row.querySelector('.filter-type-id').textContent.trim();
            document.getElementById('field').value = row.querySelector('.field').textContent.trim();
            document.getElementById('type').value = row.querySelector('.type').textContent.trim();
            document.getElementById('order').value = row.querySelector('.order').textContent.trim();

            filterFormContainer.classList.remove('hidden');
        }

        // Attach edit event listeners to existing edit buttons
        document.querySelectorAll('.edit-filter').forEach(button => {
            button.addEventListener('click', function () {
                editFilter(button.closest('tr'));
            });
        });

        saveButton.addEventListener('click', async function () {
            confirmAction('{{ _("Your changes will be saved. Are you sure?") }}', saveTemplate);
        });

        async function saveTemplate() {
            const originalText = saveButton.textContent;

            try {
                // 1. Get filter data from the table
                const filters = getFiltersFromTable();
                if (!filters || filters.length === 0) {
                    showAlert('{{ _("No filters to save") }}', 'error');
                    return;
                }

                // 2. Prepare data for the server
                const dataToSend = { filters: filters };

                // indicating the saving process
                saveButton.textContent = '{{ _("Saving...") }}';

                // 3. Send data to the server (same as before)
                const response = await fetch('/admin/filter/manage', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(dataToSend)
                });

                // 4. Handle the server response (same as before)
                if (response.ok) {
                    showAlert('{{ _("Filters saved successfully") }}', 'success');
                } else {
                    console.error('Error saving filters:', response.status);
                    showAlert('{{ _("An error occurred while saving the filters") }}', 'error');
                }
            } catch (error) {
                console.error('Error saving filters:', error);
                showAlert('{{ _("An error occurred while saving the filters") }}', 'error');
            }
            finally {
                saveButton.textContent = originalText;
            }
        }

        // Function to extract filter data from the table
        function getFiltersFromTable() {
            const filters = [];
            const table = document.getElementById('template-filter-list'); // Replace with your table's ID

            // Assuming the first row contains table headers
            const headerRow = table.rows[0];
            let headerKeys = table.dataset.headerkeys;
            if (!headerKeys) {
                return null;
            }

            headerKeys = headerKeys.split(',');

            // Iterate over data rows (starting from the second row)
            for (let i = 1; i < table.rows.length; i++) {
                const row = table.rows[i];
                const filter = {};

                // Iterate over cells in the row
                for (let j = 0; j < row.cells.length - 1; j++) {
                    const headerText = headerKeys[j]; // Get header text
                    const cellValue = row.cells[j].textContent.trim(); // Get cell value

                    // Add the header/value pair to the filter object
                    filter[headerText] = cellValue;
                }

                filters.push(filter);
            }

            return filters;
        }


        // Add event listener for delete buttons
        document.querySelectorAll('.delete-filter').forEach(button => {
            button.addEventListener('click', function () {
                deleteFilter(button.closest('tr'));
            });
        });

        // Function to delete a filter
        function deleteFilter(row) {
            function _deleteFilter() {
                const filterId = row.dataset.filterId;
                const filterList = document.getElementById('template-filter-list').querySelector('tbody');
                filterList.removeChild(row);
                // Here you might also want to update the order of remaining filters
                updateFilterOrder();
            }
            confirmAction('{{ _("Are you sure you want to delete this filter?") }}', _deleteFilter);
        }

        // Function to update the order of remaining filters after deletion
        function updateFilterOrder() {
            const filterList = document.getElementById('template-filter-list').querySelector('tbody').children;
            for (let i = 0; i < filterList.length; i++) {
                filterList[i].querySelector('.order').textContent = i + 1;
            }
            // Update the hidden input that tracks total elements
            document.getElementById('totalElements').value = filterList.length + 1;
        }
    });
</script>

{% endblock %}