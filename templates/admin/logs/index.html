{% extends "layouts/admin_base.html" %}

{% block admin_content %}
<section class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 flex flex-col py-8 px-4 sm:px-6 lg:px-8 h-full overflow-y-auto">
    <h1 class="text-3xl font-extrabold mb-8 text-gray-900 dark:text-gray-100">{{ _("System Logs") }}</h1>
    
    <div class="bg-white dark:bg-gray-800 shadow-xl rounded-xl overflow-hidden">
        <div class="px-6 py-5 sm:px-8 flex justify-between items-center border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-xl leading-6 font-semibold text-gray-900 dark:text-gray-100">{{ _("Log Entries") }}</h2>
            <div class="relative">
                <select id="log-filter" class="appearance-none bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="all">{{ _("All Logs") }}</option>
                    <option value="error">{{ _("Error") }}</option>
                    <option value="warning">{{ _("Warning") }}</option>
                    <option value="info">{{ _("Info") }}</option>
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
                    <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                        <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
                    </svg>
                </div>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table id="logsTable" class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ _("Log ID") }}</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ _("Website ID") }}</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ _("Event Type") }}</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ _("Event Description") }}</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">{{ _("Created At") }}</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                </tbody>
            </table>
        </div>
    </div>
</section>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        "use strict";

        let datatable = $('#logsTable').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            ajax: async (data, callback) => {
                try {
                    const params = {
                        draw: data.draw,
                        start: data.start,
                        length: data.length,
                    };
                    const response = await fetch(`/admin/view-logs?${new URLSearchParams(params)}`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                    if (!response.ok) {
                        throw new Error(`HTTP status ${response.status}`);
                    }
                    const json = await response.json();
                    callback(json);
                } catch (error) {
                    console.error('Error fetching data:', error);
                    showAlert('An error occurred while fetching the data. Please try again later.', 'error');
                }
            },
            columns: [
                { data: "log_id" },
                { data: "website_id" },
                { data: "event_type" },
                { data: "event_description" },
                { data: "created_at" }
            ],
            dom: '<"flex flex-col sm:flex-row justify-between items-center mb-4"lf>t<"flex flex-col sm:flex-row justify-between items-center mt-4"ip>',
            language: {
                search: '',
                searchPlaceholder: "Search logs...",
                lengthMenu: '<span class="dark:text-indigo-100">Show _MENU_ entries</span>',
                info: '<span class="dark:text-indigo-100">Showing _START_ to _END_ of _TOTAL_ entries</span>',
                paginate: {
                    first: '<span class="dark:text-indigo-100">«</span>',
                    last: '<span class="dark:text-indigo-100">»</span>',
                    previous: '<span class="dark:text-indigo-100">←</span>',
                    next: '<span class="dark:text-indigo-100">→</span>'
                }
            },
            drawCallback: function (settings) {
                $('#logsTable_filter input').addClass('dark:bg-indigo-700 dark:text-indigo-100 dark:border-indigo-600 dark:focus:ring-indigo-500 dark:focus:border-indigo-500 px-4 py-2 border border-indigo-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent');
                $('#logsTable_length select').addClass('dark:bg-indigo-700 dark:text-indigo-100 dark:border-indigo-600 dark:focus:ring-indigo-500 dark:focus:border-indigo-500 px-4 py-2 border border-indigo-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent');
            },
            rowCallback: function (row, data) {
                $(row).addClass('hover:bg-indigo-50 dark:hover:bg-indigo-700 transition-colors duration-200');
            }
        });

        $('#log-filter').on('change', function() {
            datatable.ajax.reload();
        });

        $('#logsTable').on('error.dt', function (e, settings, techNote, message) {
            console.error('{{ _("An error has been reported by DataTables:") }}', message);
            showAlert('{{ _("An error occurred within the data table. Please try again later.") }}', 'error');
        });
    });
</script>

{% endblock %}