{% extends "layouts/admin_base.html" %}

{% block admin_content %}
<section class="dark:bg-gray-800 flex flex-col py-4 px-4 sm:px-6 lg:px-8 h-full overflow-y-auto">
    <div class="mb-4 flex justify-between items-center">
        <h1 class="text-2xl font-semibold text-gray-100">{{ _("Website Settings Manager") }}</h1>
    </div>

    <div class="mb-4 flex gap-4">
        <select id="userFilter" class="px-4 py-2 rounded-lg bg-gray-700 text-white">
            <option value="">All Users</option>
            {% for user in users %}
            <option value="{{ user.user_id }}" {% if user.user_id == user_id %}selected{% endif %}>{{ user.username }}</option>
            {% endfor %}
        </select>
    </div>

    <div class="overflow-x-auto shadow-md sm:rounded-lg">
        {% if websites|length == 0 %}
        <div class="text-center py-6 px-4 bg-gray-800 text-white rounded-md">
            <p class="text-lg font-medium">{{ _("No websites available for the selected user.") }}</p>
            <p class="text-sm">{{ _("Please select a different user or check back later.") }}</p>
        </div>
        {% else %}
        <table class="min-w-full bg-white dark:bg-gray-700 text-white border-collapse">
            <thead>
                <tr class="text-left bg-gray-800">
                    <th class="py-3 px-4 border-b border-gray-600">{{ _("User") }}</th>
                    <th class="py-3 px-4 border-b border-gray-600">{{ _("Website URL") }}</th>
                    <th class="py-3 px-4 border-b border-gray-600">{{ _("Website Key") }}</th>
                    <th class="py-3 px-4 border-b border-gray-600">{{ _("Active") }}</th>
                    <th class="py-3 px-4 border-b border-gray-600">{{ _("Paused") }}</th>
                    <th class="py-3 px-4 border-b border-gray-600">{{ _("Actions") }}</th>
                </tr>
            </thead>
            <tbody id="websiteTableBody">
                {% for website in websites %}
                <tr id="website-row-{{ website.website_id }}" class="border-b border-gray-700 hover:bg-gray-600">
                    <td class="py-2 px-4">{{ website.owner.username }}</td>
                    <td class="py-2 px-4">{{ website.website_url }}</td>
                    <td class="py-2 px-4">{{ website.website_key }}</td>
                    <td class="py-2 px-4">
                        <span class="px-2 inline-flex items-center justify-center text-xs leading-5 font-semibold rounded-full {{ 'bg-green-100 text-green-800' if website.is_active else 'bg-red-100 text-red-800' }} w-24 h-6">
                            {{ _('Active') if website.is_active else _('Inactive') }}
                        </span>
                    </td>
                    <td class="py-2 px-4">
                        <span class="px-2 inline-flex items-center justify-center text-xs leading-5 font-semibold rounded-full {{ 'bg-green-100 text-green-800' if not website.is_paused else 'bg-yellow-100 text-yellow-800' }} w-24 h-6">
                            {{ 'Not Paused' if not website.is_paused else 'Paused' }}
                        </span>
                    </td>
                    <td class="py-2 px-4 flex gap-2">
                        <button onclick="viewSettings('{{ website.website_id }}')" class="px-2 inline-flex items-center justify-center text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 w-24 h-6 hover:bg-blue-200 transition duration-150 ease-in-out">{{ _("View Settings") }}</button>
                        <button onclick="toggleWebsitePause('{{ website.website_id }}')" class="px-2 inline-flex items-center justify-center text-xs leading-5 font-semibold rounded-full {{ 'bg-green-100 text-green-800' if website.is_paused else 'bg-yellow-100 text-yellow-800' }} w-24 h-6 hover:{{ 'bg-green-200' if website.is_paused else 'bg-yellow-200' }} transition duration-150 ease-in-out">
                            {{ _('Resume') if website.is_paused else _('Pause') }}
                        </button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% endif %}
    </div>
</section>


<!-- Website Settings Modal -->
<div id="settingsModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
    <div class="relative top-20 mx-auto p-6 border max-w-lg shadow-lg rounded-md bg-gray-800">
        <div class="rounded-lg mb-4 flex justify-between">
            <h3 class="text-lg font-medium text-gray-100">{{ _("Website Settings") }}</h3>
            <div class="flex flex-col items-center justify-center">
                <span class="text-lg font-medium text-gray-100">{{ _("Created on") }}</span>
                <p id="creationDate" class="text-sm text-gray-400 mt-2">{{ _("Loading...") }}</p>
            </div>
        </div>
        <form id="websiteSettingsForm">
            <input type="hidden" id="websiteId">
            <div class="mb-6">
                <label for="resultsContainerSelector" class="block text-sm font-medium text-gray-300">{{ _("Results Container Selector") }}</label>
                <input type="text" id="resultsContainerSelector" name="results_container_selector"
                    class="mt-1 block w-full p-3 rounded-md bg-gray-700 border-gray-600 text-white">
            </div>
            <div class="mb-6">
                <label for="searchInputSelector" class="block text-sm font-medium text-gray-300">{{ _("Search Input Selector") }}</label>
                <input type="text" id="searchInputSelector" name="search_input_selector"
                    class="mt-1 block w-full p-3 rounded-md bg-gray-700 border-gray-600 text-white">
            </div>
            <div class="mb-6">
                <label for="csvUrl" class="block text-sm font-medium text-gray-300">{{ _("CSV URL") }}</label>
                <input type="text" id="csvUrl" name="csv_url"
                    class="mt-1 block w-full p-3 rounded-md bg-gray-700 border-gray-600 text-white">
            </div>
            <div class="flex justify-end">
                <button type="button" onclick="closeSettingsModal()"
                    class="mr-2 px-4 py-2 bg-gray-500 text-white rounded-md">{{ _("Cancel") }}</button>
                <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md">{{ _("Save") }}</button>
            </div>
        </form>
    </div>
</div>


<script>

    document.addEventListener('DOMContentLoaded', function () {
        const userFilter = document.getElementById('userFilter');
        userFilter.addEventListener('change', function () {
            const userId = userFilter.value;
            if (userId) {
                // Redirect to the URL with the selected user ID
                window.location.href = `{{ url_for('monitor_websites') }}?user_id=${userId}`;
            }
            else {
                // Redirect to the URL without the user ID
                window.location.href = `{{ url_for('monitor_websites') }}`;
            }
        });

        document.getElementById('websiteSettingsForm').addEventListener('submit', function (e) {
            e.preventDefault();
            saveWebsiteSettings();
        });
    });



    async function fetchData(url, options) {
        try {
            const response = await fetch(url, options);
            if (!response.ok) {
                const errorBody = await response.json();
                const error = errorBody.detail || '{{ _("An error occurred. Please try again.") }}';
                showAlert(error, 'error');
                return null;
            }
            return response.json();
        } catch (error) {
            console.error('Error:', error);
            throw error;  // Re-throw error to be handled by caller
        }
    }

    async function viewSettings(websiteId) {
        try {
            const data = await fetchData(`/admin/websites/${websiteId}/settings`, {
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
            });
            if (!data) {
                return;
            }
            document.getElementById('creationDate').textContent = formatDateTime(data.created_at);
            document.getElementById('websiteId').value = websiteId;
            document.getElementById('resultsContainerSelector').value = data.website_settings.results_container_selector || '';
            document.getElementById('searchInputSelector').value = data.website_settings.search_input_selector || '';
            document.getElementById('csvUrl').value = data.website_settings.csv_url || '';
            document.getElementById('settingsModal').classList.remove('hidden');
        } catch (error) {
            console.error('Error:', error);
            showAlert('{{ _("Failed to load website settings") }}', 'error');
        }
    }

    function formatDateTime(isoString) {
        const date = new Date(isoString);
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: 'numeric',
            minute: 'numeric',
            second: 'numeric',
            timeZoneName: 'short'
        };
        return date.toLocaleDateString('en-US', options);
    }

    async function saveWebsiteSettings() {
        const websiteId = document.getElementById('websiteId').value;
        const settings = {
            results_container_selector: document.getElementById('resultsContainerSelector').value,
            search_input_selector: document.getElementById('searchInputSelector').value,
            csv_url: document.getElementById('csvUrl').value
        };

        try {
            const data = await fetchData(`/admin/websites/${websiteId}/settings`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify(settings),
            });
            closeSettingsModal();
            if (data == null) {
                return;
            }
            console.log(data);
            showAlert('{{ _("Website settings updated successfully") }}', 'success');
        } catch (error) {
            // Error is already handled in fetchData
            showAlert('{{ _("Failed to update website settings") }}', 'error');
        }
    }

    function closeSettingsModal() {
        document.getElementById('settingsModal').classList.add('hidden');
    }

    async function toggleWebsitePause(websiteId) {
        try {
            const data = await fetchData(`/admin/website/${websiteId}/toggle-pause`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'accept': 'application/json',
                },
            });
            if (!data) {
                return;
            }
            updateWebsitePauseStatus(websiteId, data.is_paused);
            let message = '';
            if (data.is_paused) {
                message = '{{ _("Website paused successfully") }}';
            } else {
                message = '{{ _("Website resumed successfully") }}';
            }
            showAlert(message, 'success');
        } catch (error) {
            console.error('Error:', error);
            showAlert('Failed to toggle website status', 'error');
        }
    }
    function updateWebsitePauseStatus(websiteId, isPaused) {
        const row = document.getElementById(`website-row-${websiteId}`);
        if (row) {
            const statusCell = row.children[4]; // Adjust index for the Paused column
            const toggleButton = row.querySelector('button:nth-child(2)');
            let statusText = '';
            if (isPaused) {
                statusText = '{{ _("Paused") }}';
            } else {
                statusText = '{{ _("Not Paused") }}';
            }
            statusCell.innerHTML = `
                <span class="px-2 inline-flex items-center justify-center text-xs leading-5 font-semibold rounded-full ${isPaused ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'} w-24 h-6">
                    ${statusText}
                </span>
            `;
            
            toggleButton.textContent = isPaused ? 'Resume' : 'Pause';
            toggleButton.className = `px-2 inline-flex items-center justify-center text-xs leading-5 font-semibold rounded-full ${isPaused ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'} w-24 h-6 hover:${isPaused ? 'bg-green-200' : 'bg-yellow-200'} transition duration-150 ease-in-out`;
        }
    }

</script>

{% endblock %}