{% macro alert(message, category='success') %}
<div id="alert-box"
class="fixed top-5 right-5 text-white px-4 py-2 rounded shadow-lg transform translate-x-full opacity-0 transition-all duration-1000 {{ 'bg-green-500' if category == 'success' else 'bg-red-500' }}" style="z-index: 1000;">
  {{ message }}
</div>
<script>
  document.addEventListener('DOMContentLoaded', function () {
    const alertBox = document.getElementById('alert-box');
    if (alertBox) {
      // Slide the alert box in
      alertBox.classList.remove('translate-x-full', 'opacity-0');
      alertBox.classList.add('translate-x-0', 'opacity-100');

      // Hide the alert after 6 seconds (slide-in + time displayed)
      setTimeout(() => {
        // Start the fade-out and slide-out animation
        alertBox.classList.remove('translate-x-0', 'opacity-100');
        alertBox.classList.add('translate-x-full', 'opacity-0');

        // Remove the element after the transition
        setTimeout(() => {
          alertBox.remove();
        }, 1000); // Match the duration of the transition
      }, 6000);
    }
  });
</script>
{% endmacro %}