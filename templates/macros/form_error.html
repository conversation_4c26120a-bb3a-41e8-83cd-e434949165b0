{% macro form_error(error) %}
<div id="register-error" class="bg-red-800 border border-red-700 text-white px-4 py-3 rounded relative" role="alert">
    <strong class="font-bold">{{ _('Error!') }}</strong>
    <span class="block sm:inline">{{ error }}</span>
    <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
        <svg class="fill-current h-6 w-6 text-white" role="button" xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20">
            <title>{{ _('Close') }}</title>
            <path
                d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697l2.652 3.031 2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.697z">
            </path>
        </svg>
    </span>
</div>
<script>
    // error alert close button
    var registerError = document.querySelector('#register-error');
    if (registerError) {
        registerError.addEventListener('click', function () {
            this.style.display = 'none';
        });
    }
</script>
{% endmacro %}