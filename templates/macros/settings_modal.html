{% macro settings_modal(website_settings, website_domain) %}
<!-- The Modal -->
<div id="settings-modal"
    class="fixed inset-0 flex items-center justify-center overflow-y-auto hidden transition-opacity duration-500 ease-in-out"
    style="z-index: 1000;" onclick="handleBackdropClick(event)">
    <div class="bg-gray-200 dark:bg-gray-500 rounded-lg overflow-hidden shadow-xl transform transition-all sm:w-3/4 sm:max-w-lg"
        onclick="event.stopPropagation();">
        <div class="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-center justify-between">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                    {{ _('Settings') }}
                </h3>
                <!-- Close button -->
                <button onclick="closeModal()"
                    class="text-gray-400 hover:text-gray-500 dark:text-white dark:hover:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 p-1 rounded-full">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>

            </div>
            <div
                class="mt-3 mb-2 p-4 bg-gray-100 dark:bg-gray-700 transition-colors duration-500 rounded-lg border-2 border-gray-300 dark:border-gray-600">
                <div class="mb-4">
                    <label for="csvUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-200">{{ _('CSV URL') }}</label>
                    <input type="text" name="csvUrl" id="csvUrl"
                        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md bg-gray-50 dark:bg-gray-800 dark:text-white dark:border-gray-600"
                        value="{% if website_settings %}{{ website_settings.csv_url }}{% endif %}"
                        placeholder="{{ _('Enter CSV URL here') }}">
                </div>

                <div>
                    <label for="websiteDomain"
                        class="block text-sm font-medium text-gray-700 dark:text-gray-200">{{ _('Website Domain') }}</label>
                    <input type="text" name="websiteDomain" id="websiteDomain"
                        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md bg-gray-50 dark:bg-gray-800 dark:text-white dark:border-gray-600"
                        value="{% if website_domain %}{{ website_domain }}{% endif %}"
                        placeholder="{{ _('Enter website domain here') }}">
                </div>
                <!-- hidden input for website settings id -->
                <input type="hidden" name="websiteSettingsId" id="websiteSettingsId"
                    value="{% if website_settings %}{{ website_settings.website_settings_id }}{% endif %}">
            </div>

            <!-- error message -->
            <div id="register-error"
                class="bg-red-800 border border-red-700 text-white px-4 py-3 rounded relative hidden" role="alert">
                <strong class="font-bold mr-1">{{ _('Error!') }}</strong>
                <span class="block sm:inline error-message"></span>
                <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                    <svg class="fill-current h-6 w-6 text-white" role="button" xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20">
                        <title>{{ _('Close') }}</title>
                        <path
                            d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697l2.652 3.031 2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.697z">
                        </path>
                    </svg>
                </span>
            </div>
            <!-- success message -->
            <div id="register-success"
                class="bg-green-800 border border-green-700 text-white px-4 py-3 rounded relative hidden" role="alert">
                <strong class="font-bold mr-1">{{ _('Success!') }}</strong>
                <span class="block sm:inline success-message"></span>
                <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                    <svg class="fill-current h-6 w-6 text-white" role="button" xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20">
                        <title>{{ _('Close') }}</title>
                        <path
                            d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697l2.652 3.031 2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.697z">
                        </path>
                    </svg>
                </span>
            </div>
            <div class="bg-gray-200 dark:bg-gray-500 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse sm:items-center">
                <!-- Button to close the modal -->
                <button onclick="handleCancel()" type="button"
                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {{ _('Cancel') }}
                </button>
                <button onclick="handleSave()" type="button"
                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                    {{ _('Save') }}
                </button>
                <!-- loading indicator -->
                <div
                    class="loading-indicator w-6 h-6 border-4 border-gray-300 border-t-4 border-t-blue-500 rounded-full animate-spin-slow hidden">
                </div>
            </div>
        </div>
    </div>

    <script>
        //the function to close the modal
        function closeModal() {
            const modal = document.getElementById('settings-modal');
            modal.style.opacity = 0; // Start the transition to opacity 0
            setTimeout(() => modal.classList.add('hidden'), 500); // After the transition ends, add the 'hidden' class
        }
        // listen for settings-modal:open event
        document.addEventListener('settings-modal:open', function () {
            const modal = document.getElementById('settings-modal');
            modal.classList.remove('hidden');
        })
        // handle cancel button
        function handleCancel() {
            closeModal();
        }
        function handleSave() {
            const csvUrl = document.getElementById('csvUrl').value.trim();
            const websiteDomain = document.getElementById('websiteDomain').value.trim();
            // empty fields are not allowed
            if (!csvUrl || !websiteDomain) {
                document.getElementById('register-error').classList.remove('hidden');
                document.querySelector('.error-message').textContent = '{{ _("Please fill in all fields.") }}';
                return;
            }
            // hide error message
            document.getElementById('register-error').classList.add('hidden');
            // hide success message
            document.getElementById('register-success').classList.add('hidden');

            // send data to server for update using jquery
            const websiteSettingsId = document.getElementById('websiteSettingsId').value.trim();
            if (!websiteSettingsId || !parseInt(websiteSettingsId)) {
                document.getElementById('register-error').classList.remove('hidden');
                document.querySelector('.error-message').textContent = '{{ _("Invalid website settings id.") }}';
                return;
            }
            const url = '/user/website/settings/' + websiteSettingsId;
            // show loading indicator
            document.querySelector('.loading-indicator').classList.remove('hidden');
            $.ajax({
                url: url,
                type: 'PUT',
                data: {
                    csv_url: csvUrl,
                    website_domain: websiteDomain
                },
                success: function (data) {
                    // show success message
                    document.getElementById('register-success').classList.remove('hidden');
                    document.querySelector('.success-message').textContent = '{{ _("Website settings updated successfully.") }}';
                    // hide loading indicator
                    document.querySelector('.loading-indicator').classList.add('hidden');
                    setTimeout(() => {
                        document.getElementById('register-success').classList.add('hidden');
                        closeModal();
                    }, 3000);
                },
                error: function (error) {
                    console.log(error);
                    document.getElementById('register-error').classList.remove('hidden');
                    if (error.responseJSON) {
                        document.querySelector('.error-message').textContent = error.responseJSON.detail;
                    }
                    else {
                        document.querySelector('.error-message').textContent = '{{ _("An error occurred. Please try again.") }}';
                    }
                    // hide loading indicator
                    document.querySelector('.loading-indicator').classList.add('hidden');
                }
            });
        }

        // handle backdrop click
        function handleBackdropClick(event) {
            closeModal();
        }

        document.addEventListener('DOMContentLoaded', () => {
            const settingsButton = document.getElementById('settingsButton');
            const modal = document.getElementById('settings-modal');
            settingsButton.addEventListener('click', () => {
                modal.classList.remove('hidden');
                setTimeout(() => modal.style.opacity = 1, 0); // Start the transition to opacity 1
            });

            // error alert close button
            var registerError = document.querySelector('#register-error');
            if (registerError) {
                registerError.addEventListener('click', function () {
                    document.getElementById('register-error').classList.add('hidden');
                });
            }
        });
    </script>
{% endmacro %}