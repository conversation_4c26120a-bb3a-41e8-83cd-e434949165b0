{% extends "layouts/admin_base.html" %}

{% block title %}{{ _('Websites Review') }}{% endblock %}

{% block admin_content %}
<div class="dark:bg-gray-800 py-4 px-4 sm:px-6 lg:px-8 h-full">
    <div class="w-full p-4 bg-white dark:bg-gray-800">
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                    {{ _('Registered Websites') }}
                </h3>
                <div class="mt-2 max-w-xl text-sm text-gray-500 dark:text-gray-400">
                    <p>{{ _('The following websites have been registered to be tracked.') }}</p>
                </div>
                <div class="mt-8">
                    {% if websites %}
                    <div class="overflow-x-auto">
                        <table class="w-full min-w-full divide-y divide-gray-200 dark:divide-gray-600 rounded-lg overflow-hidden">
                            <thead class="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                                <tr class="text-sm font-medium uppercase tracking-wider">
                                    <th class="px-6 py-3">
                                        {{ _('Website key') }}
                                    </th>
                                    <th class="px-6 py-3">
                                        {{ _('Website url') }}
                                    </th>
                                    <th class="px-6 py-3">
                                        {{ _('CSV url') }}
                                    </th>
                                    <th class="px-6 py-3">
                                        {{ _('Actions') }}
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-700 dark:divide-gray-600 text-gray-700 dark:text-gray-300">
                                {% for website in websites %}
                                <tr class="hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                    <td class="px-6 py-4">
                                        {{ website.website_key }}
                                    </td>
                                    <td class="px-6 py-4">
                                        <a href="{{ website.website_url }}" target="_blank" class="text-blue-500 dark:text-blue-400 hover:underline">{{ website.website_url }}</a>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="truncate max-w-xs">
                                            <a href="{{ website.website_settings.csv_url }}" target="_blank" class="text-blue-500 dark:text-blue-400 hover:underline">{{ website.website_settings.csv_url }}</a>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 flex items-center space-x-2">
                                        <button data-user='{{ website | tojson | safe }}' class="bg-blue-500 hover:bg-blue-700 text-white font-medium py-1 px-2 rounded text-sm action-button" onclick="handle_update_website(event)">
                                            {{ _('Update') }}
                                        </button>
                        
                                        <button data-id="{{website.website_id}}" onclick="handle_delete_website(event)" class="bg-red-500 hover:bg-red-700 text-white font-medium py-1 px-2 rounded text-sm action-button">
                                            {{ _('Delete') }}
                                        </button>
                                        {% include 'partials/form_submit_spinner.html' %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg py-8 text-center">
                        <p class="dark:text-gray-100 text-gray-700 text-2xl font-medium mb-4">
                            {{ _('You haven\'t registered any websites yet.') }}
                        </p>
                        <a href="{{ url_for('website_register') }}"
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            {{ _('Register a website') }}
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Loading overlay div -->
<div id="loading-overlay"
    class="hidden fixed inset-0 bg-white dark:bg-gray-800 bg-opacity-80 dark:bg-opacity-80 z-50 flex items-center justify-center">
    <div id="loading-spinner" class="text-2xl font-bold text-black dark:text-white">
        {{ _('Reloading...') }}
    </div>
</div>
<!-- website update modal -->
{% include 'partials/website_update_modal.html' %}

{% endblock %}

{% block admin_javascript %}
<script>
    let website_id = null;
    let target_event = null;
    function handle_update_website(event) {
        event.preventDefault();
        let website = JSON.parse(event.target.dataset.user);
        // show the modal
        window.dispatchEvent(new CustomEvent('website-update-modal:open', {
            detail: {
                website_data: website
            }
        }))
    }

    function handle_delete_website(event) {
        event.preventDefault();
        let id = event.target.dataset.id;
        if(!id) {
            showAlert('{{ _("could not delete website, missing id.") }}', 'error');
            return;
        }
        website_id = id;
        target_event = event
        // show confirmation dialog
        confirmAction('{{ _("Are you sure you want to delete this website?") }}', delete_website);
    }

    function delete_website(){
        // show the loading spinner of the current button
        toggle_loading_spinner(target_event.target);
        toggle_action_buttons()
        $.ajax({
            url: '/admin/website/delete/' + website_id,
            type: 'DELETE',
            success: function (response) {
                // handle successful deletion
                console.log(response);
                toggle_loading_spinner(target_event.target);
                toggle_action_buttons();
                reloadPage();
            },
            error: function (error) {
                console.log(error.responseJSON.detail);
                showAlert(error.responseJSON.detail, 'error');
                // hide the loading spinner
                toggle_loading_spinner(target_event.target);
                toggle_action_buttons();
            }
        })
    }

    function toggle_action_buttons() {
        let buttons = document.getElementsByClassName('action-button');
        for (let i = 0; i < buttons.length; i++) {
            buttons[i].disabled = !buttons[i].disabled;
        }
    }

    function reloadPage() {
        document.getElementById('loading-overlay').classList.remove('hidden');
        setTimeout(function () {
            location.reload();
        }, 1000);
    }

    function toggle_loading_spinner(element, data) {
        element.nextSibling.nextSibling.classList.toggle('hidden');
    }
</script>
{% endblock %}