{% extends "layouts/admin_base.html" %}

{% block admin_content %}
<section class="dark:bg-gray-800 flex flex-col py-4 px-4 sm:px-6 lg:px-8 h-full overflow-y-auto">
    <h1 class="font-bold mb-2 text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl 2xl:text-4xl">{{ _('Register a website') }}</h1>
    {% if error %}
    {{ form_error(error) }}
    {% endif %}
    {% if message %}
    {{ alert(message, 'success') }}
    {% endif %}
    <div class="w-full h-full flex items-center justify-center">
        <form class="mt-8 space-y-6 w-full max-w-md" action="{{ url_for('website_register') }}" method="POST">
            {{csrf_input}}
            <div class="rounded-md shadow-sm -space-y-px">
                <div class="rounded-md shadow-sm">
                    <!-- searchable dropdown list of clients -->
                    <div class="mb-4">
                        {% if clients %}
                        <select id="client-select" name="user_id">
                            {% for client in clients %}
                            <option value="{{ client.user_id }}" {% if form_data and form_data.user_id == client.user_id %}selected{% endif %}>{{ client.username }}</option>
                            {% endfor %}
                        </select>
                        {% endif %}
                    </div>                    
                    <div class="mb-4">
                        <label for="website-domain" class="sr-only">{{ _('Website domain') }}</label>
                        <input id="website-domain" name="website_url" type="text" required
                            class="appearance-none rounded block w-full px-3 py-2 border placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-opacity-50 sm:text-sm dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:border-gray-600 transition duration-200 ease-in-out shadow-sm dark:shadow-none hover:shadow-md dark:hover:shadow-lg dark:hover:border-gray-500 focus:ring-indigo-600 dark:focus:ring-indigo-400"
                            placeholder="{{ _('Enter the domain of the website') }}"
                            value="{{ (form_data if form_data is defined else {}).website_url|default('', true) }}">
                    </div>
                    <div class="mb-4">
                        <label for="csv-url" class="sr-only">{{ _('CSV URL') }}</label>
                        <input id="csv-url" name="csv_url" type="text"
                            class="appearance-none rounded block w-full px-3 py-2 border placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-opacity-50 sm:text-sm dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:border-gray-600 transition duration-200 ease-in-out shadow-sm dark:shadow-none hover:shadow-md dark:hover:shadow-lg dark:hover:border-gray-500 focus:ring-indigo-600 dark:focus:ring-indigo-400"
                            placeholder="{{ _('Enter the URL of the CSV file') }}"
                            value="{{ (form_data if form_data is defined else {}).csv_url|default('', true) }}">
                    </div>
                </div>

                <div>
                    <button type="submit"
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 active:bg-indigo-800 transition duration-150 ease-in-out disabled:bg-indigo-400 disabled:cursor-not-allowed"
                        {% if is_disabled %}disabled{% endif %}>
                        {{ _('Register') }}
                    </button>
                </div>
        </form>
    </div>
</section>
{% endblock %}

{% block admin_javascript %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Initialize select2
        $('#client-select').select2({
            width: '100%',
        }).on('select2:open', function (e) {
            $('.select2-dropdown').addClass('w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded');
            $('.select2-search__field').addClass('w-full py-3 px-4 text-black dark:text-white bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded');
            $('.select2-results__option').addClass('py-2 px-4 text-black dark:text-white cursor-pointer');
        });
        $('.select2-selection__rendered').addClass('text-black dark:text-white bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded');
        $('.select2-selection__arrow').addClass('text-black dark:text-white bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded');
        $('.select2-selection--single').addClass('bg-white dark:bg-gray-800 text-black dark:text-white border border-gray-300 dark:border-gray-600 rounded');
    })
</script>
{% endblock %}