{% extends "base.html" %}

{% block title %}Access Denied{% endblock %}

{% block content %}
<div class="flex items-center justify-center min-h-[85vh] bg-gray-100 dark:bg-gray-900">
    <div class="w-full max-w-md p-4">
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg border-t-4 border-red-500">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                    <svg class="h-10 w-10 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 6v6m0 4v2m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white ml-4">
                        {{ _('Access Denied') }}
                    </h3>
                </div>
                <div class="mt-2 max-w-xl text-sm text-gray-500 dark:text-gray-400">
                    <p>{{ _('You do not have the necessary permissions to view this page. Please check your credentials and try again.') }}</p>
                    <p>{{ _('If you believe this is an error, contact support. Attempting to access restricted content without authorization is not allowed.') }}</p>
                </div>
                <div class="mt-5">
                    <a href="{{ url_for('user_home') }}" class="inline-block px-4 py-2 bg-blue-500 hover:bg-blue-700 text-white font-bold rounded focus:outline-none focus:shadow-outline dark:bg-blue-600 dark:hover:bg-blue-800">
                        {{ _('Go to Homepage') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
