{% extends "layouts/client_base.html" %}

{% block client_content %}
<section class="dark:bg-gray-900 py-4 px-4 sm:px-6 lg:px-8 h-full">
    <div class="flex items-center justify-between mb-8 flex-wrap md:flex-nowrap">
        <h1 class="ml-3 text-3xl font-semibold text-gray-900 dark:text-white">{{ _('Search Terms') }}</h1>
        <div class="flex items-center">
            <!-- Date Range Picker -->
            <input type="text" id="daterange" aria-label="{{ _('Date Range') }}"
                class="daterange px-4 py-2 border focus:ring-gray-500 focus:border-gray-900 w-full sm:text-sm border-gray-300 rounded-md focus:outline-none text-gray-600 dark:text-gray-100 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-gray-400 dark:focus:border-gray-400">
        </div>
    </div>

    <!-- DataTable for search terms along with occurrences -->
    <div class="w-full overflow-hidden rounded-lg shadow-xs">
        <div class="w-full overflow-x-auto">
            <table id="searchTermsTable" class="min-w-full leading-normal dark:bg-gray-800">
                <thead>
                    <tr class="bg-gray-200 dark:bg-gray-700">
                        <th
                            class="px-4 py-3 text-center text-xs font-medium text-gray-700 dark:text-gray-100 uppercase tracking-wider border-b border-gray-300 dark:border-gray-600">
                            {{ _('Search Term') }}</th>
                        <th
                            class="px-4 py-3 text-center text-xs font-medium text-gray-700 dark:text-gray-100 uppercase tracking-wider border-b border-gray-300 dark:border-gray-600">
                            {{ _('Occurrences') }}</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Hidden input for website key -->
    {% if current_website.website_key %}
    <input type="hidden" id="websiteKey" name="websiteKey" value="{{ current_website.website_key | upper }}">
    {% endif %}
</section>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        "use strict";

        // Utility function to format date as YYYY-MM-DD
        function formatDate(date) {
            return new Date(date).toISOString().slice(0, 10);
        }

        // Initialize variables for date range
        let startDate, endDate;
        const today = new Date();
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        const yesterday = new Date(today.setDate(today.getDate() - 1));

        startDate = formatDate(firstDayOfMonth);
        endDate = formatDate(yesterday);

        // Initialize the date range picker
        const fp = flatpickr('#daterange', {
            mode: "range",
            dateFormat: "Y-m-d",
            minDate: "2024-01-01", // Set a reasonable minDate
            defaultDate: [startDate, endDate],
            onChange: function (selectedDates) {
                if (selectedDates.length === 2) {
                    startDate = formatDate(selectedDates[0]);
                    endDate = formatDate(selectedDates[1]);
                    console.log("{{ _('Date range selected:') }}", startDate, "{{ _('to') }}", endDate);
                    if (datatable) {
                        datatable.ajax.reload(null, false); // Reload data without resetting paging
                    }
                }
            }
        });

        // Extract the website key if available
        const websiteKey = document.getElementById('websiteKey') ? document.getElementById('websiteKey').value : null;

        // Define the function to fetch data from the server
        async function fetchData(params) {
            try {
                const response = await fetch(`/reports/search_terms/list?${new URLSearchParams(params)}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                if (!response.ok) {
                    throw new Error(`HTTP status ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                console.error("{{ _('Error during data fetch:') }}", error);
                throw error; // Rethrow to handle it in the calling function
            }
        }

        // Initialize DataTable and store the reference
        let datatable = $('#searchTermsTable').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            ajax: async (data, callback) => {
                try {
                    const params = {
                        websiteKey: websiteKey,
                        startDate: startDate,
                        endDate: endDate,
                        draw: data.draw,
                        start: data.start,
                        length: data.length,
                        search: data.search.value,
                        order: JSON.stringify(data.order.map(o => ({ column: data.columns[o.column].data, dir: o.dir })))
                    };
                    const json = await fetchData(params);
                    callback(json);
                } catch (error) {
                    console.error('Error fetching data:', error);
                    alert("An error occurred while fetching the data. Please try again later.");
                }
            },
            order: [[0, 'asc']], // Default order, if needed
            columns: [
                { data: "search_term", render: data => `<div class="text-center">${data}</div>`, orderable: true },
                { data: "count", render: data => `<div class="text-center">${data}</div>`, orderable: true }
            ],
            dom: '<"flex justify-between items-center mb-4"lf>t<"flex justify-between items-center mt-4"ip>',
            language: {
                search: '',
                searchPlaceholder: "{{ _('Search...') }}",
                lengthMenu: '<span class="dark:text-gray-100">{{ _('Show _MENU_ entries') }}</span>',
                info: '<span class="dark:text-gray-100">{{ _('Showing _START_ to _END_ of _TOTAL_ entries') }}</span>',
                paginate: {
                    first: '<span class="dark:text-gray-100">&laquo;</span>',
                    last: '<span class="dark:text-gray-100">&raquo;</span>',
                    previous: '<span class="dark:text-gray-100">&larr;</span>',
                    next: '<span class="dark:text-gray-100">&rarr;</span>'
                }
            },
            drawCallback: function (settings) {
                $('#searchTermsTable_filter input').addClass('dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600 dark:focus:ring-gray-400 dark:focus:border-gray-400 px-4 py-2 border border-gray-300 rounded-md focus:outline-none');
                $('#searchTermsTable_length select').addClass('dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600 dark:focus:ring-gray-400 dark:focus:border-gray-400 px-4 py-2 border border-gray-300 rounded-md focus:outline-none');
            },
            rowCallback: function (row, data) {
                $(row).addClass('bg-white dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-600');
            }
        });

        // Handle any DataTable-specific errors
        $('#searchTermsTable').on('error.dt', function (e, settings, techNote, message) {
            console.error('An error has been reported by DataTables:', message);
            alert("An error occurred within the data table. Please try again later.");
        });
    });
</script>

{% endblock %}