{% extends "layouts/client_base.html" %}

{% block client_content %}
<section class="dark:bg-gray-800 py-4 px-4 sm:px-6 lg:px-8 h-full">
    <div class="mb-8">
        <h1 class="ml-3 text-3xl font-semibold text-gray-900 dark:text-white mb-8">{{ _('Search Integration') }}</h1>
        <p>{{ _('Useful configurations and code snippets to integrate your search') }}</p>
    </div>
    <div class="bg-gray-200 dark:bg-gray-500 rounded-lg shadow-xl p-4 mb-4 ">
        <h2 class="text-xl leading-6 font-medium text-gray-900 dark:text-white mb-2">{{ _('Search input and results container selectors') }}</h2>
        <!-- <p class="text-md font-medium text-gray-700 dark:text-gray-200">{{ _('Fill in the selectors for the search input and results container of your website') }}</p> -->

        <div class="flex flex-col mt-4">
            <label for="search_input_selector" class="text-md font-medium text-gray-700 dark:text-gray-200">{{ _('Search Input Selector') }}</label>
            <input type="text" id="search_input_selector"
                class="appearance-none rounded relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:border-transparent focus:z-10 sm:text-sm transition duration-300 ease-in-out dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:focus:border-gray-500 dark:focus:ring-indigo-400 dark:placeholder-gray-400 hover:border-gray-400 dark:hover:border-gray-500"
                placeholder="{{ _('Enter search input selector') }}"
                value="{% if website_settings %}{{ website_settings.search_input_selector }}{% endif %}">
        </div>

        <div class="flex flex-col mt-4">
            <label for="results_container_selector" class="text-md font-medium text-gray-700 dark:text-gray-200">{{ _('Results Container Selector') }}</label>
            <input type="text" id="results_container_selector"
                class="appearance-none rounded relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:border-transparent focus:z-10 sm:text-sm transition duration-300 ease-in-out dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:focus:border-gray-500 dark:focus:ring-indigo-400 dark:placeholder-gray-400 hover:border-gray-400 dark:hover:border-gray-500"
                placeholder="{{ _('Enter results container selector') }}"
                value="{% if website_settings %}{{ website_settings.results_container_selector }}{% endif %}">
        </div>
        <!-- hidden input for website settings id -->
        <input type="hidden" name="websiteSettingsId" id="websiteSettingsId"
            value="{% if website_settings %}{{ website_settings.website_settings_id }}{% endif %}">

        <!-- error message -->
        {% include "partials/form_error_notification.html" %}
        <!-- success message -->
        {% include "partials/form_success_notification.html" %}

        <button id="selector-save-button"
    class="mt-10 group relative w-full flex justify-center py-2 px-4 border border-transparent text-md font-medium rounded-md text-white bg-indigo-500 dark:bg-orange-400 hover:bg-indigo-600 dark:hover:bg-orange-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-400 dark:focus:ring-orange-300 active:bg-indigo-700 dark:active:bg-orange-600 transition duration-150 ease-in-out disabled:bg-gray-400 dark:disabled:bg-gray-600 disabled:cursor-not-allowed">
            <div
                class="loading-indicator w-6 h-6 border-4 border-gray-300 border-t-4 border-t-blue-500 rounded-full animate-spin-slow hidden">
            </div>
            <span class="ml-2">{{ _('Save') }}</span>
        </button>
    </div>
    <!-- JS code snippet to integrate the search -->
    <div class="bg-gray-200 dark:bg-gray-500 rounded-lg shadow-xl p-4 mt-10">
        <h2 class="text-xl leading-6 font-medium text-gray-900 dark:text-white mb-2 flex items-center">
            <button id="copy-button" class="mr-3 p-2 rounded hover:bg-gray-300 dark:hover:bg-gray-600 transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500" title="{{ _('Copy to clipboard') }}">
                <i class="fas fa-copy"></i>
            </button>
            {{ _('JS Code Snippet') }}
        </h2>
        <div id="code-snippet" class="w-full p-4 bg-gray-100 dark:bg-gray-700 rounded-md break-words overflow-x-auto">
            <pre><code class="language-javascript">{% if code_snippet %}{{ code_snippet }}{% else %}// {{ _('Code snippet not available yet') }}{% endif %}</code></pre>
        </div>
        <div id="copy-feedback" class="mt-2 text-green-600 dark:text-green-400 hidden">
            {{ _('Copied to clipboard!') }}
        </div>
    </div>
</section>
<script>
    document.addEventListener('DOMContentLoaded', () => {
        const saveButton = document.querySelector('#selector-save-button');
        // Add a click event listener to the save button
        saveButton.addEventListener('click', () => {
            // Get the search input and results container selectors
            const searchInputSelector = document.getElementById('search_input_selector').value.trim();
            const resultsContainerSelector = document.getElementById('results_container_selector').value.trim();
            // Check if the search input and results container selectors are not empty
            if (!searchInputSelector || !resultsContainerSelector) {
                // emit error event
                window.dispatchEvent(new CustomEvent('form-error:show', {
                    detail: '{{ _('Please fill in all fields.') }}'
                }))
                return;
            }
            // emit hide error event
            window.dispatchEvent(new CustomEvent('form-error:hide'))
            // hide success message
            window.dispatchEvent(new CustomEvent('form-success:hide'))
            // send data to server for update using jquery
            const websiteSettingsId = document.getElementById('websiteSettingsId').value.trim();
            if (!websiteSettingsId || !parseInt(websiteSettingsId)) {
                window.dispatchEvent(new CustomEvent('form-error:show', {
                    detail: '{{ _('Invalid website settings id.') }}'
                }))
                return;
            }
            const url = '/user/website/settings/' + websiteSettingsId + '/selectors';
            // show the loading indicator
            document.querySelector('.loading-indicator').classList.remove('hidden');
            // make the request to the server
            $.ajax({
                url: url,
                type: 'PUT',
                data: {
                    search_input_selector: searchInputSelector,
                    results_container_selector: resultsContainerSelector
                },
                success: function (data) {
                    console.log(data);
                    // emit the success event
                    window.dispatchEvent(new CustomEvent('form-success:show', {
                        detail: '{{ _('Website settings updated successfully.') }}'
                    }))

                    // hide loading indicator
                    document.querySelector('.loading-indicator').classList.add('hidden');
                },
                error: function (error) {
                    console.log(error);
                    var errorMessage = error.responseJSON ? error.responseJSON.detail : '{{ _('An error occurred. Please try again.') }}';
                    // emit the error event
                    window.dispatchEvent(new CustomEvent('form-error:show', {
                        detail: errorMessage
                    }))
                    // hide loading indicator
                    document.querySelector('.loading-indicator').classList.add('hidden');
                }
            });
        })
    })
</script>
<script>
    document.addEventListener('DOMContentLoaded', () => {
        const copyButton = document.getElementById('copy-button');
        const codeSnippet = document.getElementById('code-snippet');
        const copyFeedback = document.getElementById('copy-feedback');
    
        copyButton.addEventListener('click', () => {
            const textToCopy = codeSnippet.innerText;
            
            navigator.clipboard.writeText(textToCopy).then(() => {
                // Show feedback
                copyFeedback.classList.remove('hidden');
                
                // Change button appearance
                copyButton.classList.add('bg-green-500');
                copyButton.querySelector('i').classList.remove('fa-copy');
                copyButton.querySelector('i').classList.add('fa-check');
                
                // Reset after 2 seconds
                setTimeout(() => {
                    copyFeedback.classList.add('hidden');
                    copyButton.classList.remove('bg-green-500');
                    copyButton.querySelector('i').classList.add('fa-copy');
                    copyButton.querySelector('i').classList.remove('fa-check');
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy text: ', err);
            });
        });
    });
</script>
{% endblock %}