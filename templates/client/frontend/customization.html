{% extends "layouts/client_base.html" %}

{% block client_content %}
<section class="dark:bg-gray-800 py-4 px-4 sm:px-6 lg:px-8 h-full">
    <div class="mb-8">
        <h1 class="ml-3 text-3xl font-semibold text-gray-900 dark:text-white mb-8">{{ _('Search Customization') }}</h1>
        <p>{{ _('Customize the order of the search filters for your website') }}</p>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4" id="filters-container">
        {% for filter in customization_json.filters %}
        <div class="bg-white dark:bg-gray-700 rounded-lg shadow-md p-4 filter-item" draggable="true"
            data-filter-field="{{ filter.field }}">
            <div class="flex justify-between">
                <span class="drag-handle text-gray-400 cursor-move">&#9776;</span>
                <button class="edit-button text-gray-600 dark:text-gray-300"
                    data-filter-field="{{ filter.field }}">{{ _('Edit') }}</button>
            </div>
            <h2 class="text-lg font-semibold mb-2 display-name">{{ filter.displayName }}</h2>
            <input type="text" class="edit-input hidden w-full mb-2 p-2 dark:bg-gray-600 dark:text-white rounded-md" value="{{ filter.displayName }}">
            <p class="text-gray-600 dark:text-gray-300">{{ _('Field:') }} {{ filter.field }}</p>
            <p class="text-gray-600 dark:text-gray-300">{{ _('Type:') }} {{ filter.type }}</p>
        </div>
        {% endfor %}
    </div>
    <input type="hidden" name="currentWebsiteKey" id="currentWebsiteKey" value="{{ customization_json.website_key }}">
</section>
<script>
    document.addEventListener('DOMContentLoaded', () => {
        const filtersContainer = document.getElementById('filters-container');
        const filterItems = document.querySelectorAll('.filter-item');
        const editButtons = document.querySelectorAll('.edit-button');

        let draggedItem = null;

        editButtons.forEach(button => {
            button.addEventListener('click', toggleEdit);
        });

        function toggleEdit(e) {
            const button = e.currentTarget;
            const parent = button.closest('.filter-item');
            const displayNameElement = parent.querySelector('.display-name');
            const editInput = parent.querySelector('.edit-input');

            if (editInput.classList.contains('hidden')) {
                button.textContent = '{{ _('Save') }}';
                displayNameElement.classList.add('hidden');
                editInput.classList.remove('hidden');
            } else {
                button.textContent = '{{ _('Edit') }}';
                displayNameElement.textContent = editInput.value;
                displayNameElement.classList.remove('hidden');
                editInput.classList.add('hidden');
                saveUpdatedDisplayName(parent.dataset.filterField, editInput.value);
            }
        }

        async function saveUpdatedDisplayName(filterField, newDisplayName) {
            const websiteKey = document.getElementById('currentWebsiteKey').value.trim();
            const response = await fetch('/api/update-filter-display-name', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ filter_field: filterField, new_display_name: newDisplayName, website_key: websiteKey })
            });

            if (response.ok) {
                console.log(`{{ _('Display name of filter') }} ${filterField} {{ _('updated successfully') }}`);
                showAlert(`{{ _('Display name of filter') }} ${filterField} {{ _('updated successfully') }}`, 'success');
            } else {
                console.error(`{{ _('Failed to update display name of filter') }} ${filterField}`);
            }
        }

        filterItems.forEach(item => {
            item.addEventListener('dragstart', dragStart);
            item.addEventListener('dragover', dragOver);
            item.addEventListener('drop', drop);
            item.addEventListener('dragend', dragEnd);
        });

        function dragStart(e) {
            draggedItem = e.currentTarget;
            e.dataTransfer.dropEffect = 'move';
            e.currentTarget.classList.add('dragged-clone');
            setTimeout(() => {
                e.currentTarget.classList.add('opacity-50', 'shadow-lg');
            }, 0);
        }

        function dragOver(e) {
            e.preventDefault();
            const dropTarget = e.currentTarget;
            if (dropTarget.classList.contains('filter-item')) {
                dropTarget.classList.add('drag-over');
            }
        }

        function drop(e) {
            e.preventDefault();
            const dropTarget = e.currentTarget;
            if (dropTarget.classList.contains('filter-item')) {
                const parent = dropTarget.parentNode;
                const items = Array.from(parent.children);
                const draggedItemIndex = items.indexOf(draggedItem);
                const dropTargetIndex = items.indexOf(dropTarget);

                if (draggedItemIndex < dropTargetIndex) {
                    parent.insertBefore(draggedItem, dropTarget.nextSibling);
                } else {
                    parent.insertBefore(draggedItem, dropTarget);
                }

                reorderItems();
                dropTarget.classList.remove('drag-over');
            }
        }

        function reorderItems() {
            const parent = filtersContainer;
            const items = Array.from(parent.children);

            items.forEach((item, index) => {
                parent.appendChild(item);
            });
        }

        function dragEnd(e) {
            e.currentTarget.classList.remove('opacity-50', 'shadow-lg', 'dragged-clone');
            filterItems.forEach(item => item.classList.remove('drag-over'));
            filtersContainer.classList.remove('drag-over');
            filtersContainer.classList.remove('drag-over-container');

            // get updated order and save to db
            saveUpdatedOrder();
        }

        function getUpdatedOrder() {
            const filtersContainer = document.getElementById('filters-container');
            const updatedOrder = Array.from(filtersContainer.children).map((item, index) => ({
                field: item.dataset.filterField,
                order: index + 1
            }));
            return updatedOrder;
        }

        async function saveUpdatedOrder() {
            const updatedOrder = getUpdatedOrder();
            const websiteKey = document.getElementById('currentWebsiteKey').value.trim();
            const response = await fetch('/api/update-filter-order', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ filters: updatedOrder, website_key: websiteKey })
            });

            if (response.ok) {
                showAlert('{{ _('Filter order updated successfully') }}', 'success');
            } else {
                console.error('{{ _('Failed to update filter order') }}');
            }
        }
    });
</script>
<style>
    .opacity-50 {
        opacity: 0.5;
    }

    .drag-over {
        background-color: #f0f0f0;
        border: 2px dashed #4299e1;
    }

    .dragged-clone {
        opacity: 0.5;
    }

    .drag-handle {
        display: inline-block;
        margin-right: 0.5rem;
        font-size: 1.2rem;
        line-height: 1;
        vertical-align: middle;
    }
</style>
{% endblock %}