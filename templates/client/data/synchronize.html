{% extends "layouts/client_base.html" %}

{% block client_content %}
<section class="dark:bg-gray-800 py-4 px-4 sm:px-6 lg:px-8 h-full">
    <div class="flex items-center justify-between mb-8 flex-wrap md:flex-nowrap">
        <h1 class="ml-3 text-3xl font-semibold text-gray-900 dark:text-white">{{ _('Data synchronization') }}</h1>

        <button id="settingsButton"
            class="ml-4 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 hover:text-gray-900 dark:text-white dark:hover:text-gray-200 px-3 py-2 rounded md:ml-0 mt-4 md:mt-0">
            <i class="fas fa-cog"></i>
            <span class="ml-2">{{ _('Settings') }}</span>
        </button>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div class="rounded overflow-hidden shadow-xl p-6 mb-8 bg-white dark:bg-gray-600">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <i class="fas fa-sync-alt text-gray-500 dark:text-gray-300 h-6 w-6"></i>
                    <h1 class="ml-3 text-xl font-semibold text-gray-900 dark:text-white">{{ _('Automatic Import') }}</h1>
                </div>
                <i class="fas fa-check-circle text-green-500 dark:text-green-300 h-8 w-8 ml-auto"></i>
            </div>

            <div class="flex items-center justify-around text-gray-900 dark:text-gray-100 mb-4">
                <div class="text-center">
                    {% if total_products %}
                    <h1 class="text-4xl font-bold text-gray-500 dark:text-gray-400">{{ total_products }}</h1>
                    {% else %}
                    <h1 class="text-4xl font-bold text-gray-500 dark:text-gray-400">-</h1>
                    {% endif %}
                    <p class="text-xl font-medium text-gray-400 dark:text-gray-400">{{ _('Imported') }}</p>
                </div>

                <div class="text-center">
                    <h1 class="text-4xl font-bold text-orange-500 dark:text-orange-400">0</h1>
                    <p class="text-xl font-medium text-gray-400 dark:text-gray-400">{{ _('Failed') }}</p>
                </div>
            </div>

            <div class="text-gray-900 dark:text-gray-100 mt-4">
                <hr />
                <ul class="space-y-2 mt-4">
                    <li class="text-gray-400 dark:text-gray-400">
                        <i class="fas fa-download mr-4"></i>
                        {{ download_duration }}
                    </li>
                    <li class="text-gray-400 dark:text-gray-400">
                        <i class="fas fa-database mr-4"></i>
                        {{ duration }}
                    </li>
                </ul>
            </div>
        </div>
        <!-- Statistics -->
        <div class="rounded overflow-hidden shadow-xl p-6 mb-8 bg-white dark:bg-gray-600">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <i class="fas fa-chart-line text-gray-500 dark:text-gray-300 h-6 w-6"></i>
                    <h1 class="ml-3 text-xl font-semibold text-gray-900 dark:text-white">{{ _('Statistics') }}</h1>
                </div>
            </div>

            <div class="flex items-center justify-around text-gray-900 dark:text-gray-100 mb-4">
                <div class="text-center">
                    <h1 class="text-xl font-bold text-gray-500 dark:text-gray-400">{{ avg_download_duration }}</h1>
                    <p class="text-md font-medium text-gray-400 dark:text-gray-400">{{ _('Avg. Download') }}</p>
                </div>
                <div class="text-center">
                    <h1 class="text-xl font-bold text-gray-500 dark:text-gray-400">{{ avg_indexation_duration }}</h1>
                    <p class="text-md font-medium text-gray-400 dark:text-gray-400">{{ _('Avg. Total') }}</p>
                </div>
            </div>
        </div>
        <!-- Manual Import -->
        <div class="rounded overflow-hidden shadow-xl p-6 mb-8 bg-white dark:bg-gray-600">
            <!-- hidden input for website key -->
            {% if current_website.website_key %}
            <input type="text" id="manualImportWebsiteKey" name="websiteKey" value={{ current_website.website_key |
                upper }} hidden>
            {% endif %}
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <i class="fas fa-database text-gray-500 dark:text-gray-300 h-6 w-6"></i>
                    <h1 class="ml-3 text-xl font-semibold text-gray-900 dark:text-white">{{ _('Manual Import') }}</h1>
                </div>
            </div>

            <div class="text-gray-900 dark:text-gray-100 mt-4 flex flex-col">
                <div class="mb-4">
                    <hr />
                    <button onclick="confirmManualImport()"
                        class="border border-gray-300 bg-white hover:bg-gray-100 text-gray-800 font-bold py-2 px-4 rounded mt-4 dark:bg-gray-600 dark:text-gray-100 dark:hover:bg-gray-500">
                        {{ _('Start Import') }}
                    </button>
                </div>
                <!-- notifications -->
                <div id="notification_container"
                    class="border px-4 py-3 rounded hidden opacity-0 transition-opacity duration-300 ease-in-out">
                    <span class="block sm:inline notification-message"></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for settings -->
    {% set website_domain = current_website.website_url if current_website else null %}
    {{ settings_modal(website_settings, website_domain) }}
</section>
<script>
    function confirmManualImport() {
        confirmAction('{{ _("Are you sure you want to index this website?") }}', handle_manual_import);
    }
    // manual import
    function handle_manual_import() {
        let websiteKeyElement = document.getElementById('manualImportWebsiteKey');
        if (!websiteKeyElement || !websiteKeyElement.value) {
            showNotification('{{ _("Please select a website.") }}', 'error');
            return;
        }

        hideNotification();
        let website_key = websiteKeyElement.value;
        let url = `/client/trigger-manual-indexing/${website_key}`;

        $.ajax({
            url: url,
            type: 'POST',
            success: function (response) {
                console.log('Success:', response);
                showNotification(response.message, 'success');
            },
            error: function (xhr, status, error) {
                let errorMessage = '{{ _("An unexpected error occurred.") }}';

                if (xhr.status === 409) {
                    errorMessage = xhr.responseJSON.detail || '{{ _("An indexation is already in progress for this website.") }}';
                } else if (xhr.responseJSON && xhr.responseJSON.detail) {
                    errorMessage = xhr.responseJSON.detail;
                }
                console.log('Error:', errorMessage);
                showNotification(errorMessage, 'error');
            }
        });
    }

    function showNotification(message, type = 'info') {
        const notificationContainer = document.getElementById('notification_container');
        const messageElement = notificationContainer.querySelector('.notification-message');

        messageElement.textContent = message;

        // Remove existing color classes
        notificationContainer.classList.remove(
            'bg-yellow-500', 'dark:bg-yellow-600', 'border-yellow-400', 'dark:border-yellow-500',
            'bg-green-500', 'dark:bg-green-600', 'border-green-400', 'dark:border-green-500',
            'bg-red-500', 'dark:bg-red-600', 'border-red-400', 'dark:border-red-500'
        );

        // Add appropriate color classes based on type
        if (type === 'success') {
            notificationContainer.classList.add('bg-green-500', 'dark:bg-green-600', 'border-green-400', 'dark:border-green-500');
        } else if (type === 'error') {
            notificationContainer.classList.add('bg-red-500', 'dark:bg-red-600', 'border-red-400', 'dark:border-red-500');
        } else {
            notificationContainer.classList.add('bg-yellow-500', 'dark:bg-yellow-600', 'border-yellow-400', 'dark:border-yellow-500');
        }

        notificationContainer.classList.remove('hidden', 'opacity-0');
        notificationContainer.classList.add('opacity-100');

        setTimeout(() => {
            hideNotification();
        }, 5000);
    }

    function hideNotification() {
        const notificationContainer = document.getElementById('notification_container');
        notificationContainer.classList.remove('opacity-100');
        notificationContainer.classList.add('opacity-0');
    }


    // push notifications
    let eventSource;

    function setupSSE() {
        eventSource = new EventSource("/sse");

        eventSource.onmessage = function (event) {
            const message = event.data;
            showAlert(message, 'success');
        };

        eventSource.onerror = function (error) {
            // console.error("EventSource failed:", error);
            eventSource.close();
            // Attempt to reconnect after a short delay
            setTimeout(setupSSE, 5000);
        };
    }



    document.addEventListener('DOMContentLoaded', () => {
        const settingsButton = document.getElementById('settingsButton');
        settingsButton.addEventListener('click', () => {
            const event = new Event('settings-modal:open');
            document.dispatchEvent(event);
        });

        
        // Setup SSE
        setupSSE();
    });
</script>
{% endblock %}