<!DOCTYPE html>
<html lang="de">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/png" href="{{ url_for('public', path='/assets/images/favicon.png') }}">
    <link rel="stylesheet" href="{{ url_for('public', path='/assets/css/base_style.css') }}">
    <link rel="stylesheet" href="{{ url_for('public', path='/assets/css/vendors/select2/select2.min.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet"
        href="{{ url_for('public', path='/assets/css/vendors/flatpickr/4.6.13/flatpickr.min.css') }}">
    <link rel="stylesheet"
        href="{{ url_for('public', path='/assets/css/vendors/datatables/2.0.2/dataTables.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('public', path='/assets/css/vendors/flag-icons/flag-icons.min.css') }}">
    <title>{% block title %}{{ _('My FastAPI App') }}{% endblock %}</title>

    <script src="{{ url_for('public', path='/assets/js/utils/notifications.js') }}"></script>
    <script src="{{ url_for('public', path='/assets/js/vendors/htmx/1.3.3/htmx.min.js') }}"></script>
    <script src="{{ url_for('public', path='/assets/js/vendors/tailwind/tailwindcss.js') }}"></script>
    <script src="{{ url_for('public', path='/assets/js/vendors/jquery/jquery-3.2.1.min.js') }}"></script>
    <script src="{{ url_for('public', path='/assets/js/vendors/select2/select2.min.js') }}"></script>
    <script src="{{ url_for('public', path='/assets/js/vendors/flatpickr/4.6.13/flatpickr.min.js') }}"></script>
    <script src="{{ url_for('public', path='/assets/js/vendors/datatables/2.0.2/dataTables.min.js') }}"></script>

    <!-- init dark mode -->
    <script>
        // Check local storage for dark mode setting before the DOM is fully loaded
        (function () {
            const userPreference = localStorage.getItem('darkMode');
            if (userPreference === null || userPreference === 'true') {
                document.documentElement.classList.add('dark');
                localStorage.setItem('darkMode', 'true');
            } else {
                document.documentElement.classList.remove('dark');
            }
        })();
    </script>
    {% block stylesheet %}{% endblock %}
</head>

<body class="flex flex-col h-screen bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200">
    {% block header %}
    {% endblock %}

    <div class="flex-grow flex overflow-hidden pt-16">
        {% block sidebar %}
        {% endblock %}

        <main class="flex-grow overflow-auto">
            {% from "macros/alert.html" import alert %}
            {% from "macros/form_error.html" import form_error %}
            {% from "macros/settings_modal.html" import settings_modal %}
            {% block content %}{% endblock %}
        </main>
    </div>

    <footer class="bg-white dark:bg-gray-900 shadow py-2 text-center">
        <p class="text-sm text-gray-600 dark:text-gray-400">Copyright © 2024</p>
        {% block footer %}{% endblock %}
    </footer>

    <script src="{{ url_for('public', path='/assets/js/vendors/tailwind/tailwind.config.js') }}"></script>
    {% block javascript %}{% endblock %}
</body>

</html>