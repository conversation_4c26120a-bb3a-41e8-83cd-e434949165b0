from pydantic import BaseModel, Field, root_validator
from datetime import datetime
from typing import Optional, List
from schemas.UserSchema import UserSchema

class WebsiteSchema(BaseModel):
    website_id: int = Field(...)
    website_key: str = Field(...)
    website_url: str = Field(...)
    is_active: bool = Field(...)
    is_paused: bool = Field(...)
    created_at: datetime = Field(...)
    updated_at: datetime = Field(...)
    owner: Optional[UserSchema] = Field(...)
    website_settings: Optional['WebsiteSettingsSchema'] = Field(...)

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }
    
class WebsiteCreateSchema(BaseModel):
    website_url: str = Field(...)
    user_id: int = Field(...)
    csv_url: str= Field(...)

    class Config:
        from_attributes = True

class WebsiteUpdateSchema(BaseModel):
    website_url: Optional[str] = Field(None)
    csv_url: Optional[str] = Field(None)
    is_paused: bool = Field(None)

class WebsiteSettingsSchema(BaseModel):
    website_settings_id: int = Field(...)
    website_id: int = Field(...)
    results_container_selector: str = Field(...)
    search_input_selector: str = Field(...)
    csv_url: str = Field(...)
    created_at: datetime = Field(...)
    updated_at: datetime = Field(...)
    website: Optional[WebsiteSchema] = Field(...)

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

class WebsiteSettingsCreateSchema(BaseModel):
    website_id: int = Field(...)
    results_container_selector: Optional[str] = Field(...)
    search_input_selector: Optional[str] = Field(...)
    csv_url: str = Field(...)

    class Config:
        from_attributes = True

class WebsiteSettingsUpdateSchema(BaseModel):
    results_container_selector: Optional[str] = Field(None)
    search_input_selector: Optional[str] = Field(None)
    csv_url: Optional[str] = Field(None)
    website_domain: Optional[str] = Field(None)

    class Config:
        from_attributes = True