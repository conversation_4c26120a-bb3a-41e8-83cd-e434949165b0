from pydantic import BaseModel, Field, field_validator
from datetime import datetime
from typing import Optional, List


class PermissionSchema(BaseModel):
    permission_id: int = Field(...)
    name: str = Field(...)

    class Config:
        from_attributes = True

class PermissionCreateSchema(BaseModel):
    name: str = Field(...)

class PermissionUpdateSchema(BaseModel):
    name: str = Field(...)

class RoleSchema(BaseModel):
    role_id: int = Field(...)
    name: str = Field(...)
    permissions: List[PermissionSchema] = Field(...)

    class Config:
        from_attributes = True

class RoleCreateSchema(BaseModel):
    name: str = Field(...)
    permissions: List[int] = Field(...)

class RoleUpdateSchema(BaseModel):
    name: str = Field(...)
    permissions: List[int] = Field(None)

class RolePermissionSchema(BaseModel):
    role_id: int = Field(...)
    permission_id: int = Field(...)

    class Config:
        from_attributes = True



class UserSchema(BaseModel):
    user_id: int = Field(...)
    username: str = Field(...)
    email: str = Field(...)
    is_active: bool = Field(...)
    is_admin: bool = Field(...)
    role_id: int = Field(...)
    created_at: datetime = Field(...)
    updated_at: datetime = Field(...)
    role: Optional[RoleSchema] = Field(...)

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class UserCreateSchema(BaseModel):
    username: str = Field(...)
    email: str = Field(...)
    password: str = Field(...)
    confirm_password: str = Field(...)
    is_active: bool = Field(True)
    is_admin: bool = Field(...)
    role_id: int = Field(...)

    @field_validator('is_admin')
    def validate_is_admin(cls, v):
        return bool(v)

class UserUpdateSchema(BaseModel):
    username: Optional[str] = Field(None)
    email: Optional[str] = Field(None)
    password: Optional[str] = Field(None)
    is_active: Optional[bool] = Field(None)
    is_admin: Optional[bool] = Field(None)
    role_id: Optional[int] = Field(None)

    @field_validator('is_active')
    def validate_is_active(cls, v):
        return bool(v) if v is not None else None
        
    @field_validator('is_admin')
    def validate_is_admin(cls, v):
        return bool(v) if v is not None else None    