from pydantic import BaseModel, Field
from enums.product_indexation_status import ProductIndexationStatusEnum
from schemas.WebsiteSchema import WebsiteSchema
from datetime import datetime
from typing import Optional, List

class ProductIndexationSchema(BaseModel):
    product_indexation_id: int = Field(...)
    website_id: int = Field(...)
    start_date: datetime = Field(...)
    duration: int = Field(...)
    download_duration: int = Field(...)
    total_products: int = Field(...)
    status: str = Field(...)
    message: str = Field(...)
    created_at: datetime = Field(...)
    # website: Optional[WebsiteSchema] = Field(...)

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

class ProductIndexationCreateSchema(BaseModel):
    website_id: int = Field(...)
    start_date: datetime = Field(...)
    duration: int = Field(...)
    download_duration: int = Field(...)
    total_products: int = Field(...)
    status: str = Field(...)
    message: str = Field(...)