from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional
from enum import Enum

from enums.task_schedule import IndexingType, ScheduleFrequency

class ScheduledTaskSchema(BaseModel):
    task_id: int = Field(...)
    name: str = Field(...)
    description: Optional[str] = Field(None)
    frequency: ScheduleFrequency = Field(...)
    indexing_type: IndexingType = Field(...)
    time: datetime = Field(...)
    is_active: bool = Field(...)
    is_scheduled: bool = Field(...)
    hours_interval: Optional[int] = Field(None)
    last_run: Optional[datetime] = Field(None)
    next_run: Optional[datetime] = Field(None)
    website_id: Optional[int] = Field(None)  # Make it optional
    created_at: datetime = Field(...)
    updated_at: datetime = Field(...)

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }
    

class ScheduledTaskCreateSchema(BaseModel):
    name: str = Field(...)
    description: Optional[str] = Field(None)
    frequency: ScheduleFrequency = Field(...)
    indexing_type: IndexingType = Field(...)
    time: datetime = Field(...)
    hours_interval: Optional[int] = Field(None)
    website_id: Optional[int] = Field(None)

    class Config:
        from_attributes = True

class ScheduledTaskUpdateSchema(BaseModel):
    name: Optional[str] = Field(None)
    description: Optional[str] = Field(None)
    frequency: Optional[ScheduleFrequency] = Field(None)
    indexing_type: Optional[IndexingType] = Field(None)
    time: Optional[datetime] = Field(None)
    is_active: Optional[bool] = Field(None)
    is_scheduled: Optional[bool] = Field(None)
    hours_interval: Optional[int] = Field(None)
    last_run: Optional[datetime] = Field(None)
    next_run: Optional[datetime] = Field(None)
    website_id: Optional[int] = Field(None)

    class Config:
        from_attributes = True