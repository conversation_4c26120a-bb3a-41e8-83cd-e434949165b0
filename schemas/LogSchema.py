from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, List
from schemas.WebsiteSchema import WebsiteSchema

class LogSchema(BaseModel):
    log_id: int = Field(...)
    website_id: Optional[int] = Field(...)
    event_type: str = Field(...)
    event_description: str = Field(...)
    created_at: datetime = Field(...)

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

class LogCreateSchema(BaseModel):
    website_id: Optional[int] = Field(None)
    event_type: str = Field(...)
    event_description: str = Field(...)