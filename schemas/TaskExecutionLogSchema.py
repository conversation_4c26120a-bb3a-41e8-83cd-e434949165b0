from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional
from enum import Enum

class TaskExecutionLogSchema(BaseModel):
    log_id: int = Field(...)
    task_id: int = Field(...)
    execution_time: datetime = Field(...)
    status: str = Field(...)
    message: Optional[str] = Field(None)
    total_products_indexed: int = Field(...)
    duration: int = Field(...)  # in seconds

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }

class TaskExecutionLogCreateSchema(BaseModel):
    task_id: int = Field(...)
    status: str = Field(...)
    message: Optional[str] = Field(None)
    total_products_indexed: int = Field(...)
    duration: int = Field(...)  # in seconds

    class Config:
        from_attributes = True