{"filters": [{"displayName": "Produkttyp", "filter_type_id": "PRODUCT_TYPE", "field": "Produkttyp", "type": "checkbox", "order": 1}, {"displayName": "<PERSON><PERSON><PERSON>", "filter_type_id": "MANUFACTURER", "field": "vendor", "type": "checkbox", "order": 2}, {"displayName": "<PERSON><PERSON><PERSON>", "filter_type_id": "CATEGORY", "field": "cat", "type": "checkbox", "order": 3}, {"displayName": "Pre<PERSON>", "filter_type_id": "PRICE", "field": "price", "type": "input_range", "order": 4}]}