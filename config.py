import os
from dotenv import load_dotenv
from fastapi.templating import Jinja2Templates

# Load .env file
load_dotenv(override=True)

# Configuration from environment variables
ELASTICSEARCH_URL = os.getenv('ELASTICSEARCH_URL', 'http://localhost:9200')
ELASTIC<PERSON>ARCH_USERNAME = os.getenv('ELASTICSEARCH_USERNAME', 'your_username')
ELASTICSEARCH_PASSWORD = os.getenv('ELASTICSEARCH_PASSWORD', 'your_password')
ELASTICSEARCH_RELAY_URL = os.getenv('ELASTICSEARCH_RELAY_URL')
PORT = int(os.getenv('PORT', 8000))

# database configuration
MARIADB_HOST = os.getenv('MARIADB_HOST')
MARIADB_PORT = os.getenv('MARIADB_PORT')
MARIADB_USER = os.getenv('MARIADB_USER')
MARIADB_PASSWORD = os.getenv('MARIADB_PASSWORD')
MARIADB_DB = os.getenv('MARIADB_DB')

SECRET_KEY = os.getenv('SECRET_KEY')
ALGORITHM = os.getenv('ALGORITHM')
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv('ACCESS_TOKEN_EXPIRE_MINUTES'))
COOKIE_NAME = os.getenv('COOKIE_NAME')

# cdn domain
CDN_DOMAIN = os.getenv('CDN_DOMAIN')

BASE_DIR = os.path.dirname(os.path.abspath(__file__))

ENV = os.getenv('ENV', 'dev')

ALLOWED_DEV_ORIGINS = os.getenv('ALLOWED_DEV_ORIGINS', '').split(',')

DEFAULT_LANGUAGE = os.getenv('DEFAULT_LANGUAGE')
SUPPORTED_LOCALES = os.getenv('SUPPORTED_LOCALES', '').split(',')

# Templates
templates = Jinja2Templates(directory="templates")