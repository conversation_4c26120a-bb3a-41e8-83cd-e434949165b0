from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import Request, <PERSON><PERSON><PERSON>
from typing import Callable
from babel.support import Translations
from babel import Locale
from config import DEFAULT_LANGUAGE, SUPPORTED_LOCALES

class TranslationMiddleware(BaseHTTPMiddleware):
    def __init__(self, app: FastAPI, default_locale: str = DEFAULT_LANGUAGE, supported_locales: list[str] = SUPPORTED_LOCALES):
        super().__init__(app)
        self.default_locale = default_locale
        self.supported_locales = supported_locales
        self.translations = {locale: Translations.load('locales', locale) for locale in supported_locales}

    async def dispatch(self, request: Request, call_next: Callable):
        # Check for preferred language in cookie
        preferred_lang = request.cookies.get("preferred_language")
        
        if preferred_lang in self.supported_locales:
            locale = preferred_lang
        else:
            # Use default locale if preferred_language cookie is not set
            locale = self.default_locale
            
        # Set the locale and translations in request.state
        request.state.locale = locale
        request.state.translations = self.translations[locale]
        request.state._ = self.translations[locale].gettext
        
        # Make translation function available to global templates
        from config import templates  # Import the global templates object
        templates.env.globals['_'] = request.state._
        
        # Process the request
        response = await call_next(request)
        
        return response
