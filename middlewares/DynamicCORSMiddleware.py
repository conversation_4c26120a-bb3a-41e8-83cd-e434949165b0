from fastapi import <PERSON><PERSON><PERSON>, Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
from dependencies.DependencyContainer import dependency_container
from cachetools import TTL<PERSON>ache, cached
from config import <PERSON>NV, ALLOWED_DEV_ORIGINS

cache = TTLCache(maxsize=100, ttl=300)  # Cache up to 100 items, each for 5 minutes

class CustomCORSMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app)
        self.website_service = dependency_container.get_website_service()

    async def dispatch(self, request: Request, call_next):
        if request.method == "OPTIONS":
            return await self.handle_preflight(request)
        
        response = await call_next(request)
        return await self.add_cors_headers(request, response)

    async def handle_preflight(self, request: Request):
        response = JSONResponse({})
        return await self.add_cors_headers(request, response)

    async def add_cors_headers(self, request: Request, response):
        origin = request.headers.get("Origin")
        
        if await self.is_origin_allowed(origin):
            response.headers["Access-Control-Allow-Origin"] = origin
            response.headers["Access-Control-Allow-Credentials"] = "true"
            response.headers["Access-Control-Allow-Methods"] = "*"
            response.headers["Access-Control-Allow-Headers"] = "*"
        
        return response

    async def is_origin_allowed(self, origin):
        try:
            if not origin:
                return False
            if ENV == 'dev' and origin in ALLOWED_DEV_ORIGINS:
                    return True
            
            websites = self.cached_websites()
            # Check both 'http' and 'https' versions
            for website in websites:
                if (origin == self.full_domain(website.website_url, 'https://') or
                    origin == self.full_domain(website.website_url, 'http://')):
                    return True
        except Exception as e:
            print(e)
            return False

    @cached(cache)
    def cached_websites(self):
        try:
            return self.website_service.get_all_websites()
        except Exception as e:
            cache.clear()
            raise e

    def full_domain(self, website_url, protocol='https://'):
        # Ensure the URL starts with the correct protocol
        if website_url.startswith('http://') or website_url.startswith('https://'):
            return website_url
        else:
            return protocol + website_url