# cors.py
import json
from typing import Set

allowed_origins: Set[str] = set()

def load_allowed_origins() -> None:
    """Load allowed origins from a JSON file."""
    global allowed_origins
    with open("allowed_origins.json", "r") as f:
        allowed_origins = set(json.load(f))

def update_allowed_origins(new_origins: Set[str]) -> None:
    """Update the allowed origins with a new set of origins."""
    if not new_origins or not isinstance(new_origins, set) or not len(new_origins):
        raise ValueError("New origins must be a non-empty set of strings")
    global allowed_origins
    new_origins = {origin if origin.startswith('https://') else 'https://' + origin for origin in new_origins}
    allowed_origins.update(new_origins)

    # Save the updated allowed origins to the JSON file
    with open("allowed_origins.json", "w") as f:
        json.dump(list(allowed_origins), f)


def get_allowed_origins() -> Set[str]:
    """Return the current set of allowed origins."""
    global allowed_origins
    print(f"Allowed origins: {allowed_origins}")
    return allowed_origins
