import sys
sys.path.append('..')

import logging
from models.models import User, Role
from models.Connexion import <PERSON><PERSON><PERSON><PERSON>
from enums.role_enum import RoleEnum

# Set up logging
logging.basicConfig(level=logging.INFO)

def seed_admin_account(db):
    try:
        admin = User.get_by_email(db, '<EMAIL>')
        if not admin:
            admin_role = Role.get_by_name(db, RoleEnum.ADMIN.value)
            if not admin_role:
                logging.error("Admin role not found. Please run the role seeder first.")
                return

            admin = User(
                email='<EMAIL>',
                password='admin',
                role_id=admin_role.role_id,
                username='admin',
                is_admin=True,
                is_active=True
            )
            User.create(db, admin)
            logging.info("Seeded admin account")
        else:
            logging.info("Admin account already exists")
    except Exception as e:
        logging.error(f"Error seeding admin account: {e}")
        db.rollback()
    finally:
        db.close()

def seed():
    with SessionLocal() as db:
        seed_admin_account(db)

if __name__ == '__main__':
    seed()