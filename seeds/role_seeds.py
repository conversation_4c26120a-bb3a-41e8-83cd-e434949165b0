import sys
sys.path.append('..')

import logging
from models.models import Role, Permission, RolePermission
from models.Connexion import SessionLocal

# Set up logging
logging.basicConfig(level=logging.INFO)

roles = ['ADMIN', 'CLIENT']  # Add your roles here
role_permissions = {
    'ADMIN': [
        'manage_users', 'manage_websites', 'manage_reports', 'manage_roles'
    ],
    'CLIENT': ['view_reports', 'manage_website_settings', 'view_website', 'manage_imports']
}

def seed_roles(db):
    try:
        seeded_roles = {}
        for role in roles:
            role_obj = Role.get_by_name(db, role)
            if not role_obj:
                role_obj = Role.create(db, Role(name=role))
            seeded_roles[role] = role_obj.role_id
        return seeded_roles
    except Exception as e:
        logging.error(f"Error seeding roles: {e}")
        db.rollback()
        raise e

def seed_permissions(db):
    try:
        seeded_permissions = {}
        for role, permissions in role_permissions.items():
            for permission in permissions:
                permission_obj = Permission.get_by_name(db, permission)
                if not permission_obj:
                    permission_obj = Permission.create(db, Permission(name=permission))
                seeded_permissions[permission] = permission_obj.permission_id
        return seeded_permissions
    except Exception as e:
        logging.error(f"Error seeding permissions: {e}")
        db.rollback()
        raise e

def seed_role_permissions(db, seeded_roles, seeded_permissions):
    try:
        for role, permissions in role_permissions.items():
            role_id = seeded_roles[role]
            for permission in permissions:
                permission_id = seeded_permissions[permission]
                role_permission = RolePermission.get_by_role_id_and_permission_id(db, role_id, permission_id)
                if not role_permission:
                    RolePermission.create(db, RolePermission(role_id=role_id, permission_id=permission_id))
    except Exception as e:
        logging.error(f"Error seeding role permissions: {e}")
        db.rollback()
        raise e

def seed():
    with SessionLocal() as db:
        try:
            logging.info("Seeding roles...")
            seeded_roles = seed_roles(db)
            logging.info("Seeding permissions...")
            seeded_permissions = seed_permissions(db)
            logging.info("Seeding role permissions...")
            seed_role_permissions(db, seeded_roles, seeded_permissions)
            logging.info("Seeding complete for roles and permissions!")
        except Exception as e:
            logging.error(f"Error seeding data: {e}")
            db.rollback()
            raise e

if __name__ == '__main__':
    seed()