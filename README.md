# FastAPI Elasticsearch Proxy

This FastAPI application acts as a proxy between a frontend application and an Elasticsearch cluster. It ensures secure and controlled access to Elasticsearch data, providing additional layers of validation and data processing.

## Features

- **FastAPI Framework**: Asynchronous request handling.
- **Elasticsearch Integration**: Direct communication with Elasticsearch.
- **Query Rewriting/Validation**: Ensure that only allowed queries are passed to Elasticsearch.

## Getting Started

These instructions will get you a copy of the project up and running on your local machine for development and testing purposes.

### Prerequisites

You need to have Python 3.6+ installed on your machine. Ensure you have access to your Elasticsearch cluster.

### Running the FastAPI Application

Clone the repository:

```bash
<NAME_EMAIL>:Antoine/elasticsearch-proxy.git
cd elasticsearch-proxy
```

To run your FastAPI application, follow these steps:

1. Activate your virtual environment (if you are using one):

    ```bash
    source venv/bin/activate  # If you're using a virtual environment named 'venv'
    ```

2. Install the required dependencies:

    ```bash
    pip3 install -r requirements.txt  # Make sure this file contains all the necessary packages
    ```
    In case it does not work for any reasons, install them using `apt install`

3. Start the FastAPI server:

    ```bash
    python3 main.py
    ```

**Running with Gunicorn (optional)**:

For production environments, you should use a process manager like Gunicorn to run the application with multiple worker processes. Here is an example command to do so:

```bash
gunicorn -w 4 -k uvicorn.workers.UvicornWorker app.main:app --bind 0.0.0.0:8000
```
if the depencies were installed using `apt install`, use

```bash
python3 -m gunicorn -w 4 -k uvicorn.workers.UvicornWorker app.main:app --bind 0.0.0.0:8000
```

### Configure Environment Variables

To temporarily set the environment variables for the current session, use the following commands:

```bash
export ELASTICSEARCH_HOST="http://localhost:9200"
export ELASTICSEARCH_USERNAME="elastic"
export ELASTICSEARCH_PASSWORD="changeme"
```

To make these environment variables permanent, you need to add them to your shell's profile script.

For Bash users, add the following lines to your `~/.bashrc` file:

```bash
echo 'export ELASTICSEARCH_HOST="http://localhost:9200"' >> ~/.bashrc
echo 'export ELASTICSEARCH_USERNAME="elastic"' >> ~/.bashrc
echo 'export ELASTICSEARCH_PASSWORD="changeme"' >> ~/.bashrc
```

Then, apply the changes with:

```bash
source ~/.bashrc
```

Or set them in /etc/enrionment.

```bash
ELASTICSEARCH_HOST="http://localhost:9200"
ELASTICSEARCH_USERNAME="elastic"
ELASTICSEARCH_PASSWORD="changeme"
```
Then, apply the changes with:

```bash
source /etc/environment
```

After executing these commands, the environment variables will be set automatically every time you start a new terminal session.

**Important Security Notice**: Storing sensitive information, such as passwords, in plaintext in your shell's profile script is not recommended for production environments. Use a more secure method for managing secrets, such as a password manager or environment management tools.

### Setting Up NGINX for FastAPI

NGINX can be used as a reverse proxy to forward requests to your FastAPI application. This can help you manage SSL/TLS termination, load balancing, and other concerns. Follow the steps below to configure NGINX for your FastAPI app.

1. Install NGINX on your server:

    For Ubuntu/Debian systems:

    ```bash
    sudo apt update
    sudo apt install nginx
    ```

2. Configure NGINX to forward requests to your FastAPI application. Create a new NGINX configuration file in `/etc/nginx/sites-available/` with the following content:

    ```nginx
    server {
        listen 80;
        server_name yourdomain.com; # Replace with your domain name

        #SSL certification
        #ssl_certificate /path/to/your/fullchain.pem;
        #ssl_certificate_key /path/to/your/privkey.pem;

        location / {
            proxy_pass http://localhost:8000; # Assuming FastAPI is running on this port
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $server_name;
            proxy_set_header X-Forwarded-Port $server_port;

        }
    }

    server {
        listen 80;
        server_name fast.toolbrothers.space;

        return 301 https://$host$request_uri;
    }
    ```

3. Enable the configuration by creating a symlink to it in `/etc/nginx/sites-enabled/`:

    ```bash
    sudo ln -s /etc/nginx/sites-available/your_fastapi_config /etc/nginx/sites-enabled/
    ```

4. Test the NGINX configuration for syntax errors:

    ```bash
    sudo nginx -t
    ```

5. Restart NGINX to apply the changes:

    ```bash
    sudo systemctl restart nginx
    ```

6. Ensure that your firewall settings allow traffic on port 80 (HTTP) and 443 (HTTPS, if using SSL).

**Note**: This is a basic configuration to get you started. In a production environment, you should also set up SSL/TLS encryption with certbot (Let's Encrypt), enable HTTP/2, and consider additional security settings and optimizations according to your needs.

**Security Tip**: Always keep your server and applications updated with the latest security patches and follow best practices for server hardening.


### Setting Up FastAPI to Run on Server Startup

To run your FastAPI application as a service that starts on server boot, you can create a systemd service file:

1. Create a systemd service file for your FastAPI application:

    ```bash
    sudo nano /etc/systemd/system/fastapi-app.service
    ```

2. Add the following content to the service file, adjusting the paths and user as necessary:

    ```ini
    [Unit]
    Description=FastAPI App Service
    After=network.target

    [Service]
    User=<your-user>  # Replace with the user you want to run the FastAPI app
    Group=<your-user-group>  # Replace with the group for the user
    WorkingDirectory=/path/to/your/fastapi/app  # Replace with the path to your FastAPI app
    EnvironmentFile=/path/to/env/variables  # Optional: Path to a file containing environment variables
    ExecStart=/path/to/your/venv/bin/gunicorn -w 4 -k uvicorn.workers.UvicornWorker main:app --bind 0.0.0.0:8000
    Restart=on-failure

    [Install]
    WantedBy=multi-user.target
    ```
    in the case the depencies were installed using `apt install`, use `ExecStart=python3 -m gunicorn -w 4 -k uvicorn.workers.UvicornWorker main:app --bind 0.0.0.0:8000`

3. Start the service with:

    ```bash
    sudo systemctl start fastapi-app.service
    ```

4. Enable the service to start on boot:

    ```bash
    sudo systemctl enable fastapi-app.service
    ```

5. You can check the status of the service with:

    ```bash
    sudo systemctl status fastapi-app.service
    ```

**Note**: Make sure to replace `/path/to/your/fastapi/app` with the actual path to your FastAPI application directory, `/path/to/your/venv` with the path to your virtual environment if you're using one, and `app.main:app` with the actual path to your FastAPI application instance. If you're not using a virtual environment, replace the `ExecStart` path with the global Uvicorn installation path (e.g., `/usr/local/bin/uvicorn`).

**Security Tip**: Running your application with superuser privileges is not recommended. Create a dedicated user with limited permissions to run your FastAPI application.

This setup ensures that your FastAPI application is treated as a service, allowing it to automatically start when the server boots up and restart if it fails.