from typing import Optional
from fastapi import <PERSON><PERSON>out<PERSON>, Depends, Query, Request, Form
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from controllers.schedulerController import SchedulerController

router = APIRouter()
templates = Jinja2Templates(directory="templates")

# Scheduled Tasks routes

@router.get("/admin/tasks/monitor", response_class=HTMLResponse)
async def tasks_monitor(request: Request, schedulerController: SchedulerController = Depends(SchedulerController)):
    return await schedulerController.tasks_monitor(request)

@router.get("/admin/tasks/create", response_class=HTMLResponse)
async def create_task(request: Request, schedulerController: SchedulerController = Depends(SchedulerController)):
    return await schedulerController.create_task_form(request)

@router.post("/admin/tasks/create")
async def create_task_submit(request: Request, schedulerController: SchedulerController = Depends(SchedulerController)):
    return await scheduler<PERSON>ontroller.create_task_submit(request)

@router.get("/admin/tasks/list", response_class=HTMLResponse)
async def list_tasks(
    request: Request, 
    page: int = Query(None),
    per_page: int = Query(None),
    schedulerController: SchedulerController = Depends(SchedulerController)
):
    return await schedulerController.get_all_tasks(request, page, per_page)

@router.get("/admin/tasks/{task_id}/view")
async def view_task(task_id: int, request: Request, schedulerController: SchedulerController = Depends(SchedulerController)):
    return await schedulerController.get_task_by_id(request, task_id)

@router.post("/admin/scheduler/refresh")
async def refresh_scheduler(request: Request, schedulerController: SchedulerController = Depends(SchedulerController)):
    return await schedulerController.refresh_scheduler(request)


@router.put("/admin/tasks/{task_id}/reschedule")
async def reschedule_task(request: Request, task_id: int, schedulerController: SchedulerController = Depends(SchedulerController)):
    return await schedulerController.reschedule_task(request, task_id)

@router.put("/admin/tasks/{task_id}/deactivate")
async def deactivate_task(request: Request, task_id: int, schedulerController: SchedulerController = Depends(SchedulerController)):
    return await schedulerController.deactivate_task(request, task_id)

@router.put("/admin/tasks/{task_id}/activate")
async def activate_task(request: Request, task_id: int, schedulerController: SchedulerController = Depends(SchedulerController)):
    return await schedulerController.activate_task(request, task_id)

@router.delete("/admin/tasks/{task_id}/delete")
async def delete_task(request: Request, task_id: int, schedulerController: SchedulerController = Depends(SchedulerController)):
    return await schedulerController.delete_task(request, task_id)

# Task Execution Logs routes

@router.get("/admin/logs/view", response_class=HTMLResponse)
async def view_logs(
    request: Request, 
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    schedulerController: SchedulerController = Depends(SchedulerController)
):
    logs = await schedulerController.get_logs_for_all_websites(page, per_page)
    return templates.TemplateResponse("/adminview_logs.html", {"request": request, "logs": logs})

@router.get("/admin/logs/search", response_class=HTMLResponse)
async def search_logs(request: Request):
    return templates.TemplateResponse("/adminsearch_logs.html", {"request": request})

@router.post("/admin/logs/search")
async def search_logs_submit(
    request: Request,
    start_date: str = Form(...),
    end_date: str = Form(...),
    website_id: Optional[int] = Form(None),
    schedulerController: SchedulerController = Depends(SchedulerController)
):
    logs = await schedulerController.search_logs(start_date, end_date, website_id)
    return JSONResponse(content={"logs": logs})

@router.get("/admin/logs/{log_id}", response_class=HTMLResponse)
async def view_log(log_id: int, request: Request, schedulerController: SchedulerController = Depends(SchedulerController)):
    log = await schedulerController.get_log_by_id(log_id)
    return templates.TemplateResponse("/adminview_log.html", {"request": request, "log": log})
