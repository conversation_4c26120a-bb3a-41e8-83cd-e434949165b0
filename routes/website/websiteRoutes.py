from typing import Optional
from fastapi import <PERSON><PERSON>out<PERSON>, Depends, Path, Query, Request
from fastapi.responses import HTMLResponse, JSONResponse
from controllers.dashboardController import DashboardController
from controllers.websiteController import WebsiteController

router = APIRouter()

@router.get("/admin/website/register", response_class=HTMLResponse)
async def website_register(request: Request, website_controller: WebsiteController = Depends(WebsiteController)):
    return await website_controller.load_website_registration_form(request)

@router.get("/user/website/switch", response_class=HTMLResponse)
async def switch_website(request: Request, selectedWebsite: str = Query(...), dashboardController: DashboardController = Depends(DashboardController)):
    return await dashboardController.switch_website(request, selectedWebsite)


@router.post("/admin/website/register", response_class=HTMLResponse)
async def website_register(request: Request, website_controller: WebsiteController = Depends(WebsiteController)):
    return await website_controller.create_website(request)

# update website settings only api call
@router.put("/user/website/settings/{website_settings_id}", response_class=JSONResponse)
async def website_settings(request: Request, website_settings_id: int=Path(...), website_controller: WebsiteController = Depends(WebsiteController)):
    # allow only auth users
    return await website_controller.update_website_settings(request, website_settings_id)

# update website selectors settings only api call
@router.put("/user/website/settings/{website_settings_id}/selectors", response_class=JSONResponse)
async def website_settings(request: Request, website_settings_id: int=Path(...), website_controller: WebsiteController = Depends(WebsiteController)):
    # allow only auth users
    return await website_controller.update_website_selectors(request, website_settings_id)

@router.put("/user/website/{website_id}", response_class=JSONResponse)
async def website_update(request: Request, website_id: int=Path(...), website_controller: WebsiteController = Depends(WebsiteController)):
    # allow only auth users
    return await website_controller.update(request, website_id)

@router.get("/admin/website/review", response_class=HTMLResponse)
async def websites_review(request: Request, website_controller: WebsiteController = Depends(WebsiteController)):
    return await website_controller.websites_review(request)

# delete website
@router.delete("/admin/website/delete/{website_id}", response_class=JSONResponse)
async def delete_website(request: Request, website_id: int=Path(...), website_controller: WebsiteController = Depends(WebsiteController)):
    return await website_controller.delete_website(request, website_id)