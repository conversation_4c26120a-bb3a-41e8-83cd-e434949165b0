from typing import Optional
from fastapi import APIRout<PERSON>, Depends, Query, Request
from fastapi.responses import JSONResponse
from controllers.logController import LogController
from config import templates

router = APIRouter()

@router.get("/admin/logs")
async def get_logs(request: Request, log_controller: LogController = Depends(LogController)):
    return templates.TemplateResponse("admin/logs/index.html", {"request": request})

@router.get("/admin/view-logs", response_class=JSONResponse)
async def get_log_list(
    request: Request,
    draw: int = Query(0),
    start: Optional[int] = Query(0),
    length: Optional[int] = Query(10),
    log_controller: LogController = Depends(LogController)
):
    return await log_controller.get_logs(
        request=request,
        draw=draw,
        start=start,
        length=length
    )