from fastapi import <PERSON><PERSON><PERSON><PERSON>, BackgroundTasks, HTTPException, Depends, Request
from background_jobs.product_indexation import Integrator
from fastapi.concurrency import run_in_threadpool
from collections import defaultdict
import asyncio
from sse_starlette.sse import EventSourceResponse
from services.authService import AuthService

router = APIRouter()
user_website_tasks = defaultdict(lambda: defaultdict(asyncio.Lock))
user_connections = defaultdict(set)

async def handle_indexing(website_key: str):
    integrator = Integrator()
    await run_in_threadpool(integrator.manual_indexation, website_key)

@router.post("/client/trigger-manual-indexing/{website_key}")
async def trigger_manual_indexing(
    request: Request,
    website_key: str, 
    background_tasks: BackgroundTasks,
    auth_service: AuthService = Depends(AuthService),
):
    current_user = auth_service.is_authenticated(request)
    current_user = current_user.user_id
    
    user_tasks = user_website_tasks[current_user]
    lock = user_tasks[website_key]
    
    if lock.locked():
        raise HTTPException(
            status_code=409,
            detail="An indexation process is already running for this website. Please try again later."
        )
    
    background_tasks.add_task(run_indexing, current_user, website_key)
    return {"message": f"Manual import started for website {website_key}"}

async def run_indexing(user_id: str, website_key: str):
    lock = user_website_tasks[user_id][website_key]
    async with lock:
        try:
            await handle_indexing(website_key)
            await notify_user(user_id, f"Indexation complete for website {website_key}")
        except Exception as e:
            error_message = f"Error during indexation for user {user_id}, website {website_key}: {str(e)}"
            print(error_message)
            await notify_user(user_id, f"Indexation failed for website {website_key}: {str(e)}")

async def notify_user(user_id: str, message: str):
    if user_id in user_connections:
        for connection in user_connections[user_id]:
            await connection.put({"data": message})

@router.get("/sse")
async def sse(request: Request, auth_service: AuthService = Depends(AuthService)):
    current_user = auth_service.is_authenticated(request)
    current_user = current_user.user_id

    async def event_generator():
        connection = asyncio.Queue()
        user_connections[current_user].add(connection)
        try:
            while True:
                if await request.is_disconnected():
                    break
                message = await connection.get()
                yield message
        finally:
            user_connections[current_user].remove(connection)

    return EventSourceResponse(event_generator())