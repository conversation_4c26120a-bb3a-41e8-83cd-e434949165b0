from functools import wraps
import random
from typing import Dict, Optional
from fastapi import API<PERSON><PERSON><PERSON>, Depends, Query, Request, HTTPException
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.responses import HTM<PERSON>esponse, RedirectResponse
from config import COOKIE_NAME
from controllers.authController import login_for_access_token
from dependencies.db_dependencies import get_db
from sqlalchemy.orm import Session

from slowapi import Limiter
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from datetime import datetime, timedelta

from utils.exceptionUtil import rate_limit_exceeded_handler
from services.securityService import SecurityService

from config import templates

# Constants
LOGIN_TEMPLATE = "account/login.html"
ERROR_500_TEMPLATE = "errors/error500.html"

router = APIRouter()

security_service = SecurityService()

# Store rate limit and failed attempts info
rate_limit_info: Dict[str, Dict] = {}
failed_attempts: Dict[str, Dict] = {}

def is_rate_limit_exceeded(client_ip: str) -> bool:
    """Check if the rate limit has been exceeded for the given client IP."""
    now = datetime.now()
    limit = security_service.get_login_rate_limit()['limit']
    rate, per = limit.split('/')
    rate = int(rate)
    
    if per == 'minute':
        time_frame = timedelta(minutes=1)
    elif per == 'hour':
        time_frame = timedelta(hours=1)
    else:
        raise ValueError(f"Unsupported time frame: {per}")

    if client_ip not in rate_limit_info:
        rate_limit_info[client_ip] = {'attempts': [now], 'cooldown': None}
        return False

    attempts = rate_limit_info[client_ip]['attempts']
    attempts = [t for t in attempts if now - t <= time_frame]
    
    if len(attempts) < rate:
        attempts.append(now)
        rate_limit_info[client_ip]['attempts'] = attempts
        return False
    
    return True

def is_cooldown_active(client_ip: str) -> bool:
    """Check if the cooldown period is active for the given client IP."""
    now = datetime.now()
    if client_ip not in rate_limit_info or rate_limit_info[client_ip]['cooldown'] is None:
        return False

    cooldown = rate_limit_info[client_ip]['cooldown']
    if now < cooldown['ends']:
        return True

    return False

def update_cooldown(client_ip: str):
    """Update the cooldown period for the given client IP."""
    now = datetime.now()
    cooldown_config = security_service.get_cooldown_time_config()
    cooldown = timedelta(**{cooldown_config['unit']: cooldown_config['duration']})

    if client_ip not in rate_limit_info:
        rate_limit_info[client_ip] = {'attempts': [], 'cooldown': None}

    if rate_limit_info[client_ip]['cooldown'] is None:
        rate_limit_info[client_ip]['cooldown'] = {'starts': now, 'ends': now + cooldown, 'attempts': 1}
    else:
        attempts = rate_limit_info[client_ip]['cooldown']['attempts']
        rate_limit_info[client_ip]['cooldown']['ends'] += cooldown * (2 ** attempts)
        rate_limit_info[client_ip]['cooldown']['attempts'] += 1

def update_failed_attempts(client_ip: str, success: bool):
    """Update the failed attempts count for the given client IP."""
    if not security_service.is_failed_login_attempts_enabled():
        return

    now = datetime.now()
    if client_ip not in failed_attempts:
        failed_attempts[client_ip] = {'count': 0, 'last_attempt': now}

    if success:
        failed_attempts[client_ip]['count'] = 0
    else:
        failed_attempts[client_ip]['count'] += 1
        failed_attempts[client_ip]['last_attempt'] = now

@router.get("/login", response_class=HTMLResponse)
async def login(request: Request):
    return templates.TemplateResponse(LOGIN_TEMPLATE, {"request": request})

@router.post("/login")
async def login_submit(request: Request, db: Session = Depends(get_db)):
    client_ip = get_remote_address(request)
    form_data = await request.form()

    if is_cooldown_active(client_ip):
        cooldown = rate_limit_info[client_ip]['cooldown']
        remaining_time = cooldown['ends'] - datetime.now()
        return templates.TemplateResponse(LOGIN_TEMPLATE, {
            "request": request,
            "error": f"Cooldown period is active. Please try again in {remaining_time.total_seconds():.0f} seconds.",
            "email": form_data.get("email", "")
        })

    if is_rate_limit_exceeded(client_ip):
        update_cooldown(client_ip)
        cooldown = rate_limit_info[client_ip]['cooldown']
        remaining_time = cooldown['ends'] - datetime.now()
        return templates.TemplateResponse(LOGIN_TEMPLATE, {
            "request": request,
            "error": f"Rate limit exceeded. Cooldown period is {remaining_time.total_seconds():.0f} seconds.",
            "email": form_data.get("email", "")
        })

    try:
        oauth2_request_form = OAuth2PasswordRequestForm(username=form_data["email"], password=form_data["password"])
        response_redirect = await login_for_access_token(db, oauth2_request_form)
        update_failed_attempts(client_ip, success=True)
        return response_redirect

    except HTTPException as http_exc:
        update_failed_attempts(client_ip, success=False)
        error = http_exc.detail
        return templates.TemplateResponse(LOGIN_TEMPLATE, {"request": request, "error": error, "email": form_data.get("email", "")})

    except Exception as exc:
        # Log the exception for debugging purposes
        import traceback
        traceback.print_exc()
        return templates.TemplateResponse(ERROR_500_TEMPLATE, {"request": request, "error": "An error occurred while processing the request."})

@router.get("/logout")
async def user_logout(request: Request):
    response = RedirectResponse(url="/login")
    response.delete_cookie(COOKIE_NAME)
    return response