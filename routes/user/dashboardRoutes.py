import json
from typing import Op<PERSON>
from fastapi import APIRouter, Depends, Path, Query, Request
from fastapi.responses import HTMLResponse, JSONResponse
from controllers.dashboardController import DashboardController

router = APIRouter()

@router.get("/", response_class=HTMLResponse)
async def user_home(request: Request, dashboardController: DashboardController = Depends(DashboardController)):
    return await dashboardController.user_home(request)

@router.get("/admin", response_class=HTMLResponse)
async def admin_home(request: Request, dashboardController: DashboardController = Depends(DashboardController)):
    return await dashboardController.admin_home(request)

@router.get("/data/synchronize", response_class=HTMLResponse)
async def data_synchronize(request: Request, dashboardController: DashboardController = Depends(DashboardController)):
    return await dashboardController.data_synchronization(request)

@router.get("/frontend/integration", response_class=HTMLResponse)
async def frontend_integration(request: Request, dashboardController: DashboardController = Depends(DashboardController)):
    return await dashboardController.frontend_integration(request)

@router.get("/frontend/customization", response_class=HTMLResponse)
async def frontend_customization(request: Request, dashboardController: DashboardController = Depends(DashboardController)):
    return await dashboardController.frontend_customization(request)

@router.get("/admin/filter/manage", response_class=HTMLResponse)
async def manage_filter(request: Request, dashboardController: DashboardController = Depends(DashboardController)):
    return await dashboardController.manage_sample_filters(request)

@router.get("/admin/websites/monitor", response_class=HTMLResponse)
async def monitor_websites(request: Request, user_id: Optional[int] = Query(None), dashboardController: DashboardController = Depends(DashboardController)):
    return await dashboardController.monitor_websites(request, user_id)

@router.put("/admin/website/{website_id}/toggle-pause", response_class=JSONResponse)
async def pause_website(request: Request, website_id: int = Path(...), dashboardController: DashboardController = Depends(DashboardController)):
    return await dashboardController.pause_website(request, website_id)

@router.get("/admin/websites/{website_id}/settings", response_class=JSONResponse)
async def website_settings(request: Request, website_id: int = Path(...), dashboardController: DashboardController = Depends(DashboardController)):
    return await dashboardController.website_settings_view(request, website_id)

@router.put("/admin/websites/{website_id}/settings", response_class=JSONResponse)
async def website_settings(request: Request, website_id: int = Path(...), dashboardController: DashboardController = Depends(DashboardController)):
    return await dashboardController.website_settings_update(request, website_id)

@router.post("/admin/filter/manage", response_class=HTMLResponse)
async def manage_filter_submit(request: Request, dashboardController: DashboardController = Depends(DashboardController)):
    return await dashboardController.manage_sample_filters_submit(request)      

@router.get("/reports/search_terms", response_class=HTMLResponse)
async def reports_search_terms(request: Request, dashboardController: DashboardController = Depends(DashboardController)):
    return await dashboardController.search_terms_report(request)

# get search terms for datatables with pagination
@router.get("/reports/search_terms/list", response_class=JSONResponse)
async def reports_search_terms_list(
    request: Request,
    websiteKey: Optional[str] = Query(None),
    draw: int = Query(0),
    start: Optional[int] = Query(0),
    length: Optional[int] = Query(10),
    startDate: Optional[str] = Query(None),
    endDate: Optional[str] = Query(None),
    order: Optional[str] = Query(None),  # New: To capture sorting order
    search: Optional[str] = Query(None),  # New: To capture search query
    dashboardController: DashboardController = Depends(DashboardController)
):
    # Parse sorting parameters if they exist
    sort_by = []
    if order:
        try:
            order_params = json.loads(order)
            sort_by = [(o['column'], o['dir']) for o in order_params]
        except Exception as e:
            print(f"Failed to parse sorting parameters: {e}")
    return await dashboardController.search_terms_listing(
        request, websiteKey, draw, start, length, startDate, endDate, sort_by, search
    )

@router.put("/api/update-filter-order")
async def update_filter_order(request: Request, dashboardController: DashboardController = Depends(DashboardController)):
    return await dashboardController.update_filter_order(request)

@router.put('/api/update-filter-display-name/', response_class=JSONResponse)
async def update_filter_display_name(request: Request, dashboardController: DashboardController = Depends(DashboardController)):
    return await dashboardController.update_filter_display_name(request)