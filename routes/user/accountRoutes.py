from fastapi import APIRout<PERSON>, Depends, Form, Path, Request, HTTPException, status
from fastapi.encoders import jsonable_encoder
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse
from sqlalchemy.orm import Session
from controllers.authController import is_authenticated
from enums.role_enum import RoleEnum
from models.models import User
from controllers.userController import UserController
from controllers.roleController import RoleController
from dependencies.db_dependencies import get_db
from schemas.UserSchema import UserCreateSchema, UserUpdateSchema

router = APIRouter()

from config import templates

@router.get("/account/register", response_class=HTMLResponse)
async def register(request: Request, db: Session=Depends(get_db)):
    try:
        roles = RoleController.get_all(db)
        return templates.TemplateResponse("account/register.html", {"request": request, "roles": roles})
    except Exception as exc:
        print(exc)
        return templates.TemplateResponse("account/register.html", {"request": request, "error": "Could not load roles."})

@router.post("/account/register")
async def register(
    request: Request, 
    user_controller: UserController=Depends(UserController)
):
    return await user_controller.create(request)

@router.get("/account/review", response_class=HTMLResponse)
async def account_review(request: Request, user_controller: UserController=Depends(UserController)):
    users = await user_controller.get_all(request)
    return templates.TemplateResponse("account/review.html", {"request": request, "users": users})

# account delete
@router.delete("/account/delete/{user_id}", response_class=JSONResponse)
async def account_delete(request: Request, user_id: int = Path(...), user_controller: UserController=Depends(UserController)):
    return await user_controller.delete(request, user_id)

# account update
@router.put("/account/update/{user_id}", response_class=JSONResponse)
async def account_update(request: Request, user_id: int = Path(...), user_controller: UserController = Depends(UserController)):
    return await user_controller.account_update(request, user_id)

# validate email address
@router.post("/account/validate_email")
async def validate_email(request: Request, email: str = Form(...), user_controller: UserController = Depends(UserController)):
    return user_controller.email_validation(email)

# validate username
@router.post("/account/validate_username")
async def validate_username(request: Request, username: str = Form(...), user_controller: UserController = Depends(UserController)):
    return user_controller.username_validation(username)

# refresh user accounts table
@router.get("/account/refresh/users", response_class=HTMLResponse)
async def refresh_users(request: Request, user_controller: UserController = Depends(UserController)):
    return user_controller.refresh_users(request)

@router.post("/account/change_language")
async def change_language(lang: str = Form(...)):
    response = JSONResponse(content={"message": "Language changed successfully."})
    response.set_cookie(key="preferred_language", value=lang, httponly=False)
    return response
