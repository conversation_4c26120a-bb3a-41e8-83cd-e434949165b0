from fastapi import APIRouter, HTTPException, Request, Depends
import logging
from controllers.shopify_controller import ShopifyController

# Initialize router and controller
router = APIRouter(prefix="/shopify", tags=["shopify"])

# Configure logging
logger = logging.getLogger(__name__)

@router.post("/search")
async def search_products(request: Request, shopify_controller: ShopifyController = Depends(ShopifyController)):
    """
    Search Shopify product variants - matches ToolBrothers interface.

    Expected request body:
    {
        "index": "shopify_products",
        "pattern": "search query",
        "current_page": 0,
        "page_size": 20,
        "args": {
            "vendor": ["Makita"],
            "price": {"min": 50, "max": 200},
            "sort": "relevance"
        }
    }
    """
    try:
        data = await request.json()

        index = data.get('index')
        pattern = data.get('pattern', '')
        current_page = data.get('current_page', 0)
        page_size = data.get('page_size', 20)
        args = data.get('args', {})

        if not index:
            raise HTTPException(status_code=400, detail="Missing 'index' in request body")

        result = await shopify_controller.search_products(
            index=index,
            pattern=pattern,
            current_page=current_page,
            page_size=page_size,
            args=args
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Shopify search endpoint error: {e}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")
