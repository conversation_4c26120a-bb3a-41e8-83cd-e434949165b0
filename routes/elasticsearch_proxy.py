import json
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Query, Request, HTTPException, status
from fastapi.responses import JSONResponse
from controllers.dashboardController import DashboardController
from controllers.elasticsearch_controller import ElasticsearchController

router = APIRouter()

@router.get("/elasticsearch/filters/get", response_class=JSONResponse)
async def get_filters(request: Request, dashboardController: DashboardController = Depends(DashboardController)):
    return await dashboardController.get_search_filters(request)

@router.get("/elasticsearch/search")
async def search_products(
    request: Request,
    pattern: str = Query(..., description="The search pattern"),
    args: str = Query(..., description="JSON string of search arguments"),
    currentPage: int = Query(..., description="Current page for pagination"),
    pageSize: int = Query(..., description="Page size for pagination"),
    index: str = Query(..., description="The Elasticsearch index to search"),
    elasticsearch_controller: ElasticsearchController = Depends(ElasticsearchController)
):
    # Convert args from JSON string to a Python object
    try:
        parsed_args = json.loads(args)
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON for args")

    # Pass all the parsed parameters to the controller
    result = await elasticsearch_controller.search_products(
        pattern=pattern,
        args=parsed_args,
        current_page=currentPage,
        page_size=pageSize,
        index=index
    )
    
    return result

@router.post("/elasticsearch/update_top_searches")
async def update_top_searches(request: Request, elasticsearch_controller: ElasticsearchController = Depends(ElasticsearchController)):
    return await elasticsearch_controller.update_top_searches(request)

@router.get("/elasticsearch/load_initial_products")
async def load_initial_products_for_modal(
    topSearchPatterns: str = Query(..., description="JSON string of top search patterns"),
    products_index: str = Query(..., description="The index name for products"),
    elasticsearch_controller: ElasticsearchController = Depends(ElasticsearchController)
):
    # Convert the JSON string back to a Python object
    try:
        top_search_patterns = json.loads(topSearchPatterns)
    except json.JSONDecodeError:
        # Handle the case where the JSON string might be invalid
        raise HTTPException(status_code=400, detail="Invalid JSON for topSearchPatterns")

    # Call the controller with the parsed patterns and index
    return await elasticsearch_controller.load_initial_products_for_modal(
        top_search_patterns=top_search_patterns, 
        index_name=products_index
    )

@router.get("/elasticsearch/get_diversified_top_searches")
async def get_diversified_top_searches(
    top_searches_index: str = Query(..., description="The index name for top searches"),
    size: int = Query(5, ge=1),
    prefix_size: int = Query(3, alias="prefixSize", ge=0),
    top_term_per_prefix: int = Query(1, alias="topTermPerPrefix", ge=1),
    elasticsearch_controller: ElasticsearchController = Depends(ElasticsearchController)
):
    return await elasticsearch_controller.get_diversified_top_searches(
        index_name=top_searches_index,
        size=size,
        prefix_size=prefix_size,
        top_term_per_prefix=top_term_per_prefix
    )
