from fastapi import <PERSON>Router, Depends, Request
from controllers.securityController import SecurityController

router = APIRouter()

@router.get("/admin/security")
async def load_security(request: Request, security_controller: SecurityController = Depends(SecurityController)):
    return await security_controller.index(request)

@router.get("/admin/security/config")
async def get_security_config(request: Request, security_controller: SecurityController = Depends(SecurityController)):
    return await security_controller.get_security_config(request)

@router.get("/admin/security/login-rate-limit")
async def get_login_rate_limit(request: Request, security_controller: SecurityController = Depends(SecurityController)):
    return await security_controller.get_login_rate_limit(request)

@router.post("/admin/security/login-rate-limit")
async def update_login_rate_limit(request: Request, security_controller: SecurityController = Depends(SecurityController)):
    return await security_controller.update_login_rate_limit(request)

@router.get("/admin/security/failed-login-attempts")
async def get_failed_login_attempts_config(request: Request, security_controller: SecurityController = Depends(SecurityController)):
    return await security_controller.get_failed_login_attempts_config(request)

@router.post("/admin/security/failed-login-attempts")
async def update_failed_login_attempts_config(request: Request, security_controller: SecurityController = Depends(SecurityController)):
    return await security_controller.update_failed_login_attempts_config(request)

@router.post("/admin/security/failed-login-attempts/enabled")
async def update_failed_login_attempts_enabled(request: Request, security_controller: SecurityController = Depends(SecurityController)):
    return await security_controller.update_failed_login_attempts_enabled(request)

@router.get("/admin/security/cooldown-time")
async def get_cooldown_time_config(request: Request, security_controller: SecurityController = Depends(SecurityController)):
    return await security_controller.get_cooldown_time_config(request)

@router.post("/admin/security/cooldown-time")
async def update_cooldown_time_config(request: Request, security_controller: SecurityController = Depends(SecurityController)):
    return await security_controller.update_cooldown_time_config(request)

@router.get("/admin/security/max-attempts")
async def get_max_attempts_before_limit(request: Request, security_controller: SecurityController = Depends(SecurityController)):
    return await security_controller.get_max_attempts_before_limit(request)

@router.post("/admin/security/max-attempts")
async def update_max_attempts_before_limit(request: Request, security_controller: SecurityController = Depends(SecurityController)):
    return await security_controller.update_max_attempts_before_limit(request)

@router.get("/admin/security/rate-limit-duration")
async def get_rate_limit_duration_hours(request: Request, security_controller: SecurityController = Depends(SecurityController)):
    return await security_controller.get_rate_limit_duration_hours(request)

@router.post("/admin/security/rate-limit-duration")
async def update_rate_limit_duration_hours(request: Request, security_controller: SecurityController = Depends(SecurityController)):
    return await security_controller.update_rate_limit_duration_hours(request)

@router.post("/admin/security/update-entire-config")
async def update_entire_config(request: Request, security_controller: SecurityController = Depends(SecurityController)):
    return await security_controller.update_entire_config(request)