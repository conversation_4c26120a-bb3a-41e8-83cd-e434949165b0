#!/bin/bash
echo -e "\e[34mExtracting messages...\e[0m"
pybabel extract -F babel.cfg -o locales/messages.pot .
echo -e "\e[32mInitializing translations for German...\e[0m"
pybabel init -i locales/messages.pot -d locales -l de
echo -e "\e[32mInitializing translations for English...\e[0m"
pybabel init -i locales/messages.pot -d locales -l en
read -p "Make the necessary modifications then press 'y' to continue: " confirm
if [[ "$confirm" != "y" ]]; then
    echo -e "\e[31mContinuing without editing translations.\e[0m"
fi
echo -e "\e[34mCompiling translations for German...\e[0m"
pybabel compile -d locales -l de
echo -e "\e[34mCompiling translations for English...\e[0m"
pybabel compile -d locales -l en
