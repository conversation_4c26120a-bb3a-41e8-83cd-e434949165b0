import json
from fastapi import FastAP<PERSON>, Request
from fastapi.staticfiles import StaticFiles
from middlewares.DynamicCORSMiddleware import CustomCORSMiddleware
from middlewares.TranslationMiddleware import TranslationMiddleware
from routes.elasticsearch_proxy import router as elasticsearch_proxy_router
from routes.user.authRoute import router as user_router
from routes.user.accountRoutes import router as account_router
from routes.user.dashboardRoutes import router as dashboard_router
from routes.website.websiteRoutes import router as website_router
from routes.events.clientCustomEventsRoutes import router as client_custom_events_router
from routes.tasks.taskRoutes import router as task_router
from routes.security.securityRoutes import router as security_router
from routes.logs.logRoutes import router as log_router
from routes.shopify_search_engine import router as shopify_search_engine_router
from config import PORT, templates

import os
from contextlib import asynccontextmanager

from services.schedulerService import SchedulerService
from dependencies.DependencyContainer import dependency_container

from slowapi import Limiter
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

from utils.exceptionUtil import rate_limit_exceeded_handler

def create_file_if_not_exists(file_name: str):
    # Get the directory of the current script
    current_dir = os.path.dirname(os.path.realpath(__file__))
    file_path = os.path.join(current_dir, file_name)

    if not os.path.exists(file_path):
        try:
            with open(file_path, 'w') as f:
                json.dump([], f)  # Write an empty list as JSON
        except IOError as e:
            print(f"Error creating JSON file: {e}")

create_file_if_not_exists("allowed_origins.json")


def get_scheduler_service():
    return SchedulerService(
        scheduled_task_service=dependency_container.get_scheduled_task_service(),
        task_execution_log_service=dependency_container.get_task_execution_log_service(),
        integrator=dependency_container.get_integrator()
    )

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    scheduler_service = get_scheduler_service()
    scheduler_service.start_scheduler()

    yield
    # Shutdown
    scheduler_service.stop_scheduler()

limiter = Limiter(key_func=get_remote_address)

app = FastAPI(lifespan=lifespan)

app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, rate_limit_exceeded_handler)

app.add_middleware(CustomCORSMiddleware)

# Add TranslationMiddleware
app.add_middleware(TranslationMiddleware)

# Make templates available to all routes
app.state.templates = templates

@app.middleware("http")
async def add_templates_to_request(request: Request, call_next):
    request.state.templates = app.state.templates
    response = await call_next(request)
    return response

# Static files
app.mount("/public", StaticFiles(directory="public"), name="public")
# cdn static files
app.mount("/cdn", StaticFiles(directory="cdn"), name="cdn")

app.include_router(elasticsearch_proxy_router)
app.include_router(user_router)
app.include_router(account_router)
app.include_router(dashboard_router)
app.include_router(website_router)
app.include_router(client_custom_events_router)
app.include_router(task_router)
app.include_router(security_router)
app.include_router(log_router)
app.include_router(shopify_search_engine_router)

if __name__ == "__main__":
    import uvicorn
    from config import ENV
    reload = True if ENV == "dev" else False
    uvicorn.run("main:app", host="0.0.0.0", port=PORT, reload=reload)