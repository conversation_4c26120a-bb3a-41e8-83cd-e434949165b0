import json
import re
import requests
from enums.log_event_types import LogEventTypesEnum
from enums.product_indexation_status import ProductIndexationStatusEnum
import logging
from repositories.website_repository import WebsiteRepository
from controllers.websiteController import WebsiteController
from controllers.LogController import LogController
from controllers.ProductIndexationController import ProductIndexationController
from models.Connexion import SessionLocal
from schemas.ProductIndexationSchema import ProductIndexationCreateSchema
from schemas.LogSchema import LogCreateSchema
from schemas.WebsiteSchema import WebsiteSettingsSchema, WebsiteSchema
from models.models import WebsiteSettings
from config import ELASTICSEARCH_URL, ELASTICSEARCH_USERNAME, ELASTICSEARCH_PASSWORD
import urllib.parse
from elasticsearch.exceptions import AuthenticationException, NotFoundError, RequestError, ConflictError, ConnectionError, TransportError
from elasticsearch import Elasticsearch
import time
import csv
from fastapi.encoders import jsonable_encoder
from datetime import datetime
import shutil
import sys
import os
import threading
from fastapi import Depends
import schedule

# build the path of the root directory
base_path = '/'.join(__file__.split('/')[:-2])
sys.path.append(base_path)


# configure the logging
logging.basicConfig(level=logging.INFO,
                    format="%(asctime)s - %(levelname)s - %(message)s")


class Integrator:
    def __init__(self):
        self.csv_filename = "findologic_0_de.csv"
        self.delimiter = "\t"  # Default delimiter for ToolBrothers format
        self.es = Elasticsearch(
            hosts=[ELASTICSEARCH_URL],
            basic_auth=(ELASTICSEARCH_USERNAME, ELASTICSEARCH_PASSWORD),
            verify_certs=True,
        )
        self.csv_urls = {}
        self.files_folder = os.path.join(
            '/'.join(__file__.split('/')[:-1]), "csv_files")
        self.websites = []
        self.current_dir = os.path.dirname(os.path.realpath(__file__))

        self.website_repository = WebsiteRepository()

        # CSV format detection and mapping
        # NOTE: Shopify column names are taken directly from datafeed/feed.py fieldnames definition
        self.csv_format_mappings = {
            'toolbrothers': {
                'delimiter': '\t',
                'required_columns': ['id', 'ordernumber', 'name', 'description', 'price', 'quantity', 'visibilities'],
                'id_field': 'id',
                'visibility_field': 'visibilities'
            },
            'shopify': {
                'delimiter': ',',
                'required_columns': [
                    # Exact column names from datafeed/feed.py (variant-based structure)
                    'product_id', 'title', 'description', 'vendor', 'product_type', 'tags',
                    'url', 'primary_image', 'all_images', 'created_at', 'status',
                    'variant_id', 'sku', 'barcode', 'price', 'inventory', 'is_available', 'compare_at_price'
                ],
                'id_field': 'variant_id',  # Changed to variant_id since each row is a variant
                'visibility_field': 'is_available'
            }
        }

        # create the file folder if not exists
        self.create_csv_files_dir()

    def create_csv_files_dir(self):
        file_path = os.path.join(self.current_dir, 'csv_files')
        if not os.path.exists(file_path):
            os.makedirs(file_path)
        return file_path
    
    def delete_csv_file(self, website_key: str):
        try:
            logging.info(f"Cleaning old downloads")
            # Check and delete uppercase version
            upper_file_path = os.path.join(self.files_folder, f"{website_key.upper()}.csv")
            if os.path.exists(upper_file_path):
                os.remove(upper_file_path)
                logging.info(f"Deleted file: {upper_file_path}")
            else:
                logging.info(f"File not found: {upper_file_path}")

            # Check and delete lowercase version
            lower_file_path = os.path.join(self.files_folder, f"{website_key.lower()}.csv")
            if os.path.exists(lower_file_path):
                os.remove(lower_file_path)
                logging.info(f"Deleted file: {lower_file_path}")
            else:
                logging.info(f"File not found: {lower_file_path}")
        except Exception as e:
            logging.error(f"Error deleting file: {e}")

    def retrieve_website_settings(self, website_key):
        for s in self.websites:
            if s['website_key'].lower() == website_key.lower():
                return s.get('website_settings')
        return None

    # create logs
    def create_logs(self, event_type, event_description, website_id=None):
        try:
            db = SessionLocal()
            log_schema = LogCreateSchema(
                event_type=event_type, event_description=event_description)
            if website_id:
                log_schema.website_id = website_id
            log = LogController.create(db, log_schema)
            logging.info(f"Created log: {log.log_id}")
        except Exception as e:
            logging.error(f"An error occurred while creating log: {e}")
        finally:
            db.close()

    # product indexation log
    def product_indexation_log(self, schema: ProductIndexationCreateSchema):
        try:
            db = SessionLocal()
            indexation_log = ProductIndexationController.create(db, schema)
            logging.info(
                f"Created indexation log: {indexation_log.product_indexation_id}")
        except Exception as e:
            logging.error(
                f"An error occurred while creating indexation log: {e}")
        finally:
            db.close()

    # load websites from database
    def load_websites_from_database(self) -> bool:
        res = False
        try:
            websites = self.website_repository.get_all()
            websites = [jsonable_encoder(website) for website in websites if not website.is_paused]
            self.websites = websites
            for w in websites:
                self.csv_urls[w["website_key"].upper(
                )] = w['website_settings']['csv_url']
            logging.info("Loaded websites from database")
            # logging.info(self.csv_urls)
            res = True
        except Exception as e:
            logging.error(f"An error occurred while fetching websites: {e}")
        return res

    # download csv file and save it locally

    def download_csv(self, url, website_key) -> bool:
        res = False
        try:
            self.delete_csv_file(website_key)
            
            logging.info(f"Downloading CSV file for {website_key}")
            response = requests.get(url)
            file_path = os.path.join(self.files_folder, f"{website_key}.csv")
            if response.status_code == 200:
                logging.info(f"Downloaded CSV file for {website_key}")
                logging.info(f"Saving CSV file for {website_key}")
                with open(file_path, 'wb') as f:
                    f.write(response.content)
                logging.info(f"Saved CSV file for {website_key}")
                res = True
            else:
                logging.error(f"Failed to download CSV file for {website_key}")
        except Exception as e:
            logging.error(
                f"An error occurred while downloading CSV file for {website_key}: {e}")
        return res

    def detect_csv_format(self, filename):
        """Detect CSV format by examining headers and delimiter"""
        try:
            # Try to detect delimiter by reading first few lines
            with open(filename, mode='r', encoding='utf-8') as csvfile:
                sample = csvfile.read(1024)
                csvfile.seek(0)

                # Count occurrences of potential delimiters
                tab_count = sample.count('\t')
                comma_count = sample.count(',')

                # Read the header line to check column names
                header_line = csvfile.readline().strip()

                # Check for Shopify format indicators (using actual column names from feed.py)
                shopify_indicators = ['product_id', 'title', 'vendor', 'product_type', 'is_available', 'variant_id', 'sku', 'barcode']
                toolbrothers_indicators = ['id', 'ordernumber', 'name', 'visibilities']

                shopify_matches = sum(1 for indicator in shopify_indicators if indicator in header_line)
                toolbrothers_matches = sum(1 for indicator in toolbrothers_indicators if indicator in header_line)

                # Determine format based on column names and delimiter frequency
                if shopify_matches >= 3 and comma_count > tab_count:
                    return 'shopify'
                elif toolbrothers_matches >= 3 and tab_count > comma_count:
                    return 'toolbrothers'
                elif comma_count > tab_count:
                    return 'shopify'  # Default to shopify for comma-separated
                else:
                    return 'toolbrothers'  # Default to toolbrothers for tab-separated

        except Exception as e:
            logging.error(f"Error detecting CSV format for {filename}: {e}")
            return 'toolbrothers'  # Default fallback

    def read_csv(self, filename="findologic_0_de.csv", csv_format=None):
        """Read CSV file with automatic format detection"""
        if csv_format is None:
            csv_format = self.detect_csv_format(filename)

        format_config = self.csv_format_mappings.get(csv_format, self.csv_format_mappings['toolbrothers'])
        delimiter = format_config['delimiter']

        logging.info(f"Reading CSV file {filename} with format: {csv_format}, delimiter: '{delimiter}'")

        with open(filename, mode='r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile, delimiter=delimiter)
            for row in reader:
                yield row, csv_format

    def sanitize_data(self, data: dict, csv_format='toolbrothers'):
        """Sanitize data based on CSV format"""
        sanitized_data = {key: value.strip() if isinstance(value, str) else value
                         for key, value in data.items() if (
                             str(value) != "nan" and str(value) != "" and value is not None)}

        # Handle fields based on format
        if csv_format == 'shopify':
            # Handle price fields
            price_fields = ['price', 'compare_at_price']
            for field in price_fields:
                if field in sanitized_data:
                    try:
                        sanitized_data[field] = float(sanitized_data[field])
                    except (ValueError, TypeError):
                        sanitized_data[field] = 0.0

            # Handle inventory fields
            inventory_fields = ['inventory']
            for field in inventory_fields:
                if field in sanitized_data:
                    try:
                        sanitized_data[field] = int(sanitized_data[field])
                    except (ValueError, TypeError):
                        sanitized_data[field] = 0

            # Handle boolean fields
            if 'is_available' in sanitized_data:
                sanitized_data['is_available'] = str(sanitized_data['is_available']).lower() in ['true', '1', 'yes']

        else:
            # ToolBrothers format (original logic)
            if 'price' in sanitized_data:
                try:
                    sanitized_data['price'] = float(sanitized_data['price'])
                except (ValueError, TypeError):
                    sanitized_data['price'] = 0.0

        return sanitized_data

    def create_index(self, index="products"):
        # Define the settings and mappings for the index
        settings = {
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "analysis": {
                    "analyzer": {
                        "product_analyzer": {
                            "type": "custom",
                            "tokenizer": "standard",
                            "filter": ["lowercase", "asciifolding"]
                        }
                    }
                }
            },
            "mappings": {
                "properties": {
                    # Common fields
                    "price": {
                        "type": "double"
                    },
                    "_csv_format": {
                        "type": "keyword"
                    },
                    "_indexed_at": {
                        "type": "date"
                    },

                    # ToolBrothers format fields
                    "id": {
                        "type": "keyword"
                    },
                    "ordernumber": {
                        "type": "keyword"
                    },
                    "name": {
                        "type": "text",
                        "analyzer": "product_analyzer",
                        "fields": {
                            "keyword": {
                                "type": "keyword"
                            }
                        }
                    },
                    "description": {
                        "type": "text",
                        "analyzer": "product_analyzer"
                    },
                    "quantity": {
                        "type": "integer"
                    },
                    "visibilities": {
                        "type": "keyword"
                    },

                    # Shopify format fields - Product level
                    "product_id": {
                        "type": "keyword"
                    },
                    "title": {
                        "type": "text",
                        "analyzer": "product_analyzer",
                        "fields": {
                            "keyword": {
                                "type": "keyword"
                            }
                        }
                    },
                    "vendor": {
                        "type": "keyword"
                    },
                    "product_type": {
                        "type": "keyword"
                    },
                    "tags": {
                        "type": "text",
                        "analyzer": "product_analyzer"
                    },
                    "is_available": {
                        "type": "boolean"
                    },
                    "primary_image": {
                        "type": "keyword"
                    },
                    "all_images": {
                        "type": "text"
                    },
                    "url": {
                        "type": "keyword"
                    },
                    "status": {
                        "type": "keyword"
                    },
                    "created_at": {
                        "type": "date"
                    },

                    # Shopify format fields - Variant level
                    "variant_id": {
                        "type": "keyword"
                    },
                    "sku": {
                        "type": "text",
                        "analyzer": "product_analyzer",
                        "fields": {
                            "keyword": {
                                "type": "keyword"
                            }
                        }
                    },
                    "barcode": {
                        "type": "text",
                        "analyzer": "product_analyzer",
                        "fields": {
                            "keyword": {
                                "type": "keyword"
                            }
                        }
                    },
                    "inventory": {
                        "type": "integer"
                    },
                    "compare_at_price": {
                        "type": "double"
                    }
                }
            }
        }

        try:
            index_exists = self.es.indices.exists(index=index)
            if index_exists:
                logging.info(f"[+] Index {index} already exists")
                return
            self.es.indices.create(index=index, body=settings)
            logging.info(f"[+] Index {index} created")
        except AuthenticationException as e:
            print('failed to authenticate to Elasticsearch', e)
            self.create_logs(event_type=LogEventTypesEnum.ELASTOCSEARCH_AUTHENTICATION_ERROR.value,
                             event_description=f"Failed to authenticate to Elasticsearch: {e}")
            return
        except Exception as e:
            print('an error occured:', e)
            self.create_logs(event_type=LogEventTypesEnum.ELASTICSEARCH_GENERIC_ERROR.value,
                             event_description=f"Failed to create index {index}: {e}")
            return

    def create_top_searches_index(self, index="top_searches"):
        request = {
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            },
            "mappings": {
                "properties": {
                    "search_term": {  # Define the search_term field as a keyword
                        "type": "keyword"
                    },
                    "count": {
                        "type": "integer"
                    },
                    "timestamp": {
                        "type": "date"
                    }
                }
            }
        }

        try:
            index_exists = self.es.indices.exists(index=index)
            if index_exists:
                logging.info(f"[+] Index {index} already exists")
                return
            self.es.indices.create(index=index, body=request)
            logging.info(f"[+] Index {index} created")
        except AuthenticationException as e:
            print('failed to authenticate to Elasticsearch', e)
            self.create_logs(event_type=LogEventTypesEnum.ELASTOCSEARCH_AUTHENTICATION_ERROR.value,
                             event_description=f"Failed to authenticate to Elasticsearch: {e}")
            return
        except Exception as e:
            print('an error occured:', e)
            self.create_logs(event_type=LogEventTypesEnum.ELASTICSEARCH_GENERIC_ERROR.value,
                             event_description=f"Failed to create index {index}: {e}")

    def parse_attributes(self, attributes):
        if not attributes:
            return {}
        if not isinstance(attributes, str):
            logging.warning(f"Expected attributes to be a string, got: {type(attributes)}. Returning original value.")
            return {'attributes': attributes}

        if '&' not in attributes and '=' not in attributes:
            return {'attributes': attributes}

        attributes = attributes.replace('+', ' ')

        data_object = {}
        # Split by '&' only if NOT followed by a space
        pairs = re.split(r'&(?=[^ ])', attributes) # Lookahead assertion to NOT match '& '

        for pair in pairs:
            if not pair:
                continue
            if '=' not in pair:
                # logging.warning(f"Invalid attribute pair format, missing '=' in: '{pair}'. Skipping pair.")
                continue
            try:
                key, value = pair.split('=', 1)
                decoded_key = urllib.parse.unquote(key)
                decoded_value = urllib.parse.unquote(value)
                if decoded_value:
                    data_object[decoded_key] = decoded_value
            except ValueError as e:
                logging.error(f"Error parsing attribute pair: '{pair}'. Error: {e}. Skipping pair.")
                continue
        return data_object

    # Define a function to send bulk data
    def send_bulk(self, bulk_operations):
        for attempt in range(5):
            try:
                res = self.es.bulk(body=bulk_operations)
                # print("[+] Bulk data sent")
                return True
            except (ConnectionError, NotFoundError, ConflictError,
                    RequestError, TransportError) as e:
                print('General Elasticsearch exception')
                self.create_logs(event_type=LogEventTypesEnum.ELASTICSEARCH_GENERIC_ERROR.value,
                                 event_description=f"Failed to send bulk data: {e}")
                return False
            except Exception as e:
                print(f"Attempt {attempt+1} failed with error: {e}")
                time.sleep(10)  # Wait for 10 seconds before retrying
                if attempt == 4:
                    print("[+] Failed to send bulk data")
                    self.create_logs(event_type=LogEventTypesEnum.ELASTICSEARCH_GENERIC_ERROR.value,
                                     event_description=f"Failed to send bulk data: {e}")
                    return False
            time.sleep(10)

    def send_create_or_update_data(self, e_index, website_key):
        """Send CSV data to Elasticsearch with automatic format detection"""
        bulk_operations = []
        total_products = 0

        print("[+] Preparing bulk data")
        file_path = os.path.join(
            self.current_dir, 'csv_files', f"{website_key}.csv")

        # Detect CSV format first
        csv_format = self.detect_csv_format(file_path)
        format_config = self.csv_format_mappings[csv_format]

        logging.info(f"Processing CSV file with format: {csv_format}")

        for index, (row, _) in enumerate(self.read_csv(file_path)):
            # Check visibility/availability based on format
            should_skip = False

            if csv_format == 'shopify':
                # For Shopify format, check is_available field (handle as boolean)
                if 'is_available' in row:
                    # Convert to boolean consistently
                    is_available = str(row['is_available']).lower() in ['true', '1', 'yes']
                    if not is_available:
                        should_skip = True
            else:
                # For ToolBrothers format, check visibilities field (string-based)
                if 'visibilities' in row and row['visibilities'].lower() == 'false':
                    should_skip = True

            if should_skip:
                continue

            # Get the appropriate ID field
            id_field = format_config['id_field']
            if id_field not in row:
                logging.warning(f"Missing ID field '{id_field}' in row {index}. Skipping.")
                continue

            action = {
                "update": {
                    "_index": e_index,
                    "_id": row[id_field],
                }
            }

            doc = {
                "doc": self.sanitize_data(row, csv_format),
                "doc_as_upsert": True
            }

            # Handle attributes parsing (mainly for ToolBrothers format)
            if 'attributes' in doc['doc']:
                attributes = self.parse_attributes(doc['doc']['attributes'])
                doc['doc'].update(attributes)

            # Add format metadata
            doc['doc']['_csv_format'] = csv_format
            doc['doc']['_indexed_at'] = datetime.now().isoformat()

            bulk_operations.append(action)
            bulk_operations.append(doc)

            if (index + 1) % 200 == 0:
                sent = self.send_bulk(bulk_operations)
                if not sent:
                    return False, total_products
                bulk_operations = []
                total_products += 200

        if bulk_operations:
            sent = self.send_bulk(bulk_operations)
            if not sent:
                return False, total_products
            total_products += len(bulk_operations) // 2  # Divide by 2 because each product has 2 operations

        bulk_operations = []
        return True, total_products

    def get_indexed_data(self):
        es = Elasticsearch(hosts=["https://elastic.toolbrothers.space:9200"])
        query = {
            "query": {
                "match_all": {}
            }
        }
        res = es.search(index="products", body=query)

    def get_document(self, name):
        uri = "https://elastic.toolbrothers.space:9200/products/_search"
        query = {
            "query": {
                "match": {
                    "name": name
                }
            }
        }
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Access-Control-Allow-Origin": "*",
        }
        res = requests.post(uri, data=json.dumps(query), headers=headers)
        if res.status_code == 200:
            res = res.json()['hits']['hits']
        print(res)

    def delete_index(self, indices):
        for index in indices:
            print(f"[+] Deleting index {index}")
            self.es.indices.delete(index=index.lower(), ignore=[400, 404])
            print(f"[+] Index {index} deleted")
    
    def refresh_index(self, index):
        if self.index_exists(index):
            self.delete_index([index])  # Delete the old index
        self.create_index(index)  # Recreate it

    # get in index from elasticsearch
    def get_indexed_data(self, index):
        res = self.es.search(index=index, body={"query": {"match_all": {}}})
        print(res)

    def index_exists(self, index):
        return self.es.indices.exists(index=index)

    def empty_folder(self, folder_path):
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)
            try:
                if os.path.isfile(file_path) or os.path.islink(file_path):
                    os.unlink(file_path)
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
            except Exception as e:
                print('Failed to delete %s. Reason: %s' % (file_path, e))

    def process_shopify_csv_file(self, csv_file_path, products_index, website_key):
        """Process a Shopify CSV file directly (for local files)"""
        if not os.path.exists(csv_file_path):
            logging.error(f"CSV file not found: {csv_file_path}")
            return 0

        # Copy the file to our processing directory - same pattern as ToolBrothers
        dest_filename = f"{website_key}.csv"
        dest_path = os.path.join(self.files_folder, dest_filename)

        try:
            shutil.copy2(csv_file_path, dest_path)
            logging.info(f"Copied Shopify CSV file to: {dest_path}")
        except Exception as e:
            logging.error(f"Failed to copy CSV file: {e}")
            return 0

        start_date = datetime.now()

        # Create/refresh the index
        self.refresh_index(products_index)

        # Process the CSV file
        try:
            res, total_products = self.send_create_or_update_data(products_index, website_key)

            if res:
                end_date = datetime.now()
                duration = int((end_date - start_date).total_seconds())
                logging.info(f"Successfully indexed {total_products} Shopify products in {duration} seconds")

                # Clean up the copied file
                try:
                    os.remove(dest_path)
                    logging.info(f"Cleaned up temporary file: {dest_path}")
                except Exception as e:
                    logging.warning(f"Failed to clean up temporary file: {e}")

                return total_products
            else:
                logging.error("Failed to index Shopify products")
                return 0

        except Exception as e:
            logging.error(f"Error processing Shopify CSV file: {e}")
            return 0

    def process_csv_file(self, website_key, url, products_index):
        d_start = datetime.now()
        res = self.download_csv(url, website_key)
        if not res:
            self.create_logs(event_type=LogEventTypesEnum.CSV_FILE_DOWNLOAD_ERROR.value,
                             event_description=f"Failed to download CSV file for {website_key}. Skipping.")
            return 0  # Return 0 products indexed if download fails
        d_end = datetime.now()
        download_duration = int((d_end - d_start).total_seconds())

        top_searches_index = f"{website_key}_top_searches".lower()
        self.create_top_searches_index(top_searches_index)

        start_date = datetime.now()
        # refresh the index
        self.refresh_index(products_index)
        # send data to elasticsearch
        res, total_products = self.send_create_or_update_data(
            products_index, website_key)
        end_date = datetime.now()

        if res:
            # compute duration in seconds
            duration = int((end_date - start_date).total_seconds())

            website_setting = self.retrieve_website_settings(website_key)
            indexation_log_schema = ProductIndexationCreateSchema(
                website_id=website_setting['website_id'],
                start_date=start_date,
                duration=duration,
                download_duration=download_duration,
                total_products=total_products,
                status=ProductIndexationStatusEnum.SUCCESS.value,
                message=f"Indexation for {website_key} completed successfully"
            )
            self.product_indexation_log(indexation_log_schema)

        # delete csv file
        self.delete_csv_file(website_key)
        return total_products # Return the total number of products indexed

    def automatic_indexation(self):
        res = self.load_websites_from_database()
        if not res:
            logging.error("Failed to load websites from database. Exiting.")
            self.create_logs(event_type=LogEventTypesEnum.DATABASE_READ_ERROR.value,
                             event_description="Failed to load websites from database. Exiting.")
            return 0  # Return 0 if loading fails

        total_products = 0
        for website_key, url, in self.csv_urls.items():
            products_index = f"{website_key}_products".lower()
            # create index if it doesn't exist
            # self.create_index(products_index)
            total_products += self.process_csv_file(website_key, url, products_index)

        # empty folder
        file_path = os.path.join(self.current_dir, 'csv_files')
        self.empty_folder(file_path)

        return total_products

    def manual_indexation(self, website_key):
        # get website setting
        failed = False
        try:
            website = self.website_repository.get_by_key(website_key)
            website_setting = website.website_settings
            self.websites = [jsonable_encoder(website)]
        except Exception as e:
            failed = True
            print(e)
            self.create_logs(event_type=LogEventTypesEnum.DATABASE_READ_ERROR.value,
                             event_description=f"Failed to retrieve website settings for {website_key}. Skipping.")

        if failed:
            return 0  # Return 0 if there's a failure

        url = website_setting.csv_url
        products_index = f"{website_key}_products".lower()
        # create index if it doesn't exist
        # self.delete_index([products_index])
        # self.create_index(products_index)
        try:
            return self.process_csv_file(website_key, url, products_index)
        except Exception as e:
            import traceback
            traceback.print_exc()
            raise e

    def get_search_terms(self, website_key, start, page_size, start_date=None, end_date=None, sort_by=None, search=None):
        # Get top searches from Elasticsearch
        index = f"{website_key}_top_searches".lower()

        # Use start directly without calculating from page_number
        from_index = start

        # Validate and reorder dates if necessary
        if start_date and end_date:
            if start_date > end_date:
                start_date, end_date = end_date, start_date

        # Initialize the query filters
        query_filters = [
            {"match_all": {}},
            {"range": {
                "timestamp": {
                    "gte": f"{start_date}T00:00:00.000Z" if start_date else '*',
                    "lte": f"{end_date}T23:59:59.999Z" if end_date else '*',
                }
            }}
        ]

        # Add search term filter if provided
        if search:
            query_filters.append({
                "wildcard": {
                    "search_term": f"*{search}*"
                }
            })

        # Initialize sort clause
        sort_clause = []
        if sort_by:
            for column, direction in sort_by:
                sort_clause.append({column: {"order": direction}})

        # Construct the Elasticsearch query body
        body = {
            "query": {
                "bool": {
                    "must": query_filters
                }
            },
            "size": page_size,
            "from": from_index,
            "sort": sort_clause
        }

        try:
            res = self.es.search(index=index, body=body)
            total_hits = res['hits']['total']['value']
            search_terms = [{"search_term": hit['_source']['search_term'],
                            "count": hit['_source']['count']} for hit in res['hits']['hits']]
            return {"total_hits": total_hits, "search_terms": search_terms}
        except Exception as e:
            print(e)
            self.create_logs(event_type=LogEventTypesEnum.ELASTICSEARCH_GENERIC_ERROR.value,
                            event_description=f"Error retrieving top searches from Elasticsearch for {website_key}: {e}")
            return None

    def setup_scheduler(self):
        # schedule the automatic indexation to run twice a day, at 6:00 AM and 6:00 PM
        schedule.every().day.at("12:00").do(self.automatic_indexation)
        schedule.every().day.at("18:00").do(self.automatic_indexation)

    def run_scheduler(self):
        while True:
            schedule.run_pending()
            time.sleep(1)

    def start_scheduler(self):
        scheduler_thread = threading.Thread(target=self.run_scheduler)
        scheduler_thread.start()
        scheduler_thread.join()

    def index_shopify_products(self, csv_file_path, index_name=None, website_key="shopify"):
        """
        Convenience method to index Shopify products from a CSV file

        Args:
            csv_file_path (str): Path to the Shopify CSV file
            index_name (str, optional): Elasticsearch index name. Follows ToolBrothers pattern: '{website_key}_products'
            website_key (str, optional): Website key for indexing. Defaults to 'shopify'

        Returns:
            int: Number of products indexed
        """
        if index_name is None:
            # Follow ToolBrothers pattern: use website_key for index naming
            index_name = f"{website_key}_products".lower()

        logging.info(f"Starting Shopify product indexation from: {csv_file_path}")

        try:
            total_products = self.process_shopify_csv_file(
                csv_file_path=csv_file_path,
                products_index=index_name,
                website_key=website_key  # Pass the parameter, don't hardcode
            )

            if total_products > 0:
                logging.info(f"✅ Successfully indexed {total_products} Shopify products")
                self.create_logs(
                    event_type=LogEventTypesEnum.PRODUCT_INDEXATION_SUCCESS.value,
                    event_description=f"Successfully indexed {total_products} Shopify products from {csv_file_path}"
                )
            else:
                logging.error("❌ No products were indexed")
                self.create_logs(
                    event_type=LogEventTypesEnum.PRODUCT_INDEXATION_ERROR.value,
                    event_description=f"Failed to index Shopify products from {csv_file_path}"
                )

            return total_products

        except Exception as e:
            logging.error(f"Error during Shopify product indexation: {e}")
            self.create_logs(
                event_type=LogEventTypesEnum.PRODUCT_INDEXATION_ERROR.value,
                event_description=f"Error indexing Shopify products: {e}"
            )
            return 0

    def test_shopify_csv_processing(self, csv_file_path):
        """
        Test method to verify Shopify CSV processing with variant-based structure
        """
        if not os.path.exists(csv_file_path):
            logging.error(f"Test CSV file not found: {csv_file_path}")
            return False

        try:
            # Test format detection
            detected_format = self.detect_csv_format(csv_file_path)
            logging.info(f"Detected CSV format: {detected_format}")

            # Test reading a few rows
            row_count = 0
            for row, format_type in self.read_csv(csv_file_path):
                if row_count >= 3:  # Just test first 3 rows
                    break

                logging.info(f"Row {row_count + 1}: {row}")

                # Test sanitization
                sanitized = self.sanitize_data(row, format_type)
                logging.info(f"Sanitized: {sanitized}")

                row_count += 1

            logging.info(f"✅ Successfully processed {row_count} test rows")
            return True

        except Exception as e:
            logging.error(f"❌ Error testing CSV processing: {e}")
            return False


if __name__ == "__main__":
    integrator = Integrator()

    # Example usage for different scenarios:

    # 1. Original ToolBrothers automatic indexation
    # integrator.automatic_indexation()

    # 2. Original ToolBrothers manual indexation
    # integrator.manual_indexation("189FA637867BEE6BC27637C23A82ED7B")

    # 3. NEW: Index Shopify products from CSV file
    # shopify_csv_path = "/path/to/your/shopify_products_feed.csv"
    # integrator.index_shopify_products(shopify_csv_path, "shopify_products")

    # 4. Test Shopify CSV processing (for debugging)
    # integrator.test_shopify_csv_processing("/path/to/your/shopify_products_feed.csv")

    # 5. Get search terms (works for both formats)
    # print(integrator.get_search_terms("76D8F097AC378CA586DBDCCCA7897D5B", 0, 10, "2024-03-08", "2024-03-08"))

    # Default: set the schedule and start the scheduler for automatic indexation
    integrator.setup_scheduler()
    integrator.start_scheduler()
