from datetime import datetime, timezone, time
from zoneinfo import ZoneInfo

# Utility function to ensure datetime is in the correct timezone
def ensure_timezone(dt: datetime, target_tz=timezone.utc) -> datetime:
    """Ensure the datetime is in the target timezone."""
    if dt.tzinfo is None:
        # Assume naive datetimes are in the target timezone
        return dt.replace(tzinfo=target_tz)
    else:
        # Convert aware datetimes to the target timezone
        return dt.astimezone(target_tz)

# Utility function to extract time of day from datetime
def get_time_of_day(dt: datetime) -> time:
    """Extracts the time of day from a datetime object."""
    return dt.time()



def get_server_timezone():
    try:
        with open('/etc/timezone', 'r') as f:
            return ZoneInfo(f.read().strip())
    except FileNotFoundError:
        return ZoneInfo('UTC')  # Default to UTC if file not found

