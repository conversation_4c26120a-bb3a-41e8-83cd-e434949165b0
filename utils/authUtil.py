from functools import wraps

from fastapi import <PERSON>TT<PERSON><PERSON><PERSON><PERSON>, Request, status

from enums.role_enum import Role<PERSON><PERSON>
from utils.exceptionUtil import handle_exception


def check_role(role=None):
    def decorator(func):
        @wraps(func)
        async def wrapper(self, request: Request, *args, **kwargs):
            try:
                user = self.auth_service.is_authenticated(request)
                if not user:
                    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Not authenticated")
                
                if role and user.role.name != role:
                    raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Role mismatch")
            except Exception as exc:
                return handle_exception(request, exc)  
            return await func(self, request, *args, **kwargs)
        return wrapper
    return decorator


def admin_required(func):
    @wraps(func)
    async def wrapper(self, request: Request, *args, **kwargs):
        try:
            user = self.auth_service.is_authenticated(request)
            if user.role.name != RoleEnum.ADMIN.value:
                raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Unauthorized")
        except Exception as exc:
            return handle_exception(request, exc)  
        return await func(self, request, *args, **kwargs)
    return wrapper