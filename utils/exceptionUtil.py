from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request, status
from fastapi.responses import JSONResponse
from slowapi.errors import RateLimitExceeded

from config import templates


def _handle_http_exception(request: Request, exc: HTTPException):
    if request.headers.get("accept") == "application/json":
        return JSONResponse(
            status_code=exc.status_code,
            content={"detail": exc.detail}
        )

    if exc.status_code == status.HTTP_401_UNAUTHORIZED:
        return templates.TemplateResponse("account/login.html", {"request": request})
    elif exc.status_code == status.HTTP_403_FORBIDDEN:
        return templates.TemplateResponse("errors/error403.html", {"request": request})
    return templates.TemplateResponse("errors/error500.html", {"request": request})

def _handle_general_exception(request: Request, exc: Exception):
    if request.headers.get("accept") == "application/json":
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"detail": "An unexpected error occurred"}
        )
    return templates.TemplateResponse("errors/error500.html", {"request": request})

def handle_exception(request: Request, exc: Exception):
    if isinstance(exc, HTTPException):
        return _handle_http_exception(request, exc)
    return _handle_general_exception(request, exc)


def rate_limit_exceeded_handler(request: Request, exc: RateLimitExceeded):
    return templates.TemplateResponse("errors/rate_limit_exceeded.html", {"request": request}, status_code=429)