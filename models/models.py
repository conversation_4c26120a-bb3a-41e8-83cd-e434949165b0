import enum
from typing import List, <PERSON><PERSON>, Tuple
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>te<PERSON>, String, Foreign<PERSON>ey, DateTime, Text, func
from sqlalchemy.orm import relationship, Session
from sqlalchemy.sql import expression
from enums.task_schedule import IndexingType, ScheduleFrequency
from models.Connexion import Base
from passlib.context import Crypt<PERSON>ontext
from enums.role_enum import RoleEnum
from enums.product_indexation_status import ProductIndexationStatusEnum

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class User(Base):
    __tablename__ = 'users'

    user_id = Column(Integer, primary_key=True, index=True)
    username = Column(String(255), unique=True, index=True)
    password = Column(String(255))
    email = Column(String(255), unique=True, index=True)
    is_active = Column(Boolean, nullable=False,
                       server_default=expression.true())
    is_admin = Column(Boolean, nullable=False)
    role_id = Column(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("roles.role_id", ondelete="RESTRICT"))
    # metadata fields
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(),
                        onupdate=func.now())

    websites = relationship("Website", back_populates="owner",
                            cascade="all, delete-orphan", lazy="joined")
    role = relationship("Role", back_populates="users", lazy="joined")

    # get all
    @classmethod
    def get_all(cls, db: Session, page: int = None, per_page: int = None) -> List['User']:
        query = db.query(cls)
        if page is not None and per_page is not None:
            items_offset = (page - 1) * per_page
            query = query.offset(items_offset).limit(per_page)
        return query.all()

    # get all by role name
    @classmethod
    def get_all_by_role_name(cls, db: Session, role_name: str, page: int = None, per_page: int = None) -> List['User']:
        query = db.query(cls).join(Role).filter(Role.name == role_name)
        if page is not None and per_page is not None:
            items_offset = (page - 1) * per_page
            query = query.offset(items_offset).limit(per_page)
        return query.all()

    # get by id
    @classmethod
    def get_by_id(cls, db: Session, user_id: int) -> Optional['User']:
        return db.query(cls).filter(cls.user_id == user_id).first()

    # get by username
    @classmethod
    def get_by_username(cls, db: Session, username: str) -> Optional['User']:
        return db.query(cls).filter(cls.username == username).first()

    # get by email
    @classmethod
    def get_by_email(cls, db: Session, email: str) -> Optional['User']:
        return db.query(cls).filter(cls.email == email).first()

    # hash password
    @classmethod
    def hash(cls, password):
        return pwd_context.hash(password)

    # verify password
    @classmethod
    def verify_password(cls, plain_password, hashed_password):
        return pwd_context.verify(plain_password, hashed_password)

    # create
    @classmethod
    def create(cls, db: Session, user: 'User') -> 'User':
        try:
            # hash the password
            user.password = cls.hash(user.password)
            db.add(user)
            db.commit()
            db.refresh(user)
        except Exception as e:
            db.rollback()
            raise e
        else:
            return user

    # update
    @classmethod
    def update(cls, db: Session, user: 'User') -> 'User':
        try:
            db.add(user)
            db.commit()
            db.refresh(user)
        except Exception as e:
            db.rollback()
            raise e
        else:
            return user

    # delete
    @classmethod
    def delete(cls, db: Session, user_id: int) -> Boolean:
        user = db.query(cls).filter_by(user_id=user_id).first()
        user_exists = user is not None
        if user_exists:
            try:
                # first delete everything related to the user
                # delete all related Websites and their related WebsiteSettings and ProductsIndexation
                for website in user.websites:
                    db.query(WebsiteSettings).filter_by(
                        website_id=website.website_id).delete()
                    db.query(ProductsIndexation).filter_by(
                        website_id=website.website_id).delete()
                    db.delete(website)
                db.delete(user)
                db.commit()
                return True
            except Exception as e:
                db.rollback()
                raise e
        return False


class Website(Base):
    __tablename__ = 'websites'

    website_id = Column(Integer, primary_key=True, index=True)
    # the id to use to query elasticsearch
    website_key = Column(String(255), unique=True, index=True)
    website_url = Column(String(255), unique=True, index=True)
    user_id = Column(Integer, ForeignKey("users.user_id"))
    is_active = Column(Boolean, server_default='0', nullable=False)
    is_paused = Column(Boolean, server_default='0', nullable=False)
    # metadata fields
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(),
                        onupdate=func.now())

    owner = relationship("User", back_populates="websites")
    # logs = relationship("Log", back_populates="website", cascade="all, delete-orphan")
    website_settings = relationship("WebsiteSettings", back_populates="website",
                                    cascade="all, delete-orphan", lazy="joined", uselist=False)

    # get all
    @classmethod
    def get_all(cls, db: Session, page: int = None, per_page: int = None) -> List['Website']:
        query = db.query(cls)
        if page is not None and per_page is not None:
            items_offset = (page - 1) * per_page
            query = query.offset(items_offset).limit(per_page)
        return query.all()

    # get by id
    @classmethod
    def get_by_id(cls, db: Session, website_id: int) -> Optional['Website']:
        return db.query(cls).filter(cls.website_id == website_id).first()

    # get by key
    @classmethod
    def get_by_key(cls, db: Session, website_key: str) -> Optional['Website']:
        return db.query(cls).filter(cls.website_key == website_key).first()

    # get by user id
    @classmethod
    def get_by_user_id(cls, db: Session, user_id: int) -> List['Website']:
        return db.query(cls).filter(cls.user_id == user_id).all()

    # get by website url
    @classmethod
    def get_by_website_url(cls, db: Session, website_url: str) -> Optional['Website']:
        return db.query(cls).filter(cls.website_url == website_url).first()

    # create
    @classmethod
    def create(cls, db: Session, website: 'Website') -> 'Website':
        try:
            # If this is the first website for the user, set it as active
            existing_websites = db.query(cls).filter(cls.user_id == website.user_id).count()
            if existing_websites == 0:
                website.is_active = True
            
            db.add(website)
            db.commit()
            db.refresh(website)
        except Exception as e:
            db.rollback()
            raise e
        else:
            return website

    # update
    @classmethod
    def update(cls, db: Session, website: 'Website') -> 'Website':
        try:
            db.add(website)
            db.commit()
            db.refresh(website)
        except Exception as e:
            db.rollback()
            raise e
        else:
            return website

    # delete
    @classmethod
    def delete(cls, db: Session, website_id: int) -> bool:
        website = db.query(cls).filter_by(website_id=website_id).first()
        website_exists = website is not None
        if website_exists:
            try:
                # first delete everything related to the website
                # delete website settings
                db.query(WebsiteSettings).filter_by(
                    website_id=website_id).delete()
                # delete all related ProductsIndexation
                db.query(ProductsIndexation).filter_by(
                    website_id=website_id).delete()
                db.query(cls).filter_by(website_id=website_id).delete()
                db.commit()
                return True
            except:
                db.rollback()
                return False
        return False

    @classmethod
    def set_active(cls, db: Session, website_id: int, user_id: int) -> Optional['Website']:
        try:
            # Deactivate all websites for the user
            db.query(cls).filter(cls.user_id == user_id).update(
                {"is_active": False})

            # Activate the selected website
            website = db.query(cls).filter(
                cls.website_id == website_id, cls.user_id == user_id).first()
            if website:
                website.is_active = True
                db.commit()
                return website
            return None
        except Exception as e:
            db.rollback()
            raise e

    @classmethod
    def get_active(cls, db: Session, user_id: int) -> Optional['Website']:
        return db.query(cls).filter(cls.user_id == user_id, cls.is_active == True).first()


class WebsiteSettings(Base):
    __tablename__ = 'website_settings'

    website_settings_id = Column(Integer, primary_key=True, index=True)
    website_id = Column(Integer, ForeignKey("websites.website_id"))
    results_container_selector = Column(String(255))
    search_input_selector = Column(String(255))
    csv_url = Column(String(255))
    # metadata fields
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(),
                        onupdate=func.now())

    website = relationship("Website", back_populates="website_settings")

    # get by website settings id
    @classmethod
    def get_by_website_settings_id(cls, db: Session, website_settings_id: int) -> Optional['WebsiteSettings']:
        return db.query(cls).filter(cls.website_settings_id == website_settings_id).first()

    # get by website id
    @classmethod
    def get_by_website_id(cls, db: Session, website_id: int) -> Optional['WebsiteSettings']:
        return db.query(cls).filter(cls.website_id == website_id).first()

    # get all
    @classmethod
    def get_all(cls, db: Session, page: int = None, per_page: int = None) -> List['WebsiteSettings']:
        query = db.query(cls)
        if page is not None and per_page is not None:
            items_offset = (page - 1) * per_page
            query = query.offset(items_offset).limit(per_page)
        return query.all()

    # create
    @classmethod
    def create(cls, db: Session, website_settings: 'WebsiteSettings') -> 'WebsiteSettings':
        try:
            db.add(website_settings)
            db.commit()
            db.refresh(website_settings)
        except Exception as e:
            db.rollback()
            raise e
        else:
            return website_settings

    # update
    @classmethod
    def update(cls, db: Session, website_settings: 'WebsiteSettings') -> 'WebsiteSettings':
        try:
            db.add(website_settings)
            db.commit()
            db.refresh(website_settings)
        except Exception as e:
            db.rollback()
            raise e
        else:
            return website_settings

    # delete
    @classmethod
    def delete(cls, db: Session, website_settings_id: int) -> bool:
        website_settings = db.query(cls).filter_by(
            website_settings_id=website_settings_id).first()
        website_settings_exists = website_settings is not None
        if website_settings_exists:
            try:
                db.query(cls).filter_by(
                    website_settings_id=website_settings_id).delete()
                db.commit()
                return True
            except Exception as e:
                db.rollback()
                raise e
        return False


class Role(Base):
    __tablename__ = 'roles'
    role_id = Column(Integer, primary_key=True)
    name = Column(String(255), unique=True)  # e.g., 'admin', 'client'

    users = relationship('User', back_populates='role')
    permissions = relationship(
        'Permission', secondary='role_permissions', back_populates='roles')

    # get all
    @classmethod
    def get_all(cls, db: Session, page: int = None, per_page: int = None) -> List['Role']:
        query = db.query(cls)
        if page is not None and per_page is not None:
            items_offset = (page - 1) * per_page
            query = query.offset(items_offset).limit(per_page)
        return query.all()

    # get by id
    @classmethod
    def get_by_id(cls, db: Session, role_id: int) -> Optional['Permission']:
        return db.query(cls).filter_by(role_id=role_id).first()

    # get by name
    @classmethod
    def get_by_name(cls, db: Session, name: str) -> Optional['Permission']:
        return db.query(cls).filter_by(name=name).first()

    # create
    @classmethod
    def create(cls, db: Session, role: 'Role') -> 'Role':
        try:
            db.add(role)
            db.commit()
            db.refresh(role)
        except Exception as e:
            db.rollback()
            raise e
        else:
            return role

    # update
    @classmethod
    def update(cls, db: Session, role: 'Role') -> 'Role':
        try:
            db.add(role)
            db.commit()
            db.refresh(role)
        except Exception as e:
            db.rollback()
            raise e
        else:
            return role

    # delete
    @classmethod
    def delete(cls, db: Session, id: int) -> bool:
        role = db.query(cls).filter_by(role_id=id).first()
        role_exists = role is not None
        if role_exists:
            try:
                db.query(cls).filter_by(role_id=id).delete()
                db.commit()
                return True
            except Exception as e:
                db.rollback()
                raise e
        return False


class Permission(Base):
    __tablename__ = 'permissions'
    permission_id = Column(Integer, primary_key=True)
    # e.g., 'manage_clients', 'view_reports'
    name = Column(String(255), unique=True)
    roles = relationship("Role", secondary="role_permissions",
                         back_populates="permissions")

    # get all
    @classmethod
    def get_all(cls, db: Session, page: int = None, per_page: int = None) -> List['Permission']:
        query = db.query(cls)
        if page is not None and per_page is not None:
            items_offset = (page - 1) * per_page
            query = query.offset(items_offset).limit(per_page)
        return query.all()

    # get by id
    @classmethod
    def get_by_id(cls, db: Session, id: int) -> Optional['Role']:
        return db.query(cls).filter_by(permission_id=id).first()

    # get by name
    @classmethod
    def get_by_name(cls, db: Session, name: str) -> Optional['Role']:
        return db.query(cls).filter_by(name=name).first()

    # create
    @classmethod
    def create(cls, db: Session, permission: 'Permission') -> 'Permission':
        try:
            db.add(permission)
            db.commit()
            db.refresh(permission)
        except Exception as e:
            db.rollback()
            raise e
        else:
            return permission

    # update
    @classmethod
    def update(cls, db: Session, permission: 'Permission') -> 'Permission':
        try:
            db.add(permission)
            db.commit()
            db.refresh(permission)
        except Exception as e:
            db.rollback()
            raise e
        else:
            return permission

    # delete
    @classmethod
    def delete(cls, db: Session, id: int) -> bool:
        permission = db.query(cls).filter_by(permission_id=id).first()
        permission_exists = permission is not None
        if permission_exists:
            try:
                db.query(cls).filter_by(permission_id=id).delete()
                db.commit()
                return True
            except Exception as e:
                db.rollback()
                raise e
        return False


class RolePermission(Base):
    __tablename__ = 'role_permissions'
    role_id = Column(Integer, ForeignKey(
        'roles.role_id', ondelete='CASCADE'), primary_key=True)
    permission_id = Column(Integer, ForeignKey(
        'permissions.permission_id', ondelete='CASCADE'), primary_key=True)

    # get all by role id
    @classmethod
    def get_all_by_role_id(cls, db: Session, role_id: int) -> List['RolePermission']:
        return db.query(cls).filter_by(role_id=role_id).all()

    @classmethod
    def get_all_grouped_by_role_id(cls, db: Session) -> List[tuple]:
        return (
            db.query(cls.role_id, func.count('*').label('permission_count'))
            .group_by(cls.role_id)
            .all()
        )

    # get by role id and permission id
    @classmethod
    def get_by_role_id_and_permission_id(cls, db: Session, role_id: int, permission_id: int) -> Optional['RolePermission']:
        return db.query(cls).filter_by(role_id=role_id, permission_id=permission_id).first()

    # create
    @classmethod
    def create(cls, db: Session, role_permission: 'RolePermission') -> 'RolePermission':
        try:
            db.add(role_permission)
            db.commit()
            db.refresh(role_permission)
        except Exception as e:
            db.rollback()
            raise e
        else:
            return role_permission

    # delete
    @classmethod
    def delete(cls, db: Session, role_id: int, permission_id: int) -> bool:
        role_permission = db.query(cls).filter_by(
            role_id=role_id, permission_id=permission_id).first()
        role_permission_exists = role_permission is not None
        if role_permission_exists:
            try:
                db.query(cls).filter_by(role_id=role_id,
                                        permission_id=permission_id).delete()
                db.commit()
                return True
            except Exception as e:
                db.rollback()
                raise e
        return False

    def delete_all_by_role_id(cls, db: Session, role_id: int) -> bool:
        role_permissions = db.query(cls).filter_by(role_id=role_id).all()
        role_permissions_exist = role_permissions is not None
        if role_permissions_exist:
            try:
                db.query(cls).filter_by(role_id=role_id).delete()
                db.commit()
                return True
            except Exception as e:
                db.rollback()
                raise e
        return False


class Log(Base):
    __tablename__ = 'logs'

    log_id = Column(Integer, primary_key=True, index=True)
    website_id = Column(Integer, ForeignKey(
        "websites.website_id", ondelete="CASCADE"), nullable=True)
    event_type = Column(String(255))
    event_description = Column(Text)
    # metadata fields
    created_at = Column(DateTime, server_default=func.now())

    # get all
    @classmethod
    def get_all(cls, db: Session, page: int = None, per_page: int = None) -> Tuple[List['Log'], int]:
        query = db.query(cls).order_by(cls.created_at.desc())
        total_count = query.count()
        if page is not None and per_page is not None:
            items_offset = (page - 1) * per_page
            query = query.offset(items_offset).limit(per_page)
        return query.all(), total_count

    # get by id
    @classmethod
    def get_by_id(cls, db: Session, id: int) -> Optional['Log']:
        return db.query(cls).filter_by(log_id=id).first()

    # create
    @classmethod
    def create(cls, db: Session, log: 'Log') -> 'Log':
        try:
            db.add(log)
            db.commit()
            db.refresh(log)
        except Exception as e:
            db.rollback()
            raise e
        else:
            return log

    # update
    @classmethod
    def update(cls, db: Session, log: 'Log') -> 'Log':
        try:
            db.add(log)
            db.commit()
            db.refresh(log)
        except Exception as e:
            db.rollback()
            raise e
        else:
            return log

    # delete
    @classmethod
    def delete(cls, db: Session, id: int) -> bool:
        log = db.query(cls).filter_by(log_id=id).first()
        log_exists = log is not None
        if log_exists:
            try:
                db.query(cls).filter_by(log_id=id).delete()
                db.commit()
                return True
            except Exception as e:
                db.rollback()
                raise e
        return False


class ProductsIndexation(Base):
    __tablename__ = 'products_indexations'

    product_indexation_id = Column(Integer, primary_key=True, index=True)
    start_date = Column(DateTime)
    duration = Column(Integer, server_default='0')  # in seconds
    download_duration = Column(Integer, server_default='0')  # in seconds
    total_products = Column(Integer, server_default='0')
    status = Column(Enum(ProductIndexationStatusEnum, values_callable=lambda obj: [
                    e.value for e in obj]), nullable=False)
    message = Column(Text)
    website_id = Column(Integer, ForeignKey(
        "websites.website_id", ondelete="CASCADE"))
    # metadata fields
    created_at = Column(DateTime, server_default=func.now())

    # website = relationship("Website", back_populates="products_indexations")

    # get all
    @classmethod
    def get_all(cls, db: Session, page: int = None, per_page: int = None) -> List['ProductsIndexation']:
        query = db.query(cls)
        if page is not None and per_page is not None:
            items_offset = (page - 1) * per_page
            query = query.offset(items_offset).limit(per_page)
            for q in query.all():
                if q.duration is None:
                    q.duration = 0
                if q.download_duration is None:
                    q.download_duration = 0
                if q.total_products is None:
                    q.total_products = 0
        return query

    # get by id
    @classmethod
    def get_by_id(cls, db: Session, id: int) -> Optional['ProductsIndexation']:
        return db.query(cls).filter_by(product_indexation_id=id).first()

    # get by website id
    @classmethod
    def get_by_website_id(cls, db: Session, website_id: int, page: int = None, per_page: int = None) -> List['ProductsIndexation']:
        query = db.query(cls).filter_by(website_id=website_id)
        if page is not None and per_page is not None:
            items_offset = (page - 1) * per_page
            query = query.offset(items_offset).limit(per_page)
        query = query.all()
        for q in query:
            if q.duration is None:
                q.duration = 0
            if q.download_duration is None:
                q.download_duration = 0
            if q.total_products is None:
                q.total_products = 0
        return query

    # get the last inserted product indexation by website id
    @classmethod
    def get_last_inserted_by_website_id(cls, db: Session, website_id: int) -> Optional['ProductsIndexation']:
        return db.query(cls).filter_by(website_id=website_id).order_by(ProductsIndexation.created_at.desc()).first()

    # create
    @classmethod
    def create(cls, db: Session, products_indexation: 'ProductsIndexation') -> 'ProductsIndexation':
        try:
            db.add(products_indexation)
            db.commit()
            db.refresh(products_indexation)
        except Exception as e:
            db.rollback()
            raise e
        else:
            return products_indexation

    # update
    @classmethod
    def update(cls, db: Session, products_indexation: 'ProductsIndexation') -> 'ProductsIndexation':
        try:
            db.add(products_indexation)
            db.commit()
            db.refresh(products_indexation)
        except Exception as e:
            db.rollback()
            raise e
        else:
            return products_indexation

    # delete
    @classmethod
    def delete(cls, db: Session, id: int) -> bool:
        products_indexation = db.query(cls).filter_by(
            product_indexation_id=id).first()
        products_indexation_exists = products_indexation is not None
        if products_indexation_exists:
            try:
                db.query(cls).filter_by(product_indexation_id=id).delete()
                db.commit()
                return True
            except Exception as e:
                db.rollback()
                raise e
        return False


class ScheduledTask(Base):
    __tablename__ = 'scheduled_tasks'

    task_id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(String(500))
    frequency = Column(Enum(ScheduleFrequency), nullable=False)
    indexing_type = Column(Enum(IndexingType), nullable=False)
    time = Column(DateTime, nullable=False)
    is_active = Column(Boolean, default=True)
    is_scheduled = Column(Boolean, server_default='0', nullable=False)
    hours_interval = Column(Integer, nullable=True)
    last_run = Column(DateTime)
    next_run = Column(DateTime)
    website_id = Column(Integer, ForeignKey("websites.website_id"), nullable=True)  # Make it nullable
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    website = relationship("Website")
    execution_logs = relationship("TaskExecutionLog", back_populates="task")

    @classmethod
    def get_all(cls, db: Session, page: int = None, per_page: int = None):
        query = db.query(cls)
        if page is not None and per_page is not None:
            items_offset = (page - 1) * per_page
            query = query.offset(items_offset).limit(per_page)
        return query.all()

    @classmethod
    def get_by_id(cls, db: Session, task_id: int):
        return db.query(cls).filter(cls.task_id == task_id).first()

    @classmethod
    def create(cls, db: Session, task: 'ScheduledTask'):
        try:
            db.add(task)
            db.commit()
            db.refresh(task)
            return task
        except Exception as e:
            db.rollback()
            raise e

    @classmethod
    def update(cls, db: Session, task: 'ScheduledTask'):
        try:
            db.add(task)
            db.commit()
            db.refresh(task)
            return task
        except Exception as e:
            db.rollback()
            raise e

    @classmethod
    def delete(cls, db: Session, task_id: int):
        task = db.query(cls).filter(cls.task_id == task_id).first()
        if task:
            try:
                db.delete(task)
                db.commit()
                return True
            except Exception as e:
                db.rollback()
                raise e
        return False

class TaskExecutionLog(Base):
    __tablename__ = 'task_execution_logs'

    log_id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, ForeignKey("scheduled_tasks.task_id"))
    execution_time = Column(DateTime, server_default=func.now())
    status = Column(String(50))  # 'success', 'failure'
    message = Column(String(500))
    total_products_indexed = Column(Integer, default=0)
    duration = Column(Integer)  # in seconds

    task = relationship("ScheduledTask", back_populates="execution_logs")

    @classmethod
    def create(cls, db: Session, log: 'TaskExecutionLog'):
        try:
            db.add(log)
            db.commit()
            db.refresh(log)
            return log
        except Exception as e:
            db.rollback()
            raise e

    @classmethod
    def get_by_task_id(cls, db: Session, task_id: int, page: int = None, per_page: int = None):
        query = db.query(cls).filter(cls.task_id == task_id)
        if page is not None and per_page is not None:
            items_offset = (page - 1) * per_page
            query = query.offset(items_offset).limit(per_page)
        return query.all()
