from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from config import MARIADB_PASSWORD, MARIADB_USER, MARIADB_HOST, MARIADB_PORT, MARIADB_DB

# Construct the connection string
DATABASE_URL = f"mariadb+mariadbconnector://{MARIADB_USER}:{MARIADB_PASSWORD}@{MARIADB_HOST}:{MARIADB_PORT}/{MARIADB_DB}"

# Create the engine with explicit settings for production
engine = create_engine(
    DATABASE_URL, 
    pool_size=20,  # Adjust based on your expected concurrent connections
    max_overflow=30,  # Adjust based on your expected connection spikes
    pool_timeout=30,  # Timeout for connection pool in seconds
    pool_recycle=3600,  # Recycle connections after 1 hour (3600 seconds)
    pool_pre_ping=True,  # Enable connection liveness check
    echo=False  # Explicitly disable SQL echoing
)

# Create the session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Declare the base model
Base = declarative_base()