    const db_search_script = document.currentScript;
    const inputSearchSelector = db_search_script.dataset.inputSearchSelector;
    const resultsContainerSelector = db_search_script.dataset.resultsContainerSelector;

    const instances = {};

    async function instanciateMobileSearch() {
        try {
            let responsiveSearch = new ResponsiveSearch(inputSearchSelector);
            await responsiveSearch.demoSuggestions();
            responsiveSearch.setTargetContainerSelector(resultsContainerSelector);
            instances['responsiveSearch'] = responsiveSearch;
        } catch (error) {
            console.error("Error instantiating mobile search:", error);
        }
    }

    function instanciateDesktopSearch() {
        const searchBoxes = document.querySelectorAll(inputSearchSelector);
        const desktopSearch = new ModalManager(searchBoxes[searchBoxes.length - 1], searchBoxes);
        desktopSearch.setTargetResultContainer(resultsContainerSelector);
        desktopSearch.setTargetInputSelector(inputSearchSelector);
        instances['desktopSearch'] = desktopSearch;
    }

    function initializeSearch() {
        if (window.matchMedia("(max-width: 992px)").matches) {
            instanciateMobileSearch();
        } else {
            instanciateDesktopSearch();
        }
    }

    function handleScreenSizeChange(mql) {
        if (mql.matches) { // Mobile
            if (instances['desktopSearch']) {
                instances['desktopSearch'].screenSizeChanged();
            }
            if (!instances['responsiveSearch']) {
                instanciateMobileSearch();
            } else {
                instances['responsiveSearch'].resetScreenSizeChanged();
            }
        } else { // Desktop
            if (instances['responsiveSearch']) {
                instances['responsiveSearch'].screenSizeChanged();
            }
            if (!instances['desktopSearch']) {
                instanciateDesktopSearch();
            } else {
                instances['desktopSearch'].resetScreenSizeChanged();
            }
        }
    }

    const mediaQuery = window.matchMedia("(max-width: 992px)");
    mediaQuery.addEventListener('change', handleScreenSizeChange);

    initializeSearch();
