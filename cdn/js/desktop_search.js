class SearchHandler {
  constructor(searchBox) {
    this.debounceDelay = 300; // Debounce delay in milliseconds
    this.throttleInterval = 1000; // 1000 milliseconds (1 second)
    this.lastRan = null;
    this.searchPattern = '';
    this.searchResults = [];
    this.totalProducts = 0;
    this.timeoutId = null;
    this.currentPage = 0;
    this.pageSize = 20;
    this.r_only_pageSize = 20;
    this.data = [];
    this.filterElements = {};
    this.searchBox = searchBox;
    this.additionalSearchPattern = null;
  }

  setSearchBox(searchBox) {
    this.searchBox = searchBox;
  }

  setCurrentPage(currentPage) {
    if (currentPage >= 0 && currentPage <= Math.ceil(this.totalProducts / this.pageSize)) {
      this.currentPage = currentPage;
    }
  }

  setPageSize(pageSize) {
    if (pageSize > 0) {
      this.pageSize = pageSize;
    }
  }

  setTotalProducts(totalProducts) {
    if (totalProducts) {
      this.totalProducts = totalProducts;
    }
  }

  setData(data) {
    if (data) {
      this.data = data;
    }
  }

  setSearchPattern(searchPattern) {
    if (searchPattern) {
      this.searchPattern = searchPattern;
    }
  }

  setAdditionalSearchPattern(additionalSearchPattern) {
    this.additionalSearchPattern = additionalSearchPattern;
  }

  getSearchPattern() {
    return this.searchPattern;
  }

  getData() {
    return this.data;
  }

  getCurrentPage() {
    return this.currentPage;
  }

  getPageSize() {
    return this.pageSize;
  }

  getTotalProducts() {
    return this.totalProducts;
  }

  getFilterElements() {
    return this.filterElements;
  }

  async debouncedThrottledSearch() {
    const now = Date.now();

    // Clear the previous timeout if it exists
    if (this.timeoutId !== null) {
      clearTimeout(this.timeoutId);
    }

    // Throttle check: proceed if enough time has passed since the last execution
    if (this.lastRan === null || (now - this.lastRan) >= this.throttleInterval) {
      this.lastRan = now;
      this.performSearch();
    }
    else {
      // Debounce check: wait for the specified time and then proceed
      this.timeoutId = setTimeout(async () => {
        this.lastRan = now;
        this.performSearch();
      }, this.debounceDelay);
    }
  }

  async performSearch() {
    this.searchPattern = this.searchBox.value;
    if (!this.searchPattern) {
      this.resetSuggestions();
      return;
    }
    // Reset filters and pagination for a new search
    this.additionalSearchPattern = {}; // Clear filters
    this.currentPage = 0; // Reset to first page
    this.pageSize = this.r_only_pageSize; // Reset to default (20)

    try {
      const result = await apiCall(this.searchPattern, this.currentPage, this.pageSize);
      this.updateUI(result);
      // Update URL after search
      updateSearchUrl(this.searchPattern, this.additionalSearchPattern, this.currentPage, this.pageSize);
    } catch (error) {
      console.log('Error while searching', error);
    }
  }

  resetSuggestions() {
    // Emit the 'updateSidebarTitle' event with custom event detail if necessary
    const updateEvent = new CustomEvent('updateSidebarTitle', {
      detail: {} // Add any relevant event details here
    });
    document.dispatchEvent(updateEvent);
    // Emit the'resetSuggestions' event to clear current suggestions
    const resetEvent = new CustomEvent('resetSuggestions');
    document.dispatchEvent(resetEvent);
  }

  updateUI(result) {
    // update data and possibly other parts of the UI
    this.data = result ? result.data : [];
    this.totalProducts = result ? result.totalProducts : 0;

    if (result && result.data.length) {
      // dispatch the updateModal event
      const updateModalEvent = new CustomEvent('updateModal')
      document.dispatchEvent(updateModalEvent);
    }
  }

  async debouncedSearch() {
    // Clear the previous timeout if it exists
    if (this.timeoutId !== null) {
      clearTimeout(this.timeoutId);
    }

    // Set the new timeout
    this.timeoutId = setTimeout(async () => {
      this.searchPattern = this.searchBox.value;
      if (!this.searchPattern) {
        // Emit the 'resetSuggestions' event to clear current suggestions
        const resetEvent = new CustomEvent('resetSuggestions');
        document.dispatchEvent(resetEvent);
        // Emit the 'updateSidebarTitle' event with custom event detail if necessary
        const updateEvent = new CustomEvent('updateSidebarTitle', {
          detail: {} // Add any relevant event details here
        });
        document.dispatchEvent(updateEvent);
        return;
      }

      try {
        const result = await apiCall(this.searchPattern, this.currentPage, this.pageSize);
        this.data = result ? result.data : [];
        this.totalProducts = result ? result.totalProducts : 0;

        if (result && result.data.length) {
          let suggestion = this.inlineAutoComplete(this.searchPattern, this.searchResults);

          // Update the search box with the suggestion
          this.searchBox.value = suggestion;
          this.searchBox.setSelectionRange(this.searchBox.selectionStart, suggestion.length);

          // Render main content and suggestions
          // Dispatch the 'updateModal' event
          const updateModalEvent = new CustomEvent('updateModal');
          document.dispatchEvent(updateModalEvent);
        }
      } catch (error) {
        console.error('Error in normalSearch:', error);
      }

    }, this.debounceDelay);
  }

  async normalSearch() {
    try {
      const result = await apiCall(this.searchPattern, this.currentPage, this.pageSize, this.additionalSearchPattern);
      this.data = result ? result.data : [];
      this.totalProducts = result ? result.totalProducts : 0;
      this.filterElements = result ? result.filterElements : {};
      // Update URL after search
      // updateSearchUrl(this.searchPattern, this.additionalSearchPattern, this.currentPage, this.pageSize);
    } catch (error) {
      console.log(error);
    }
  }

  inlineAutoComplete(input, results) {
    let lowerInput = input.toLowerCase();
    let bestSuggestion = input;
    let longestMatchLength = 0;

    for (let i = 0; i < results.length; i++) {
      let lowerResultName = results[i].name.toLowerCase();
      let match = lowerResultName.match(new RegExp(`${lowerInput}(\\s*\\w*)`));

      if (match && match[1].length > longestMatchLength) {
        bestSuggestion = input + match[1];
        longestMatchLength = match[1].length;
      }
    }

    return bestSuggestion;
  }
}

class SearchResultPage {
  constructor(searchHandlerInstance, translations) {
    this.searchHandler = searchHandlerInstance;
    this.targetContainer = ".db-all-main-container";
    this.resultSearchPattern = "";
    this.additionalSearchPattern = {};
    this.categories_roots = [];
    this.current_page = 1;
    this.items_per_page = 20;
    this.search_state = {
      'search_data': null,
      'filter_data': null,
    }
    this.translations = translations;

    this.min_price = null;
    this.max_price = null;

    this.priceFilter = null;
  }

  initMaxPrice(min_price, max_price) {
    if (!this.min_price) {
      this.min_price = min_price;
    }

    if (!this.max_price) {
      this.max_price = max_price;
    }

    if (min_price < this.min_price) {
      this.min_price = min_price;
    }

    if (max_price > this.max_price) {
      this.max_price = max_price;
    }
  }

  setTargetContainer(targetContainer) {
    this.targetContainer = targetContainer;
  }

  getResultSearchPattern() {
    return this.resultSearchPattern;
  }

  setResultSearchPattern(pattern) {
    this.resultSearchPattern = pattern;
  }

  getAdditionalSearchPattern() {
    return this.additionalSearchPattern;
  }

  setAdditionalSearchPattern(pattern) {
    this.additionalSearchPattern = pattern;
  }

  getCategoriesRoots() {
    return this.categories_roots;
  }

  setCategoriesRoots(roots) {
    this.categories_roots = roots;
  }

  getCurrentPage() {
    return this.current_page;
  }

  setCurrentPage(page) {
    if (page > 0) {
      this.current_page = page;
    }
  }

  getItemsPerPage() {
    return this.items_per_page;
  }

  setItemsPerPage(items) {
    if (items > 0) {
      this.items_per_page = items;
    }
  }

  getSearchState() {
    return this.search_state;
  }

  setSearchState(searchData, filterData) {
    this.search_state = {
      'search_data': searchData,
      'filter_data': filterData,
    };
  }

  resetTargetContainer() {
    const container = document.querySelector(this.targetContainer);
    container.innerHTML = '';
  }

  categories_preprocessing(categories) {
    const filtered_categories = new Set();

    categories.forEach(category => {
      let splitted_cats = category.split('_');
      if ('cat' in this.additionalSearchPattern) {
        let filtered_cats = splitted_cats.filter(cat => !this.additionalSearchPattern['cat'].includes(cat));
        if (filtered_cats.length > 0) {
          filtered_categories.add(filtered_cats[0]);
        }
      }
      else {
        if (splitted_cats.length > 0) {
          filtered_categories.add(splitted_cats[0]);
        }
      }
    });

    return Array.from(filtered_categories);
  }

  createFilters(data) {
    const filterElements = this.searchHandler.getFilterElements();
    this.initMaxPrice(filterElements['minPrice'], filterElements['maxPrice']);
    const categories = this.categories_preprocessing(filterElements['categories']);
    const products_types = new Set(filterElements['productTypes']);
    const manufacturers = new Set(filterElements['manufacturers']);

    const getFilterDisplayName = (filterField) => {
      for (const filter of filterDefinitions.filters) {
        if (filter.field === filterField) {
          return filter.displayName;
        }
      }
    }


    let priceOptions = [this.min_price, this.max_price, 0.01];
    const filters = [
      { item: createFilterItem(getFilterDisplayName('cat'), categories.map(cat => ({ text: cat, value: cat })), this.handleCategoryCheckbox.bind(this), this.additionalSearchPattern, filterTypes.category), type: filterTypes.category },
      { item: createFilterItem(getFilterDisplayName('Produkttyp'), Array.from(products_types).map(type => ({ text: type, value: type })), this.handleProductTypeCheckbox.bind(this), this.additionalSearchPattern, filterTypes.productType), type: filterTypes.productType },
      { item: createFilterItem(getFilterDisplayName('vendor'), Array.from(manufacturers).map(mfr => ({ text: mfr, value: mfr })), this.handleManufacturerCheckbox.bind(this), this.additionalSearchPattern, filterTypes.manufacturer), type: filterTypes.manufacturer },
      { item: createFilterItem(getFilterDisplayName('price'), priceOptions, null, null, filterTypes.price), type: filterTypes.price },
    ];

    // filter filters based on the filterDefinitions set up by the customer
    if (filterDefinitions) {
      // Create a map of field to order
      const fieldOrderMap = filterDefinitions.filters.reduce((acc, filter) => {
        acc[filter.field] = filter.order;
        return acc;
      }, {});

      // Function to determine the order for each filter
      function getOrderForFilter(filter) {
        switch (filter.type) {
          case filterTypes.category:
            return fieldOrderMap['cat'];
          case filterTypes.productType:
            return fieldOrderMap['Produkttyp'];
          case filterTypes.manufacturer:
            return fieldOrderMap['vendor'];
          case filterTypes.price:
            return fieldOrderMap['price'];
          default:
            return Number.MAX_SAFE_INTEGER; // Or a high value if no order is defined
        }
      }
      // Sort the filters array by the order field
      const sortedFilters = filters.sort((a, b) => getOrderForFilter(a) - getOrderForFilter(b));

      return sortedFilters.map(filter => filter.item);
    }

    return filters;
  }

  handleCategoryCheckbox(event) {
    const checkbox = event.target;
    const value = checkbox.value;

    if (!this.additionalSearchPattern.hasOwnProperty('cat')) {
      this.additionalSearchPattern['cat'] = [];
    }

    if (checkbox.checked && !this.additionalSearchPattern['cat'].includes(value)) {
      this.additionalSearchPattern['cat'].push(value);
    } else {
      delete this.additionalSearchPattern['cat'];
    }
    this.handleFilter()
  }

  handleProductTypeCheckbox(event) {
    const checkbox = event.target;
    const value = checkbox.value.trim();
    if (checkbox.checked) {
      this.additionalSearchPattern['Produkttyp'] = value;
    } else {
      delete this.additionalSearchPattern['Produkttyp'];
    }
    this.handleFilter()
  }

  handleManufacturerCheckbox(event) {
    const checkbox = event.target;
    const value = checkbox.value;
    if (checkbox.checked) {
      this.additionalSearchPattern['vendor'] = value;
    } else {
      delete this.additionalSearchPattern['vendor'];
    }
    this.handleFilter()
  }

  // handle price filter
  handlePriceRange(minValue, maxValue) {
    this.additionalSearchPattern['price'] = {
      min: minValue,
      max: maxValue
    };

    this.handleFilter()
  }

  // handle clear price range
  handleClearPriceRange() {
    if ('price' in this.additionalSearchPattern) {
      delete this.additionalSearchPattern['price'];
      this.handleFilter()
    }
  }

  async handleFilter() {
    // show loading indicator
    showLoadingIndicator()

    await this.fetchData();

    // hide loading indicator
    hideLoadingIndicator()

    if (!this.search_state['search_data']) {
      return;
    }
    let result = this.search_state['search_data'];
    this.createResultPage(result.data, this.current_page, this.items_per_page, result.totalProducts);
    updateSearchUrl(this.searchHandler.getSearchPattern(), this.additionalSearchPattern, this.current_page - 1, this.items_per_page);
  }

  async fetchData() {
    this.searchHandler.setCurrentPage(this.current_page - 1);
    this.searchHandler.setPageSize(this.items_per_page);
    this.searchHandler.setAdditionalSearchPattern(this.additionalSearchPattern);
    await this.searchHandler.normalSearch();

    this.search_state['search_data'] = {
      'data': this.searchHandler.getData(),
      'totalProducts': this.searchHandler.getTotalProducts(),
    };
  }

  initFilter() {

    /****** init price filter ****************/
    const priceFilterOptions = {
      sliderId: 'db_slider',
      minHandleId: 'db_min-handle',
      maxHandleId: 'db_max-handle',
      rangeId: 'db_range',
      minInputId: 'db_min-price-input',
      maxInputId: 'db_max-price-input',
      clearButtonId: 'db_clear-button',
      applyButtonId: 'db_apply-button',
      formatValue: value => `${value.toFixed(2)}`,
      onChange: (minValue, maxValue) => {
        // Handle range change here
        this.handlePriceRange(minValue, maxValue);
      },
      onApply: (minValue, maxValue) => {
        this.handlePriceRange(minValue, maxValue);
      },
      onClear: () => {
        // Handle clear button click here
        this.handleClearPriceRange();
      }
    };
    const priceFilter = new RangeFilter(priceFilterOptions);
    this.priceFilter = priceFilter;
    const filterElements = this.searchHandler.getFilterElements();
    if ('price' in this.additionalSearchPattern) {
      let priceOptions = [...Object.values(this.additionalSearchPattern['price'])];
      priceOptions[0] = Math.max(priceOptions[0], filterElements['minPrice']);
      priceOptions[1] = Math.min(priceOptions[1], filterElements['maxPrice']);
      priceFilter.setInitialValues(...priceOptions);
    }
    else {
      priceFilter.setInitialValues(filterElements['minPrice'], filterElements['maxPrice']);
    }
    /***************************End init price filter ******************************/

    if (this.additionalSearchPattern && typeof this.additionalSearchPattern === 'object') {
      let f_buttons = document.querySelectorAll('.db_d_result_filter-button');
      for (let button of f_buttons) {
        let id = button.id;
        id = id.replace(/^db_/, '');
        if (this.additionalSearchPattern.hasOwnProperty('cat') && this.additionalSearchPattern['cat'].length > 0 && filterTypes.category.toLowerCase() === id) {
          button.dispatchEvent(new Event('click'));
        }
        if (this.additionalSearchPattern.hasOwnProperty('Produkttyp') && this.additionalSearchPattern['Produkttyp'] && filterTypes.productType.toLowerCase() === id) {
          button.dispatchEvent(new Event('click'));
        }
        if (this.additionalSearchPattern.hasOwnProperty('vendor') && this.additionalSearchPattern['vendor'] && filterTypes.manufacturer.toLowerCase() === id) {
          button.dispatchEvent(new Event('click'));
        }

        if (this.additionalSearchPattern.hasOwnProperty('price') && filterTypes.price.toLowerCase() === id) {
          button.dispatchEvent(new Event('click'));
        }

      }
    }
  }

  // async initialRender() {
  //   // reset the additional search pattern
  //   this.additionalSearchPattern = {};
  //   this.searchHandler.setCurrentPage(0);
  //   this.searchHandler.setPageSize(this.items_per_page);
  //   this.searchHandler.setAdditionalSearchPattern(this.additionalSearchPattern);

  //   await this.searchHandler.normalSearch();

  //   // create the result page
  //   this.createResultPage(this.searchHandler.getData(), this.current_page, this.items_per_page, this.searchHandler.getTotalProducts());
  // }

  async initialRender() {
    // Don’t reset unless there’s no URL state
    const { searchPattern, additionalSearchPattern, currentPage, pageSize } = parseSearchUrl();

    // Use URL-provided values if present, otherwise keep current or default
    this.searchHandler.setSearchPattern(searchPattern || this.searchHandler.getSearchPattern());
    this.searchHandler.setAdditionalSearchPattern(additionalSearchPattern || this.additionalSearchPattern);
    this.searchHandler.setCurrentPage(currentPage >= 0 ? currentPage : this.searchHandler.getCurrentPage());
    this.searchHandler.setPageSize(pageSize || this.items_per_page);

    // Sync SearchResultPage state
    this.current_page = (currentPage >= 0 ? currentPage + 1 : this.current_page) || 1; // UI is 1-based
    this.items_per_page = pageSize || this.items_per_page;
    this.additionalSearchPattern = additionalSearchPattern || this.additionalSearchPattern;

    // Fetch data with the current (or URL-provided) state
    await this.searchHandler.normalSearch();

    // Render the result page with the correct state
    this.createResultPage(
      this.searchHandler.getData(),
      this.current_page,
      this.items_per_page,
      this.searchHandler.getTotalProducts()
    );

    // Ensure filters are initialized correctly
    this.initFilter();
    // Update URL *after* rendering, using UI state
    updateSearchUrl(
      this.searchHandler.getSearchPattern(),
      this.additionalSearchPattern,
      this.current_page - 1, // 0-based for URL
      this.items_per_page
    );
  }

  async createResultPage(data, currentPage, itemsPerPage, totalResults) {
    const resultContainerWrapper = document.createElement('div');
    resultContainerWrapper.classList.add('db_d_result_container-wrapper');

    const filterContainer = document.createElement('div');
    filterContainer.classList.add('db_d_result_filter-container');
    resultContainerWrapper.appendChild(filterContainer);

    // create filters
    this.createFilters(data).forEach(element => {
      filterContainer.appendChild(element);
    })

    // append the loading indicator to the filter container
    const loadingIndicator = createLoadingContainer();
    filterContainer.appendChild(loadingIndicator);

    const resultContainer = document.createElement('div');
    resultContainer.classList.add('db_d_result_container');
    resultContainerWrapper.appendChild(resultContainer);

    // calculate the start and end indices for slicing the data array
    // const startIndex = (currentPage - 1) * itemsPerPage;
    const startIndex = 0;
    const endIndex = startIndex + itemsPerPage;
    const slicedData = data.slice(startIndex, endIndex);

    if (slicedData.length > 0) {
      slicedData.forEach(element => {
        resultContainer.appendChild(this.createProduct(element));
      });
    } else {
      const noResult = document.createElement('p');
      noResult.textContent = this.translations['no_results'];
      resultContainer.appendChild(noResult);
    }
    const container = document.querySelector(this.targetContainer);
    // empty the container
    container.innerHTML = "";
    container.appendChild(resultContainerWrapper);

    // configure filters
    this.initFilter();

    // create pagination
    const pagination = this.createPagination(currentPage, totalResults, itemsPerPage);
    container.appendChild(pagination);
  }

  createPagination(currentPage, totalResults, itemsPerPage) {
    // calculate the total number of pages
    const totalPages = Math.ceil(totalResults / itemsPerPage);
    const pagination = document.createElement('div');
    pagination.classList.add('db_d_result_pagination');

    const is_first_page = currentPage === 1;
    const is_last_page = currentPage === totalPages;

    // create the first and previous buttons
    const first = this.createPageButton(this.translations['pagination_first'], () => {
      currentPage = 1;
      this.handlePaginationButtonClick(currentPage, totalResults, itemsPerPage);
    }, is_first_page);
    const previous = this.createPageButton(this.translations['pagination_prev'], () => {
      if (currentPage > 1) {
        currentPage--;
      }
      this.handlePaginationButtonClick(currentPage, totalResults, itemsPerPage);
    }, is_first_page);
    pagination.appendChild(first);
    pagination.appendChild(previous);

    // determine the range of page numbers to display
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, startPage + 4);

    for (let i = startPage; i <= endPage; i++) {
      const pageButton = document.createElement('button');
      pageButton.textContent = i;
      pageButton.classList.add('db_d_result_pagination-button');
      if (i === currentPage) {
        pageButton.classList.add('db_d_result_pagination-button-active');
        // Disable the current page button
        pageButton.disabled = true;
        // remove the cursor on the current page button
        pageButton.style.cursor = 'default';
      }
      pageButton.addEventListener('click', () => {
        // remove the active class from all buttons
        const buttons = pagination.querySelectorAll('.db_d_result_pagination-button');
        buttons.forEach(function (button) {
          button.classList.remove('db_d_result_pagination-button-active');
        });
        // add the active class to the clicked button
        pageButton.classList.add('db_d_result_pagination-button-active');
        currentPage = i;
        this.handlePaginationButtonClick(currentPage, totalResults, itemsPerPage);
      })
      pagination.appendChild(pageButton);
    }

    // create the next and last buttons
    const next = this.createPageButton(this.translations['pagination_next'], () => {
      if (currentPage < totalPages) {
        currentPage++;
      }
      this.handlePaginationButtonClick(currentPage, totalResults, itemsPerPage);
    }, is_last_page);
    const last = this.createPageButton(this.translations['pagination_last'], () => {
      currentPage = totalPages;
      this.handlePaginationButtonClick(currentPage, totalResults, itemsPerPage);
    }, is_last_page);
    pagination.appendChild(next);
    pagination.appendChild(last);

    return pagination;
  }

  createPageButton(label, callback, isDisabled = false) {
    const button = document.createElement('button');
    button.textContent = label;
    button.classList.add('db_d_result_pagination-button');
    button.disabled = isDisabled;
    button.style.cursor = isDisabled ? 'default' : 'pointer';
    button.addEventListener('click', callback.bind(this));
    return button;
  }

  async handlePaginationButtonClick(currentPage, totalResults, itemsPerPage) {
    this.searchHandler.setCurrentPage(currentPage - 1);
    this.searchHandler.setPageSize(itemsPerPage);
    this.searchHandler.setAdditionalSearchPattern(this.additionalSearchPattern);
    await this.searchHandler.normalSearch();
    this.createResultPage(this.searchHandler.getData(), currentPage, itemsPerPage, totalResults);
    updateSearchUrl(this.searchHandler.getSearchPattern(), this.additionalSearchPattern, currentPage - 1, itemsPerPage);
  }

  createProduct(data) {
    const productCardWrapper = document.createElement('div');
    productCardWrapper.classList.add('db_d_result_product-card-wrapper');

    const productCard = document.createElement('a');
    productCard.classList.add('db_d_result_product-card');
    productCard.href = data.url;

    productCardWrapper.appendChild(productCard);

    const imageWrapper = document.createElement('div');
    imageWrapper.classList.add('db_d_result_product-img-wrapper');

    const image = document.createElement('img');
    image.src = data.image;
    image.alt = data.name;

    imageWrapper.appendChild(image);
    productCard.appendChild(imageWrapper);

    const productCardContent = document.createElement('div');
    productCardContent.classList.add('db_d_result_product-card-content');

    productCard.appendChild(productCardContent);

    const title = document.createElement('p');
    title.classList.add('db_d_result_product-title');
    title.textContent = data.name;
    productCardContent.appendChild(title);

    // devider
    const hr = document.createElement('hr');
    hr.classList.add('db_d_result_product-devider');

    productCardContent.appendChild(hr);

    // order number
    const orderNumberWrapper = document.createElement('div');
    orderNumberWrapper.classList.add('db_d_result_order-number-wrapper');
    const orderNumber = document.createElement('small');
    orderNumber.classList.add('db_d_result_order-number');
    orderNumber.textContent = `EAN: ${data.ordernumber}`;

    orderNumberWrapper.appendChild(orderNumber);
    productCardContent.appendChild(orderNumberWrapper);

    // product price wrapper
    const priceWrapper = document.createElement('div');
    priceWrapper.classList.add('db_d_result_product-price-wrapper');

    const price = document.createElement('p');
    price.classList.add('db_d_result_product-price');
    price.innerHTML = data.price + "&nbsp;€";
    const priceValue = data.price.replace(".", ",");
    price.innerHTML = priceValue + "&nbsp;€";
    const taxInfo = document.createElement('span');
    taxInfo.classList.add('db_d_result_tax-info');
    taxInfo.textContent = '(Inkl. MwSt. zzgl. Versand)';
    priceWrapper.appendChild(price);
    priceWrapper.appendChild(taxInfo);

    productCardContent.appendChild(priceWrapper);

    // Add an 'i' button with a prepend icon and text, wrapped in a container
    const buttonWrapper = document.createElement('div');
    buttonWrapper.classList.add('db_d_result_button-wrapper');

    const iButton = document.createElement('button');
    iButton.classList.add('db_d_result_button');

    const iconWrapper = document.createElement('span'); // Create an icon wrapper
    iconWrapper.classList.add('db_d_result_icon-wrapper');
    iButton.appendChild(iconWrapper); // Append the icon wrapper to the button

    const icon = document.createElement('i');
    icon.classList.add('db_d_result_prepend-icon', 'fa', 'fa-list');
    iconWrapper.appendChild(icon); // Append the icon to the icon wrapper

    const text = document.createElement('span');
    text.classList.add('db_d_result_button-text');
    text.textContent = this.translations['product_card_bttn'];
    iButton.appendChild(text);
    buttonWrapper.appendChild(iButton);

    productCard.appendChild(buttonWrapper);

    return productCardWrapper;
  }
}


class ModalManager {
  constructor(searchBox, searchBoxes = []) {
    this.searchBox = searchBox;
    this.searchBoxes = searchBoxes;
    this.targetInputSelector = '';
    this.translations = getTranslation();

    this.modal = null;
    this.overlay = null;
    this.suggestionsTitle = null;
    this.suggestionsListWrapper = null;
    this.closeButton = null;
    this.productsDiv = null;
    this.showAllButtonContainer = null;

    this.searchPattern = '';
    this.searchResults = [];
    this.topSearches = [];
    this.initialProducts = [];

    this.searchHandler = new SearchHandler(this.searchBox);
    this.searchResultPage = new SearchResultPage(this.searchHandler, this.translations);

    this.initModal();
  }

  initModal() {
    this.createModal();
    this.initZIndexes();
    this.loadTopSearches();

    // Check URL for existing search params
    const { searchPattern, additionalSearchPattern, currentPage, pageSize } = parseSearchUrl();
    if (searchPattern) {
      this.searchBox.value = searchPattern;
      this.searchHandler.setSearchPattern(searchPattern);
      this.searchHandler.setAdditionalSearchPattern(additionalSearchPattern || {});
      this.searchHandler.setCurrentPage(currentPage);
      this.searchHandler.setPageSize(pageSize);
      this.searchResultPage.setCurrentPage(currentPage + 1); // +1 for UI
      this.searchResultPage.setItemsPerPage(pageSize);
      this.searchResultPage.setAdditionalSearchPattern(additionalSearchPattern || {});
      this.searchResultPage.initialRender(); // Let initialRender handle the rest
      this.hideModal(); // Skip modal if URL has a query
    }
  }

  initZIndexes() {
    // this.searchBox.style.zIndex = 10000;
    this.searchBox.parentNode.style.zIndex = 10000;
    this.searchBox.style.position = 'relative';
    this.searchBox.classList.add('db_d_input_autocomplete');
    this.modal.style.zIndex = 10000;
    this.overlay.style.zIndex = 9999;
  }


  async loadTopSearches() {
    let res = await getDiversifiedTopSearches(12, 5, 2);
    if (!res) {
      res = [];
    }
    res.sort((a, b) => b.count - a.count);
    const prefixes = new Set();
    const filteredRes = [];
    for (const item of res) {
      const prefix = item.search_term.split(' ')[0];
      if (!prefixes.has(prefix)) {
        prefixes.add(prefix);
        filteredRes.push(item);
      }
      if (filteredRes.length >= 6) {
        break;
      }
    }
    if (filteredRes.length < 6) {
      const defaultSearches = ['makita', 'adventskalender', 'milwaukee', 'festool', 'bosch', 'dewalt', 'parksida', 'metabo'];
      const missingElements = 6 - filteredRes.length;
      const additionalElements = defaultSearches.filter(item => {
        const prefix = item.split(' ')[0];
        if (prefixes.has(prefix)) {
          return false;
        }
        prefixes.add(prefix);
        return true;
      }).slice(0, missingElements).map(item => ({ 'search_term': item, 'count': 0 }));
      filteredRes.push(...additionalElements);
    }
    let default6 = await loadInitialProductsForModal(filteredRes.map(item => item['search_term']).slice(0, 6));
    this.topSearches = filteredRes.slice(0, 12);
    this.initialProducts = default6;
    this.createTopSearches();
    this.handleInitialProducts();
  }



  handleInitialProducts() {
    if (this.initialProducts && this.initialProducts.length) {
      this.showMainContent(this.initialProducts);
    }
  }

  createTopSearches() {
    let res = this.topSearches;
    if (res && res.length) {
      res = res.map(item => item['search_term']);
      const ul = document.createElement('ul');
      for (let elt of res) {
        const li = document.createElement('li');
        li.innerHTML = elt;
        li.addEventListener('click', () => {
          this.handlesuggestionClick(elt);
        })
        ul.appendChild(li);
      }
      // Append the ul to the suggestionsList
      this.resetSuggestionsContainer();
      this.suggestionsListWrapper.appendChild(ul);
    }
  }

  setTargetResultContainer(container) {
    this.searchResultPage.setTargetContainer(container);
  }

  setSearchBoxes(searchBoxes) {
    this.searchBoxes = searchBoxes;
  }

  setTargetInputSelector(selector) {
    this.targetInputSelector = selector;
  }

  setSearchBox(searchBox) {
    this.searchBox = searchBox;
    this.searchHandler.setSearchBox(searchBox);
  }

  screenSizeChanged() {
    this.searchResultPage.resetTargetContainer();
    this.modal.parentNode.removeChild(this.modal);
    this.overlay.parentNode.removeChild(this.overlay);
  }

  resetScreenSizeChanged() {
    document.body.appendChild(this.overlay);
    document.body.appendChild(this.modal);
  }

  updateSidebarTitle(title = this.translations['default']) {
    this.suggestionsTitle.innerHTML = title;
  }

  createModal() {
    const modal = document.createElement('div');
    const overlay = document.createElement('div');
    const modalContent = document.createElement('div');
    const suggestionsContainer = document.createElement('div');
    const suggestionsTitle = document.createElement('h3');
    const suggestionsListWrapper = document.createElement('div');
    const productsContainer = document.createElement('div');
    const titleContainer = document.createElement('div');
    const title = document.createElement('h2');
    const closeButton = document.createElement('button');
    const productsDiv = document.createElement('div');
    const showAllButtonContainer = document.createElement('div');
    const showAllText = document.createElement('h3');

    modal.classList.add('db_d_modal', 'db_d_hidden');
    overlay.classList.add('db_d_overlay', 'db_d_hidden');
    modalContent.classList.add('db_d_modal-content');
    suggestionsContainer.classList.add('db_d_suggestions-container');
    suggestionsTitle.classList.add('db_d_suggestions-title');
    suggestionsListWrapper.className = 'db_d_suggestions-list-wrapper';
    productsContainer.classList.add('db_d_products-container');
    titleContainer.classList.add('db_d_title-container');
    closeButton.classList.add('db_d_close-button');
    productsDiv.classList.add('db_d_products');
    showAllButtonContainer.classList.add('db_d_show-all-button-container');

    suggestionsTitle.innerHTML = this.translations['default'];
    title.textContent = this.translations['search_results'];
    closeButton.textContent = 'X';
    showAllText.textContent = this.translations['showAllBttn'];

    suggestionsContainer.appendChild(suggestionsTitle);
    suggestionsContainer.appendChild(suggestionsListWrapper);
    titleContainer.appendChild(title);
    titleContainer.appendChild(closeButton);
    showAllButtonContainer.appendChild(showAllText);
    productsContainer.appendChild(titleContainer);
    productsContainer.appendChild(productsDiv);
    productsContainer.appendChild(showAllButtonContainer);
    modalContent.appendChild(suggestionsContainer);
    modalContent.appendChild(document.createElement('hr'));
    modalContent.appendChild(productsContainer);
    modal.appendChild(modalContent);

    this.modal = modal;
    this.overlay = overlay;
    this.suggestionsTitle = suggestionsTitle;
    this.suggestionsListWrapper = suggestionsListWrapper;
    this.closeButton = closeButton;
    this.productsDiv = productsDiv;
    this.showAllButtonContainer = showAllButtonContainer;

    this.configureEvents();

    document.body.appendChild(overlay);
    document.body.appendChild(modal);
  }


  createProductItem(data) {
    const productCard = document.createElement('a');
    productCard.classList.add('db_d_product-card');
    productCard.href = data.url;

    // create product wrapper
    const productWrapper = document.createElement('div');
    productWrapper.classList.add('db_d_product-wrapper');

    const image = document.createElement('img');
    image.src = data.image;
    image.alt = data.name;
    productWrapper.appendChild(image);

    const title = document.createElement('p');
    title.classList.add('db_d_product-title');
    title.textContent = data.name;
    productWrapper.appendChild(title);

    // product price wrapper
    const priceWrapper = document.createElement('div');
    priceWrapper.classList.add('db_d_product-price-wrapper');

    const price = document.createElement('p');
    price.classList.add('db_d_product-price');
    price.innerHTML = data.price.replace('.', ',') + "&nbsp;€";
    priceWrapper.appendChild(price);
    productWrapper.appendChild(priceWrapper);
    productCard.appendChild(productWrapper);

    return productCard;
  }

  configureEvents() {
    document.addEventListener('click', this.handleGlobalClickEvent.bind(this));
    document.addEventListener('updateSidebarTitle', this.handleUpdateSidebarTitle.bind(this));
    document.addEventListener('resetSuggestions', this.handleResetSuggestions.bind(this));
    document.addEventListener('updateModal', this.handleUpdateModal.bind(this));
    document.addEventListener('scroll', this.handleScroll.bind(this));
    this.searchBoxes.forEach((searchBox) => {
      searchBox.addEventListener('focus', this.handleSearchBoxFocus.bind(this));
      searchBox.addEventListener('keydown', this.handleKeyDownEvent.bind(this));
      searchBox.addEventListener('input', this.handleSearchInput.bind(this));
    })
    this.showAllButtonContainer.addEventListener('click', this.handleShowResultsPage.bind(this));

    // Add popstate listener
    window.addEventListener('popstate', (event) => {
      const state = event.state || parseSearchUrl();
      if (state.searchPattern) {
        this.searchBox.value = state.searchPattern;
        this.searchHandler.setSearchPattern(state.searchPattern);
        this.searchHandler.setAdditionalSearchPattern(state.additionalSearchPattern || {});
        this.searchHandler.setCurrentPage(state.currentPage);
        this.searchHandler.setPageSize(state.pageSize);
        this.searchResultPage.setCurrentPage(state.currentPage + 1);
        this.searchResultPage.setItemsPerPage(state.pageSize);
        this.searchResultPage.setAdditionalSearchPattern(state.additionalSearchPattern || {});
        this.searchResultPage.initialRender();
        this.hideModal();
      } else {
        this.searchBox.value = '';
        this.searchResultPage.resetTargetContainer();
        this.showModal();
      }
    });
  }

  // handleScroll(event) {
  //   const sticky_header = document.querySelector('.et-sticky-header.et-sticky-visible');
  //   if (sticky_header) {
  //     const sticky_search_box = sticky_header.querySelector(this.targetInputSelector);
  //     const rect = sticky_search_box.getBoundingClientRect();
  //     this.modal.style.top = `${rect.bottom}px`;
  //     this.modal.style.position = 'fixed';
  //     this.overlay.style.zIndex = 1000;
  //   }
  //   else {
  //     this.modal.style.position = 'absolute';
  //     const rect = this.searchBox.getBoundingClientRect();
  //     this.modal.style.top = `${rect.bottom}px`;
  //     this.overlay.style.zIndex = 9999;
  //     // revert to the original search box
  //     this.revertToInitialSearchBox();
  //   }
  // }

  handleScroll(event) {
    const stickyHeader = document.querySelector('.et-sticky-header.et-sticky-visible');
    const viewportTop = window.scrollY; // Current scroll position
    let modalTopPosition;

    if (stickyHeader) {
      // Case 1: Sticky header is visible
      const stickySearchBox = stickyHeader.querySelector(this.targetInputSelector);
      if (!stickySearchBox) return; // Guard clause

      const rect = stickySearchBox.getBoundingClientRect();
      modalTopPosition = rect.bottom + viewportTop; // Convert to document-relative position
      this.modal.style.position = 'fixed';
      this.modal.style.top = `${rect.bottom}px`; // Viewport-relative position
      this.overlay.style.zIndex = 1000;
    } else {
      // Case 2: No sticky header - use original search box
      const searchBoxRect = this.searchBox.getBoundingClientRect();
      modalTopPosition = searchBoxRect.bottom + viewportTop;

      // Detect if modal would go off-screen (negative top)
      if (searchBoxRect.bottom < 0) {
        // Anchor modal to top of viewport when search box scrolls away
        this.modal.style.position = 'fixed';
        this.modal.style.top = '0';
      } else {
        // Default absolute positioning relative to search box
        this.modal.style.position = 'fixed';
        this.modal.style.top = `${searchBoxRect.bottom}px`;
      }
      this.overlay.style.zIndex = 9999;
      // this.searchBox.style.zIndex = 10000;
      this.searchBox.parentNode.style.zIndex = 10000;
      this.revertToInitialSearchBox();
    }

    // Optional: Handle modal sticking to viewport top
    if (modalTopPosition < viewportTop) {
      this.modal.style.position = 'fixed';
      this.modal.style.top = '0';
    }
  }

  handleSearchBoxFocus() {
    this.positonModal();
    this.showModal();
  }

  revertToInitialSearchBox() {
    this.searchBox = this.searchBoxes[this.searchBoxes.length - 1];
    this.setSearchBox(this.searchBox);
    this.positonModal();
  }

  handleGlobalClickEvent(event) {
    if (event.target === this.overlay || event.target === this.closeButton) {
      this.hideModal();
      this.searchBox.value = this.searchPattern;
    }
    else {
      this.searchBoxes.forEach((searchBox) => {
        if (event.target === searchBox && event.target !== this.searchBox) {
          this.setSearchBox(searchBox);
          this.searchBox.dispatchEvent(new Event('focus'));
        }
      })
    }
  }

  handleKeyDownEvent(event) {
    if (event.key === 'Tab' && event.target === this.searchBox) {
      event.preventDefault();
      if (!this.searchResults.length) {
        return;
      }
      // move the caret position to the end of user's original input
      this.searchBox.setSelectionRange(this.searchBox.value.length, this.searchBox.value.length);
      this.searchPattern = this.searchBox.value;
      // dispatch an input event to trigger the search
      this.searchBox.dispatchEvent(new Event('input'));
    }
    else if (event.key === 'Enter' && event.target === this.searchBox && this.searchBox.value) {
      event.preventDefault();
      this.searchBox.blur();
      this.showAllButtonContainer.dispatchEvent(new Event('click'));
      // update top searches
      updateTopSearches(this.searchBox.value);
    }
  }

  async handleSearchInput(event) {
    this.searchHandler.setCurrentPage(0);
    this.searchHandler.setPageSize(this.searchHandler.r_only_pageSize)
    // await this.searchHandler.debouncedSearch();
    await this.searchHandler.debouncedThrottledSearch();
  }

  handleUpdateSidebarTitle(event) {
    const detail = event.detail;
    if ('sidebarTitle' in detail) {
      this.updateSidebarTitle(detail.sidebarTitle);
    }
    else {
      this.updateSidebarTitle();
    }
  }

  handleResetSuggestions(event) {
    if (this.suggestionsListWrapper) {
      this.suggestionsListWrapper.innerHTML = '';
      this.createTopSearches();
      this.handleInitialProducts();
    }
  }

  handleUpdateModal(event) {
    this.showMainContent(this.searchHandler.getData());
    this.createSuggestions(this.searchHandler.getData(), this.searchHandler.getSearchPattern());
  }

  showMainContent(data) {
    // Clear the previous content of the main content
    this.resetProductsContianer();
    // Loop through the data and create product items
    for (let i = 0; i < data.length; i++) {
      const item = this.createProductItem(data[i]);
      this.productsDiv.appendChild(item);
      // display only the first 6 items
      if (i === 5) {
        break;
      }
    }
  }


  createSuggestions(data, input) {
    if (!data || !data.length) {
      return;
    }
    this.updateSidebarTitle(this.translations['autocomplete']);
    // Create a ul of lis with the elements in data
    const ul = document.createElement('ul');
    const listItems = new Map();
    const lowerCaseInput = input.toLowerCase();
    let i = 0;

    while (listItems.size < 12 && i < data.length) {
      const element = data[i];
      const name = element.name;
      const match = name.toLowerCase().match(new RegExp(`${lowerCaseInput}(\\s*\\w*)`));
      if (match && `${input}${match[1]}`.trim() !== input && !listItems.has(`${input}${match[1]}`.trim())) {
        listItems.set(`${input}${match[1]}`, `<span class="input">${input}</span><span class="suffix">${match[1]}</span>`);
      }
      i++;
    }

    listItems.forEach((value, key) => {
      const li = document.createElement('li');
      li.innerHTML = value;
      li.dataset.suggestion = key;
      li.addEventListener('click', () => {
        this.handlesuggestionClick(key);
      });
      ul.appendChild(li);
    });
    // Append the ul to the suggestionsList
    if (listItems.size) {
      this.resetSuggestionsContainer();
      this.suggestionsListWrapper.appendChild(ul);
    }
  }

  async handlesuggestionClick(clicked_pattern) { // TODO: to be modified for result page class
    this.searchHandler.setSearchPattern(clicked_pattern);
    this.searchBox.value = clicked_pattern;
    this.showAllButtonContainer.dispatchEvent(new Event('click'));
    // update top searches
    updateTopSearches(this.searchBox.value);
    updateSearchUrl(clicked_pattern, this.searchHandler.additionalSearchPattern); // Update here too
  }

  handleShowResultsPage(event) {
    // preconfiguration of the results page
    this.searchResultPage.initialRender();

    // close the modal
    this.hideModal();
  }

  resetProductsContianer() {
    this.productsDiv.innerHTML = '';
  }

  resetSuggestionsContainer() {
    this.suggestionsListWrapper.innerHTML = '';
  }

  positonModal() {
    const rect = this.searchBox.getBoundingClientRect();
    this.modal.style.top = `${rect.bottom}px`;
  }

  showModal() {
    this.modal.classList.remove('db_d_hidden');
    this.overlay.classList.remove('db_d_hidden');
  }

  hideModal() {
    this.modal.classList.add('db_d_hidden');
    this.overlay.classList.add('db_d_hidden');
  }

  setSearchResults(suggestions, products) {
    this.domManipulator.setSuggestions(suggestions);
    this.domManipulator.setProducts(products);
  }
}