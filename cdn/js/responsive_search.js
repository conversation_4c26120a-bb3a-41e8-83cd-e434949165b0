class MobileFilter {

        constructor(callback, additionalSearchPattern = {}) {
                this.filter_bar = null;
                this.filter_modal_instances = [];
                this.callback = callback;
                this.additionalSearchPattern = additionalSearchPattern;
        }

        getFilterBarNode() {
                return this.filter_bar.getFilterBar();
        }

        removeModals() {
                this.filter_modal_instances.forEach((filter_modal) => {
                        filter_modal.removeModal();
                });
        }

        extractFields(filterElements) {
                const fields = {
                        'cat': new Set(),
                        'vendor': new Set(filterElements['manufacturers']),
                        'productType': new Set(filterElements['productTypes']),
                }

                for (const category of filterElements['categories']) {
                        let cats = category.split('_');
                        if (cats.length > 0) {
                                if ('cat' in this.additionalSearchPattern) {
                                        const filteredCats = cats.filter(cat => !this.additionalSearchPattern['cat'].includes(cat));
                                        if (filteredCats.length > 0) {
                                                fields['cat'].add(filteredCats[0]);
                                        }
                                }
                                else {
                                        fields['cat'].add(cats[0]);
                                }
                        }
                }

                fields['cat'] = Array.from(fields['cat']);
                fields['vendor'] = Array.from(fields['vendor']);
                fields['productType'] = Array.from(fields['productType']);

                return fields
        }

        setupFilter(filterDefinitions, filterElements, minMax, filter_title = 'Filter') {
                const filterOptions = [];
                const fields = this.extractFields(filterElements);
                const categoryCheckboxes = this.createCheckboxes(fields['cat'], 'cat');
                const vendorCheckboxes = this.createCheckboxes(fields['vendor'], 'vendor');
                const productTypeCheckboxes = this.createCheckboxes(fields['productType'], 'Produkttyp');
                const priceFilterElement = createPriceFilter(minMax[0], minMax[1], 0.01);
                for (const filter of filterDefinitions.filters) {
                        const bodyContent = filter.field === 'cat' ? categoryCheckboxes : filter.field === 'vendor' ? vendorCheckboxes : filter.field === 'Produkttyp' ? productTypeCheckboxes : priceFilterElement;
                        const filterModal = new MobileFilterModal({
                                'headerText': filter.displayName,
                                'generateBodyContent': bodyContent,
                        });
                        this.filter_modal_instances.push(filterModal);
                        filterOptions.push({ 'type': filter.field, 'label': filter.displayName, 'onclick': () => filterModal.open() });
                }
                this.filter_bar = new FilterBar(filterOptions, filter_title);
        }

        shouldBeChecked(option, filter_type) {
                if (filter_type !== 'cat') {
                        if (this.additionalSearchPattern.hasOwnProperty(filter_type) && this.additionalSearchPattern[filter_type].trim() === option) {
                                return true
                        }
                }

                return false
        }

        createCheckboxes(options, filter_type) {
                const checkboxes = [];
                const indent = filter_type === 'cat' && this.additionalSearchPattern.hasOwnProperty('cat') && this.additionalSearchPattern['cat'].length > 0;
                if (filter_type === 'cat') {
                        if (this.additionalSearchPattern.hasOwnProperty('cat') && this.additionalSearchPattern['cat'].length > 0) {
                                const option = this.additionalSearchPattern['cat'].join(' => ');
                                checkboxes.push(this.createCheckbox(option, option, option, this.callback, true, filter_type));
                        }
                }
                else {
                        if (this.additionalSearchPattern.hasOwnProperty(filter_type) && this.additionalSearchPattern[filter_type].trim() !== '') {
                                options = options.filter((option) => option.trim() !== this.additionalSearchPattern[filter_type].trim());
                                options.unshift(this.additionalSearchPattern[filter_type]);
                        }
                }
                for (let option of options) {
                        if (!option) continue;
                        const isChecked = this.shouldBeChecked(option.trim(), filter_type)
                        checkboxes.push(this.createCheckbox(option, option, option, this.callback, isChecked, filter_type, indent));
                }

                return checkboxes
        }

        createCheckbox(option, value, name, callback, isChecked = false, filter_type = '', indent = false) {
                let checkboxContainer = document.createElement('div');
                checkboxContainer.className = 'db_m_result_checkbox-container';

                if (indent) {
                        checkboxContainer.classList.add('indent');
                }

                let label = document.createElement('label');

                let checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.name = name;
                checkbox.value = value;
                checkbox.id = `${name}-${value}`;
                checkbox.checked = isChecked; // Set the checked state
                checkbox.dataset.filterType = filter_type;

                label.htmlFor = checkbox.id;
                label.appendChild(document.createTextNode(' ' + option));

                checkboxContainer.appendChild(checkbox);
                checkboxContainer.appendChild(label);

                checkbox.addEventListener('change', callback);

                return checkboxContainer;
        }

}

class ResponsiveSearch {
        constructor(originalInputElementSelector = '#original_search') {
                // total number of products found
                this.totalProducts = 0;
                // page number
                this.currentPage = 1;
                // number of products per page
                this.pageSize = 20;
                // Maximum number of page buttons to show at once
                this.maxPageButtons = 10;
                // current search pattern
                this.searchPattern = "";

                // timeout for the search
                this.timeoutId = null;
                // debounce delay 
                this.debounceDelay = 300;

                this.searchResults = null;

                // target container selector
                this.targetContainerSelector = '.db_m_main-results-container';

                // input element selector
                this.inputElementSelector = '#db_m_search';

                // original input element selector
                this.originalInputElementSelector = originalInputElementSelector;

                this.searchInput = null;
                this.searchIconRight = null;
                this.modal = null;
                this.overlay = null;
                this.translations = getTranslation();

                this.mobileFilter = null;
                this.additionalSearchPattern = {};
                this.min_price = null;
                this.max_price = null;

                this.originalSearchBoxes = [];

                // set the original search boxes
                this.setOriginalSearchBoxes();

                // add popup to DOM
                this.addPopUpToDOM();
                // add overlay to DOM
                this.addOverlayToDOM();
                // init the existing search input
                // this.initExistingSearchInput();
                this.initExistingCscartSearchInput();
        }

        setOriginalSearchBoxes() {
                this.originalSearchBoxes = document.querySelectorAll(this.originalInputElementSelector);
        }

        updateOriginalSearchBoxesValues(value) {
                this.originalSearchBoxes.forEach((box) => {
                        box.value = value;
                });
        }

        async handleFilterChange(event) {
                event.preventDefault();

                const checkbox = event.target;
                const filterType = event.target.dataset.filterType;
                const filterValue = event.target.value;
                if (!filterValue) return;
                if (filterType === 'cat') {
                        if (!this.additionalSearchPattern.hasOwnProperty('cat')) {
                                this.additionalSearchPattern['cat'] = [];
                        }

                        if (checkbox.checked && !this.additionalSearchPattern['cat'].includes(filterValue)) {
                                this.additionalSearchPattern['cat'].push(filterValue);
                        } else {
                                delete this.additionalSearchPattern['cat'];
                        }
                }
                else {
                        if (checkbox.checked) {
                                this.additionalSearchPattern[filterType] = filterValue;
                        }
                        else {
                                delete this.additionalSearchPattern[filterType];
                        }
                }
                await this.fetchData();
                this.buildResultPage();
        }

        initPriceFilter() {
                const priceFilterOptions = {
                        sliderId: 'db_slider',
                        minHandleId: 'db_min-handle',
                        maxHandleId: 'db_max-handle',
                        rangeId: 'db_range',
                        minInputId: 'db_min-price-input',
                        maxInputId: 'db_max-price-input',
                        clearButtonId: 'db_clear-button',
                        applyButtonId: 'db_apply-button',
                        formatValue: value => `${value ? value.toFixed(2) : value}`,
                        onChange: (minValue, maxValue) => {
                                // Handle range change here
                                this.handlePriceRange(minValue, maxValue);
                        },
                        onApply: (minValue, maxValue) => {
                                this.handlePriceRange(minValue, maxValue);
                        },
                        onClear: () => {
                                // Handle clear button click here
                                console.log('Filter cleared');
                                this.handleClearPriceRange();
                        }
                };
                const priceFilter = new RangeFilter(priceFilterOptions);
                this.priceFilter = priceFilter;
                const filterElements = this.getFilterResults();
                if('price' in this.additionalSearchPattern){
                  let priceOptions = [...Object.values(this.additionalSearchPattern['price'])];
                  priceOptions[0] = Math.max(priceOptions[0], filterElements['minPrice']);
                  priceOptions[1] = Math.min(priceOptions[1], filterElements['maxPrice']);
                  priceFilter.setInitialValues(...priceOptions);
                }
                else{
                  priceFilter.setInitialValues(filterElements['minPrice'], filterElements['maxPrice']);
                }
        }

        async handlePriceRange(minValue, maxValue) {
                this.additionalSearchPattern['price'] = {
                        min: minValue,
                        max: maxValue
                };
                await this.fetchData();
                this.buildResultPage();
        }


        async handleClearPriceRange() {
                if ('price' in this.additionalSearchPattern) {
                        delete this.additionalSearchPattern['price'];
                        await this.fetchData();
                        this.buildResultPage();
                }
        }

        resetAdditionalSearchPattern() {
                this.additionalSearchPattern = {};
        }

        createSearchPopup() {
                // Create the outer 'div' element with its classes
                const outerDiv = document.createElement('div');
                outerDiv.className = 'db_m_toggle-modal db_m_modal';

                // Create the 'search-container' div
                const searchContainer = document.createElement('div');
                searchContainer.className = 'db_m_search-container';

                // Create the 'search-icon' div for the left arrow and append it to the 'search-container'
                const searchIconLeft = document.createElement('div');
                searchIconLeft.className = 'db_m_search-icon db_m_close-btn';
                searchIconLeft.innerHTML = '&larr;';
                searchContainer.appendChild(searchIconLeft);
                this.attachEventToNode(searchIconLeft, 'click', this.handleBackArrowClick.bind(this));

                // Create the 'input' element for search and append it to the 'search-container'
                const searchInput = document.createElement('input');
                searchInput.type = 'search';
                searchInput.id = 'db_m_search';
                searchInput.name = 'db_m_search';
                searchInput.placeholder = 'Search';
                searchContainer.appendChild(searchInput);
                this.searchInput = searchInput;
                this.attachEventToNode(searchInput, 'input', this.handleInputChange.bind(this));

                // Create the 'clear-icon' div and append it to the 'search-container'
                const clearIcon = document.createElement('div');
                clearIcon.className = 'db_m_search-icon db_m_clear-icon';
                searchContainer.appendChild(clearIcon);

                // Add click event listener to the clear icon to clear the search input
                this.attachEventToNode(clearIcon, 'click', this.handleClearIconClick.bind(this));

                // Create the 'search-icon' div for the magnifying glass icon and append it to the 'search-container'
                const searchIconRight = document.createElement('div');
                searchIconRight.className = 'db_m_search-icon db_m_magnify-icon';
                searchIconRight.textContent = '🔍'; // Using textContent for security and proper handling of special characters
                searchContainer.appendChild(searchIconRight);
                this.searchIconRight = searchIconRight;
                this.attachEventToNode(searchIconRight, 'click', this.handleSearchIconClick.bind(this));


                // Append the 'search-container' to the outer 'div'
                outerDiv.appendChild(searchContainer);

                // Create the suggestions container and append it to the outer 'div'
                const suggestionsContainer = document.createElement('div');
                suggestionsContainer.className = 'db_m_suggestions-container';
                outerDiv.appendChild(suggestionsContainer);

                document.addEventListener('keydown', this.handleKeyDownEvent.bind(this));

                return outerDiv;
        }

        addPopUpToDOM() {
                const searchPopup = this.createSearchPopup();
                document.body.appendChild(searchPopup);
                this.modal = searchPopup;
        }

        addOverlayToDOM() {
                // Create the 'div' element
                const divElement = document.createElement('div');

                // Add the classes to the 'div' element
                divElement.className = 'db_m_toggle-modal db_m_overlay';

                // Append the 'div' element to the body or a specific container in the DOM
                document.body.appendChild(divElement);
                this.overlay = divElement;

                // Add an event listener to the 'div' element
                divElement.addEventListener('click', (event) => {
                        this.toggleSearchPopup(event);
                })
        }

        screenSizeChanged() {
                document.querySelector(this.targetContainerSelector).innerHTML = '';
                this.modal.parentNode.removeChild(this.modal);
                this.overlay.parentNode.removeChild(this.overlay);
        }

        resetScreenSizeChanged() {
                document.body.appendChild(this.overlay);
                document.body.appendChild(this.modal);
        }

        attachEventToNode(node, event, callback) {
                node.addEventListener(event, callback);
        }

        toggleSearchPopup(event) {
                event.preventDefault();
                let modal = document.querySelector('.db_m_modal');
                let overlay = document.querySelector('.db_m_overlay');
                modal.classList.toggle('db_m_toggle-modal');
                overlay.classList.toggle('db_m_toggle-modal');
        }

        handleClearIconClick(event) {
                event.preventDefault();
                this.searchInput.value = '';
                this.updateOriginalSearchBoxesValues('')
                this.demoSuggestions();
        }

        handleBackArrowClick(event) {
                event.preventDefault();
                this.toggleSearchPopup(event);
        }

        async handleSearchIconClick(event) {
                event.preventDefault();
                let searchInputValue = this.getSearchInputElement();
                if (!searchInputValue || !searchInputValue.value) {
                        return;
                }
                searchInputValue = searchInputValue.value.trim();
                this.searchPattern = searchInputValue;

                this.toggleSearchPopup(event);

                // reset the current page
                this.resetCurrentPage();

                // reset filter
                this.resetAdditionalSearchPattern();

                await this.fetchData();

                // TODO:: implement result page
                this.buildResultPage();

                // update top searches
                updateTopSearches(searchInputValue);
        }

        resetCurrentPage() {
                this.currentPage = 1;
        }

        handleSuggestionIconClick(event) {
                // Prevent the default action for the event to avoid side effects.
                event.preventDefault();
                event.stopPropagation();

                // Retrieve the trimmed text content of the first child of the
                // clicked icon's parent element (likely a suggestion).
                const suggestionText = event.target.parentNode.firstChild
                        .textContent.trim();

                // Select the search input element by its ID.
                const searchInput = this.getSearchInputElement();

                // If the search input element exists, set its value to the
                this.updateInputSearchValue(suggestionText);
        }

        updateInputSearchValue(value) {
                const searchInput = this.getSearchInputElement();
                if (searchInput) {
                        searchInput.value = value;
                        this.updateOriginalSearchBoxesValues(value);
                }
        }

        handleInputChange(event) {
                // Prevent the default action for the event to avoid side effects.
                event.preventDefault();
                event.stopPropagation();

                // clear the previous timeout
                if (this.timeout) {
                        clearTimeout(this.timeout);
                }

                // Store the trimmed value from the event target
                const searchValue = event.target.value.trim();

                // update the original search boxes
                this.updateOriginalSearchBoxesValues(searchValue);

                // set the new timeout
                this.timeout = setTimeout(async () => {
                        // reset filter
                        this.resetAdditionalSearchPattern();

                        this.searchPattern = searchValue;
                        await this.fetchData();
                        this.generateSuggestions();
                }, this.debounceDelay);
        }

        handleKeyDownEvent(event) {
                if (event.key === 'Enter' && event.target === this.searchInput && this.searchInput.value) {
                        event.preventDefault();
                        this.searchInput.blur();
                        this.searchIconRight.dispatchEvent(new Event('click'));
                        if (this.searchInput.value) {
                                updateTopSearches(this.searchInput.value);
                        }
                }
        }

        initExistingSearchInput() {
                // Select the search input element by its ID.
                const searchInput = document.querySelector(this.originalInputElementSelector);
                this.attachEventToNode(searchInput, 'click', this.toggleSearchPopup.bind(this));
        }

        initExistingCscartSearchInput() {
                // Select all the search inputs
                // const searchBoxes = document.querySelectorAll("input[id^='search_input']");
                const searchBoxes = document.querySelectorAll(this.originalInputElementSelector);

                // Iterate over the searchBoxes NodeList and add the event listeners to each search box
                searchBoxes.forEach((searchBox) => {
                        this.attachEventToNode(searchBox, 'click', this.toggleSearchPopup.bind(this));
                });
        }

        setOriginalInputElementSelector(selector) {
                this.originalInputElementSelector = selector;
        }

        async handleSuggestionClick(event) {
                event.preventDefault();
                let text = event.target.firstChild.textContent.trim();
                this.searchPattern = text;
                this.updateInputSearchValue(text);
                this.toggleSearchPopup(event);

                // reset filter
                this.resetAdditionalSearchPattern();

                await this.fetchData();
                // TODO: implement result page
                this.buildResultPage();
        }

        createListWithArrows(items) {
                // Create the ul element
                const list = document.createElement('ul');

                // Iterate over each string in the items array
                items.forEach(item => {
                        // Create the list item
                        const listItem = document.createElement('li');

                        // Create the anchor element
                        const anchor = document.createElement('a');
                        anchor.setAttribute('href', '#');
                        anchor.textContent = item;
                        this.attachEventToNode(anchor, 'click', this.handleSuggestionClick.bind(this));

                        // Create the span element
                        const span = document.createElement('span');
                        span.className = 'db_m_arrow';
                        span.innerHTML = '&#8598;';
                        this.attachEventToNode(span, 'click', this.handleSuggestionIconClick.bind(this));

                        // Append the span to the anchor, and the anchor to the list item
                        anchor.appendChild(span);
                        listItem.appendChild(anchor);

                        // Append the list item to the list
                        list.appendChild(listItem);
                });

                // Return the completed list
                return list;
        }

        demoSuggestions() {
                // Example usage:
                const items = ['makita', 'adventskalender', 'milwaukee', 'festool', 'bosch', 'dewalt', 'parksida', 'metabo'];
                const listElement = this.createListWithArrows(items);

                // Assuming you have a container element to append the list to
                const container = document.querySelector('.db_m_modal .db_m_suggestions-container');
                // Empty the container first
                while (container.firstChild) {
                        container.removeChild(container.firstChild);
                }
                container.appendChild(listElement);
        }

        async fetchData() {
                const data = await apiCall(this.searchPattern, this.currentPage - 1, this.pageSize, this.additionalSearchPattern);
                this.searchResults = data;
        }

        hasSearchResults() {
                return this.searchResults && this.searchResults.data.length > 0;
        }

        generateSuggestions() {
                if (this.hasSearchResults()) {
                        let searchInput = this.getSearchInputElement();
                        if (searchInput && searchInput.value) {
                                let listItems = this.extractSuggestions(searchInput.value, this.searchResults.data);
                                if (listItems.size > 0) {
                                        let suggestions = this.createListWithArrows(Array.from(listItems));
                                        this.displaySuggestions(suggestions);
                                }
                        }
                }
        }

        displaySuggestions(suggestionsList) {
                const suggestionsContainer = this.getSuggestionsContainer();
                suggestionsContainer.innerHTML = ''; // Clear existing suggestions
                suggestionsContainer.appendChild(suggestionsList);
        }

        extractSuggestions(input, data) {
                const listItems = new Set();
                const lowerCaseInput = input.toLowerCase();
                let i = 0;

                while (listItems.size < 12 && i < data.length) {
                        const element = data[i];
                        const name = element.name;
                        const match = name.toLowerCase().match(new RegExp(`${lowerCaseInput}(\\s*\\w*)`));
                        if (match && `${input}${match[1]}`.trim() !== input && !listItems.has(`${input}${match[1]}`.trim())) {
                                listItems.add(`${input}${match[1]}`.trim());
                        }
                        i++;
                }
                return listItems;
        }

        getTotalResults() {
                return this.searchResults.totalProducts;
        }

        getResults() {
                return this.searchResults.data;
        }
        getFilterResults() {
                return this.searchResults['filterElements'];
        }

        getSearchInputElement() {
                return document.querySelector(this.inputElementSelector);
        }

        setInputElementSelector(selector) {
                this.inputElementSelector = selector;
        }

        getSuggestionsContainer() {
                return document.querySelector('.db_m_modal .db_m_suggestions-container');
        }

        getResultsContainer() {
                return document.querySelector(this.targetContainerSelector);
        }

        setTargetContainerSelector(selector) {
                this.targetContainerSelector = selector;
        }

        initMaxPrice(min_price, max_price) {
          if(!this.min_price) {
            this.min_price = min_price;
          }
      
          if(!this.max_price) {
            this.max_price = max_price;
          }
      
          if(min_price < this.min_price) {
            this.min_price = min_price;
          }
      
          if(max_price > this.max_price) {
            this.max_price = max_price;
          }
        }

        buildFilter() {
                if (this.mobileFilter) {
                        this.mobileFilter.removeModals();
                }
                const filterElements = this.getFilterResults();
                this.initMaxPrice(filterElements['minPrice'], filterElements['maxPrice']);
                const minMax = [this.min_price, this.max_price];
                this.mobileFilter = new MobileFilter(this.handleFilterChange.bind(this), this.additionalSearchPattern);
                this.mobileFilter.setupFilter(filterDefinitions, this.getFilterResults(), minMax);
                this.resultsContainer.prepend(this.mobileFilter.getFilterBarNode());
                this.initPriceFilter();
        }

        buildResultPage() {
                // Cache the results container if not already done
                if (!this.resultsContainer) {
                        this.resultsContainer = this.getResultsContainer();
                }
                const result = this.getResults();
                if(!result || result.length === 0) {
                        this.resultsContainer.appendChild(this.createNoResultMessage());
                        return;
                }
                const productCards = this.getResults().map(data => this.createProduct(data));
                this.resultsContainer.innerHTML = '';

                /*************init filter******************* */
                this.buildFilter();
                /*************End filter init***************************** */

                // Convert the array of product cards into a document fragment before appending
                const productCardsFragment = document.createElement('div');
                productCardsFragment.classList.add('db_m_result_product-cards_wrapper');
                productCards.forEach(card => productCardsFragment.appendChild(card));
                this.resultsContainer.appendChild(productCardsFragment);

                // pagination
                const pagination = this.createPagination(this.getTotalResults());
                this.getResultsContainer().appendChild(pagination);
        }

        createProduct(data) {
                const productCardWrapper = this.createWrapper('div', 'db_m_result_product-card-wrapper');
                const productCard = this.createWrapper('a', 'db_m_result_product-card', productCardWrapper, data.url);
                const imageWrapper = this.createWrapper('div', 'db_m_result_product-img-wrapper', productCard);
                const image = this.createImage(data);
                imageWrapper.appendChild(image);

                const productCardContent = this.createWrapper('div', 'db_m_result_product-card-content', productCard);
                this.createTitle(data, productCardContent);
                this.createPrice(data, productCardContent);
                this.createInfoButton(productCardContent);

                return productCardWrapper;
        }

        createNoResultMessage() {
                const noResultWrapper = this.createWrapper('div', 'db_m_result_no-result-wrapper')
                const noResultMessage = document.createElement('p')
                noResultMessage.classList.add('db_m_result_no-result-message')
                noResultMessage.textContent = 'Leider konnten wir keine Ergebnisse zu Ihrer Suche finden.'
                
                noResultWrapper.appendChild(noResultMessage)
                
                return noResultWrapper
        }


        createWrapper(elementType, className, parent = null, href = null) {
                const element = document.createElement(elementType);
                element.classList.add(className);
                if (href) element.href = href;
                if (parent) parent.appendChild(element);
                return element;
        }

        createImage(data) {
                const image = document.createElement('img');
                image.src = data.image;
                image.alt = data.name;
                return image;
        }

        createTitle(data, parent) {
                const title = document.createElement('p');
                title.classList.add('db_m_result_product-title');
                title.textContent = data.name;
                parent.appendChild(title);
        }

        createDivider(parent) {
                const hr = document.createElement('hr');
                hr.classList.add('db_m_result_product-devider');
                parent.appendChild(hr);
        }

        createOrderNumber(data, parent) {
                const orderNumberWrapper = this.createWrapper('div', 'db_m_result_order-number-wrapper', parent);
                const orderNumber = document.createElement('small');
                orderNumber.classList.add('db_m_result_order-number');
                orderNumber.textContent = `EAN: ${data.ordernumber}`;
                orderNumberWrapper.appendChild(orderNumber);
        }

        createPrice(data, parent) {
                const priceWrapper = this.createWrapper('div', 'db_m_result_product-price-wrapper', parent);
                const price = document.createElement('p');
                price.classList.add('db_m_result_product-price');
                price.innerHTML = data.price.replace('.', ',') + "&nbsp;€";
                const taxInfo = document.createElement('span');
                taxInfo.classList.add('db_m_result_tax-info');
                taxInfo.textContent = '(Inkl. MwSt. zzgl. Versand)';
                priceWrapper.appendChild(price);
                priceWrapper.appendChild(taxInfo);
        }

        createInfoButton(parent) {
                const buttonWrapper = this.createWrapper('div', 'db_m_result_button-wrapper', parent);
                const iButton = this.createWrapper('button', 'db_m_result_button', buttonWrapper);

                const text = document.createElement('span');
                text.classList.add('db_m_result_button-text');
                text.textContent = this.translations['product_card_bttn'];
                iButton.appendChild(text);
        }

        createPagination(totalResults) {
                const totalPages = Math.ceil(totalResults / this.pageSize);
                const pagination = document.createElement('div');
                pagination.classList.add('db_m_result_pagination');

                // Helper function to create a page button
                const createPageButton = (text, onClick) => {
                        const button = document.createElement('button');
                        button.textContent = text;
                        button.classList.add('db_m_result_pagination-button');
                        button.addEventListener('click', onClick);
                        return button;
                };

                // Previous button
                const previous = createPageButton('<', () => {
                        if (this.currentPage > 1) {
                                this.setCurrentPage(this.currentPage - 1, totalPages);
                        }
                });
                pagination.appendChild(previous);

                // Visible page buttons
                const startPage = Math.max(this.currentPage - 1, 1);
                const endPage = Math.min(startPage + 3, totalPages);

                for (let i = startPage; i <= endPage; i++) {
                        const pageButton = createPageButton(i.toString(), () => {
                                // Remove active class from all page buttons
                                document.querySelectorAll('.db_m_result_pagination-button').forEach(button => {
                                        button.classList.remove('db_m_result_pagination-button-active');
                                });
                                // Add active class to the clicked button
                                pageButton.classList.add('db_m_result_pagination-button-active');

                                // update the currentPage
                                this.setCurrentPage(i, totalPages);
                        });

                        // Add the active class if this is the current page
                        if (i === this.currentPage) {
                                pageButton.classList.add('db_m_result_pagination-button-active');
                        }
                        pagination.appendChild(pageButton);
                }

                // Next button
                const next = createPageButton('>', () => {
                        if (this.currentPage < totalPages) {
                                this.setCurrentPage(this.currentPage + 1, totalPages);
                        }
                });
                pagination.appendChild(next);

                return pagination;
        }

        setCurrentPage(page, totalPages) {
                if (page >= 1 && page <= totalPages) {
                        this.currentPage = page;
                        this.handlePaginationButtonClick(page);
                        // Any other logic that needs to happen when the page changes
                }
        }

        async handlePaginationButtonClick(page) {
                console.log(`Page clicked: ${page}`);
                await this.fetchData();
                this.buildResultPage();
        }

}