
// retrieving the different data attributes

// Get the current script element
var script = document.currentScript;

// Access data attributes
var websiteKey = script.dataset.websiteKey;
var relayServer = script.dataset.relayServer;
const products_index = websiteKey + '_products';
const top_searches_index = website<PERSON>ey + '_top_searches';
let filterDefinitions = null;

// controller for the search
let searchController = new AbortController();
const filterTypes = {
    'category': 'CATEGORY',
    'productType': 'PRODUCT_TYPE',
    'manufacturer': 'MANUFACTURER',
    'price': 'PRICE'
}

const translations = {
    en: {
        'default': '<span class="db_d_suggestion_title_icon">&#9733;</span>Top searches',
        'autocomplete': '<span class="db_d_suggestion_title_icon">&#128269;</span>Suggestions',
        'showAllBttn': 'Show all results',
        'filter_category': 'Category',
        'filter_productType': 'Product type',
        'filter_manufacturer': 'Manufacturer',
        'filter_price': 'Price',
        'search_results': 'Search results',
        'no_results': 'No results found',
        'pagination_first': 'First',
        'pagination_prev': 'Previous',
        'pagination_next': 'Next',
        'pagination_last': 'Last',
        'product_card_bttn': 'All Offers',
    },
    de: {
        'default': '<span class="db_d_suggestion_title_icon">&#9733;</span>Top-Suchen',
        'autocomplete': '<span class="db_d_suggestion_title_icon">&#128269;</span>Vorschläge',
        'showAllBttn': 'Alle Ergebnisse anzeigen',
        'filter_category': 'Kategorie',
        'filter_productType': 'Produkttyp',
        'filter_manufacturer': 'Hersteller',
        'filter_price': 'Preis',
        'search_results': 'Suchergebnisse',
        'no_results': 'Keine Ergebnisse gefunden',
        'pagination_first': 'Erste',
        'pagination_prev': 'Vorherige',
        'pagination_next': 'Nächste',
        'pagination_last': 'Letzte',
        'product_card_bttn': 'Alle Angebote',
    }
};

function getUserLanguage() {
    const language = document.documentElement.lang ||
        (document.querySelector('meta[name="language"]') && document.querySelector('meta[name="language"]').getAttribute("content")) ||
        'en';
    return language;
}

function getTranslation() {
    const userLanguage = getUserLanguage();
    return translations[userLanguage] || translations['en'];
}

const base_url = relayServer;

const queryBuilder = (pattern, args) => {
    let body_query;

    if (pattern) {
        const mainShould = [
            // Highest priority: Exact match_phrase with a slop for exact and close full pattern matches
            {
                "match_phrase": {
                    "name": {
                        "query": pattern,
                        "slop": 2,
                        "boost": 200  // High boost for exact phrase matches
                    }
                }
            },
            {
                "match_phrase": {
                    "ordernumber": {
                        "query": pattern,
                        "slop": 2,
                        "boost": 200  // High boost for exact phrase matches
                    }
                }
            },

            // High priority: match_phrase_prefix to catch prefix matches on full pattern
            {
                "match_phrase_prefix": {
                    "name": {
                        "query": pattern,
                        "boost": 150
                    }
                }
            },
            {
                "match_phrase_prefix": {
                    "ordernumber": {
                        "query": pattern,
                        "boost": 150
                    }
                }
            },

        ];

        body_query = {
            "bool": {
                "must": [],
                "filter": [],
                "should": mainShould,
                "minimum_should_match": 1
            }
        };
    } else {
        // Fallback when no pattern is provided
        body_query = {
            "function_score": {
                "query": {
                    "bool": {
                        "must": [{ "match_all": {} }],
                        "filter": []
                    }
                },
                "functions": [
                    {
                        "random_score": {}
                    }
                ],
                "boost_mode": "multiply"
            }
        };
    }

    // Apply additional filters based on args
    if (args) {
        for (const [key, value] of Object.entries(args)) {
            let filter;
            if (key === "cat") {
                filter = { "match_phrase_prefix": { [key]: value.join('_') } };
            } else if (key === "price" && typeof value === "object" && value.min !== undefined && value.max !== undefined) {
                filter = {
                    "range": {
                        [key]: {
                            "gte": value.min,
                            "lte": value.max
                        }
                    }
                };
            } else {
                filter = { "match_phrase_prefix": { [key]: value } };
            }
    
            if (pattern) {
                body_query.bool.filter.push(filter);
            } else {
                body_query.function_score.query.bool.filter.push(filter);
            }
        }
    }
    return body_query;
};

const fetchApi = async (url, body_query, signal, currentPage, pageSize) => {
    const res = await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        },
        body: JSON.stringify({
            "query": body_query,
            "sort": [
                { "name.keyword": { "order": "asc" } }
            ],
            "from": currentPage * pageSize,
            "size": pageSize,
            "aggs": {
                "min_price": { "min": { "field": "price" } },
                "max_price": { "max": { "field": "price" } },
                "categories": { "terms": { "field": "cat.keyword", "size": 1000 } },
                "product_types": { "terms": { "field": "Produkttyp.keyword", "size": 1000 } },
                "manufacturers": { "terms": { "field": "vendor.keyword", "size": 1000 } }
            }
        }),
        signal: signal
    });

    return res;
};

const handleResponse = async (res) => {
    let result = null;

    if (!res) {
        return result;
    }

    if (res.ok) {
        const data = await res.json();
        if (data) {
            const filteredData = data['hits']['hits'].map(item => item['_source']);
            const minPrice = data.aggregations.min_price.value;
            const maxPrice = data.aggregations.max_price.value;
            const categories = data.aggregations.categories.buckets.map(item => item.key);
            const productTypes = data.aggregations.product_types.buckets.map(item => item.key);
            const manufacturers = data.aggregations.manufacturers.buckets.map(item => item.key);

            const filterElements = {
                'minPrice': minPrice,
                'maxPrice': maxPrice,
                'categories': categories,
                'productTypes': productTypes,
                'manufacturers': manufacturers
            }
            result = {
                'data': filteredData,
                'totalProducts': data['hits']['total']['value'],
                'filterElements': filterElements
            }
        } else {
            console.log("No data returned");
        }
    } else {
        console.error("Request failed:", res.status, res.statusText);
    }
    return result;
};

async function apiCall(pattern, currentPage = 0, pageSize = 50, args = null) {
    pattern = pattern.trim();
    const url = `${base_url}/${products_index}/_search`;

    let body_query = queryBuilder(pattern, args);

    // cancel the previous search
    searchController.abort();

    // create a new AbortController
    searchController = new AbortController();

    try {
        const response = await fetchApi(url, body_query, searchController.signal, currentPage, pageSize);
        const result = await handleResponse(response);
        return result;
    } catch (error) {
        if (error.name === 'AbortError') {
            // console.log('Fetch aborted:', error);
        } else {
            console.log('Fetch error:', error);
        }
        return null;
    }
}


const normalizeSearchTerm = (term) => {
    // Normalize the search term: trim, lowercase
    return term.trim().toLowerCase();
};

const updateTopSearches = async (searchTerm) => {
    const normalizedSearchTerm = normalizeSearchTerm(searchTerm);
    const url = `${base_url}/${top_searches_index}/_update/${encodeURIComponent(normalizedSearchTerm)}`;
    const body = {
        "script": {
            "source": "ctx._source.count += 1",
            "lang": "painless"
        },
        "upsert": {
            "search_term": normalizedSearchTerm,
            "count": 1,
            "timestamp": new Date().toISOString()
        }
    };

    try {
        const res = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(body)
        });

        if (res.ok) {
            const response = await res.json();
            // console.log(`Successfully updated or inserted search term: ${normalizedSearchTerm}`, response);
        } else {
            console.error(`Failed to update search term: ${normalizedSearchTerm}, Status: ${res.status}`);
        }
    } catch (error) {
        console.error(`Failed to update or insert search term: ${normalizedSearchTerm}, Error: ${error}`);
    }
};

async function loadInitialProductsForModal(topSearchPatterns) {
    try {
        const query = buildDiversifiedSearchQuery(topSearchPatterns);
        const url = `${base_url}/${products_index}/_search`;
        const response = await postToElasticSearch(url, query);
        const products = [];
        const seenNames = new Set();
        let productCount = 0;

        const buckets = response.aggregations.patterns.buckets;
        const bucketValues = Object.values(buckets);

        // Ensure we gather products in a round-robin fashion across patterns
        while (productCount < 6) {
            let addedProduct = false;
            for (const bucket of bucketValues) {
                if (productCount >= 6) break;
                const hits = bucket.top_hits.hits.hits;
                for (const hit of hits) {
                    if (!seenNames.has(hit._source.name)) {
                        products.push(hit._source);
                        seenNames.add(hit._source.name);
                        productCount++;
                        addedProduct = true;
                        break; // Move to the next pattern to ensure diversity
                    }
                }
            }
            // If no new product was added in a full cycle, break to avoid infinite loop
            if (!addedProduct) break;
        }

        return products;
    } catch (error) {
        console.error("Error loading initial products for modal:", error);
        return [];
    }
}


function buildDiversifiedSearchQuery(patterns) {
    const shouldClauses = patterns.map(pattern => ({
        match_phrase_prefix: { "name": pattern }
    }));

    const filters = patterns.reduce((acc, pattern) => {
        acc[pattern] = { match_phrase_prefix: { "name": pattern } };
        return acc;
    }, {});

    return {
        size: 0, // We don't want any documents in the top-level hits, only in aggregations
        query: {
            bool: {
                should: shouldClauses,
                minimum_should_match: 1
            }
        },
        aggs: {
            patterns: {
                filters: {
                    filters
                },
                aggs: {
                    top_hits: {
                        top_hits: {
                            size: 3 // Return up to 3 documents for each pattern
                        }
                    }
                }
            }
        }
    };
}

const getDiversifiedTopSearches = async (size = 5, prefixSize = 3, topTermPerPrefix = 1) => {
    const url = `${base_url}/${top_searches_index}/_search`;
    const body = createElasticSearchBody(prefixSize, size, topTermPerPrefix);
    try {
        const result = await postToElasticSearch(url, body);
        return processElasticSearchResult(result);
    } catch (error) {
        console.error("Failed to get diversified top searches:", error);
        return null;
    }
};

const createElasticSearchBody = (prefixSize, size, topTermPerPrefix) => ({
    "size": 0,
    "aggs": {
        "prefixes": {
            "terms": {
                "script": {
                    "source": "String searchTerm = doc['search_term'].value; searchTerm = searchTerm.length() > params.prefixSize ? searchTerm.substring(0, params.prefixSize) : searchTerm;",
                    "params": { "prefixSize": prefixSize }
                },
                "size": size
            },
            "aggs": {
                "top_searches": {
                    "top_hits": {
                        "size": topTermPerPrefix,
                        "sort": [{ "count": { "order": "desc" } }]
                    }
                }
            }
        }
    }
});

const postToElasticSearch = async (url, body) => {
    const response = await fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(body)
    });
    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
};

const processElasticSearchResult = (result) => {
    const buckets = result.aggregations.prefixes.buckets;
    return buckets.flatMap(bucket => {
        return bucket.top_searches.hits.hits.map(hit => ({
            prefix: bucket.key,
            search_term: hit._source.search_term,
            count: hit._source.count,
            timestamp: hit._source.timestamp
        }));
    });
};


function parseAttributes(attribute) {
    attribute = attribute.replace(/\+/g, ' ');
    const regex = /&(?=\w+=)/;
    const dataObject = {};
    const pairs = attribute.split(regex);
    pairs.forEach((pair) => {
        const [key, value] = pair.split('=');
        dataObject[decodeURIComponent(key)] = decodeURIComponent(value);
    })

    return dataObject
}

function createCheckbox(option, value, name, callback, isChecked = false) {
    let checkboxContainer = document.createElement('div');
    checkboxContainer.className = 'db_d_result_checkbox-container';

    let label = document.createElement('label');

    let checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.name = name;
    checkbox.value = value;
    checkbox.id = `${name}-${value}`;
    checkbox.checked = isChecked; // Set the checked state

    label.htmlFor = checkbox.id;
    label.appendChild(document.createTextNode(' ' + option));

    checkboxContainer.appendChild(checkbox);
    checkboxContainer.appendChild(label);

    checkbox.addEventListener('change', callback)

    return checkboxContainer;
}


function createFilterButton(filterName, filter_type = '') {
    let filterButton = document.createElement('button');
    filterButton.className = 'db_d_result_filter-button';
    filterButton.id = filter_type.toLowerCase();
    filterButton.innerHTML = filterName + ' <span class="db_d_result_caret"></span>';

    filterButton.addEventListener('click', function () {
        let filterContent = this.nextElementSibling;
        filterContent.classList.toggle('db_d_result_show');
        let caret = this.querySelector('.db_d_result_caret');
        caret.classList.toggle('db_d_result_up');
    });

    return filterButton;
}

function createFilterContent(options, name, callback, filter_type = '') {
    let filterContent = document.createElement('div');
    filterContent.className = 'db_d_result_filter-content';

    if (filter_type === 'price') {
        let priceContainer = createPriceFilter(options[0], options[1], options[2]);
        filterContent.appendChild(priceContainer);
        return filterContent;
    }

    options.forEach((option) => {
        let checkboxContainer = createCheckbox(option.text, option.value, name, callback);
        filterContent.appendChild(checkboxContainer);
    });

    return filterContent;
}

function createFilterItem(filterName, options, callback = () => { }, additionalFilters = null, filter_type = null) {

    
    if (!Array.isArray(options)) {
        throw new Error('Options must be an array');
    }
    
    let name = filterName.toLowerCase().replace(/ /g, '-');

    const filterItem = createFilterItemDiv();
    const filterButton = createFilterButton(filterName, filter_type);
    filterItem.appendChild(filterButton);
    
    let filterContent = createFilterContent(options, name, callback, filter_type.toLowerCase());

    handleAdditionalFilters(additionalFilters, filter_type, filterContent, callback);

    filterItem.appendChild(filterContent);

    return filterItem;
}


function createFilterItemDiv() {
    let filterItem = document.createElement('div');
    filterItem.className = 'db_d_result_filter-item';
    return filterItem;
}

/**
 * Handles additional filters based on the filter type.
 *
 * @param {Object} additionalFilters - The additional filters to be applied.
 * @param {String} filter_type - The type of filter. Possible values are 'category', 'productType', 'manufacturer'.
 * @param {HTMLElement} filterContent - The HTML element that represents the filter content.
 * @param {Function} callback - The callback function to be called when the filters are handled.
 * @returns {undefined}
 */
function handleAdditionalFilters(additionalFilters, filter_type, filterContent, callback) {
    if (additionalFilters && typeof additionalFilters === 'object' && filter_type) {
        // Move the logic for handling each filter type into its own function
        if (additionalFilters.hasOwnProperty('cat') && additionalFilters['cat'].length > 0 && filter_type === filterTypes.category) {
            handleCategoryFilter(additionalFilters['cat'], filterContent, callback);
        }
        else if (additionalFilters.hasOwnProperty('Produkttyp') && additionalFilters['Produkttyp'] && filter_type === filterTypes.productType) {
            handleProductTypeFilter(additionalFilters['Produkttyp'], filterContent, callback);
        }
        else if (additionalFilters.hasOwnProperty('vendor') && additionalFilters['vendor'] && filter_type === filterTypes.manufacturer) {
            handleVendorFilter(additionalFilters['vendor'], filterContent, callback);
        }
    }
}

/**
 * Handles the category filter by creating a checkbox for each category,
 * adding a class to each filter content element, and inserting the checkboxes
 * at the beginning of the filter content.
 *
 * @param {Array} categories - An array of categories.
 * @param {HTMLElement} filterContent - The filter content HTMLElement.
 * @param {Function} callback - The callback function.
 * @returns {undefined}
 */
function handleCategoryFilter(categories, filterContent, callback) {
    let category = categories.join(' => ');
    var checkedCheckbox = createCheckbox(category, category, category, callback, true);
    Array.from(filterContent.children).forEach((element) => {
        element.classList.add('db_d_result_nested');
    })
    filterContent.insertBefore(checkedCheckbox, filterContent.firstChild);
}

/**
 * Handles the product type filter by creating a checkbox with the given product type,
 * appending it to the filter content, and removing any existing child elements from 
 * the filter content.
 *
 * @param {String} productType - The product type to be used for creating the checkbox.
 * @param {HTMLElement} filterContent - The element that represents the filter content.
 * @param {Function} callback - The callback function to be called when the checkbox is clicked.
 * @returns {undefined}
 */
function handleProductTypeFilter(productType, filterContent, callback) {
    var checkedCheckbox = createCheckbox(productType, productType, productType, callback, true);
    while (filterContent.firstChild) {
        filterContent.removeChild(filterContent.firstChild);
    }
    filterContent.appendChild(checkedCheckbox);
}

/**
 * Handles the vendor filter.
 *
 * @param {any} vendor - The vendor to filter.
 * @param {HTMLElement} filterContent - The element where the filter content will be displayed.
 * @param {Function} callback - The callback function to be executed.
 * @return {undefined}
 */
function handleVendorFilter(vendor, filterContent, callback) {
    var checkedCheckbox = createCheckbox(vendor, vendor, vendor, callback, true);
    while (filterContent.firstChild) {
        filterContent.removeChild(filterContent.firstChild);
    }
    filterContent.appendChild(checkedCheckbox);
}

function createResponsiveModal() {
    // Create the modal and overlay
    const modal = document.createElement('div');
    modal.className = 'db_mobile_modal db_hidden';

    const overlay = document.createElement('div');
    overlay.className = 'db_overlay db_hidden';
    document.body.appendChild(overlay);

    // Create the modal content
    const modalContent = document.createElement('div');
    modalContent.className = 'db_mobile_modal-content';

    modal.appendChild(modalContent);

    return modal
}

(function () {
    let url = `${base_url}/filters/get?website_key=${websiteKey}`;
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw response
            }
            return response.json()
        })
        .then(data => {
            filterDefinitions = data
        })
        .catch(error => {
            console.log(error)
        })
})()

function get_filters_items() {
    return filterDefinitions
}

function get_price_range(search_result) {
    let min_price = 100000, max_price = 0;
    search_result.forEach(item => {
        min_price = Math.min(min_price, item.price);
        max_price = Math.max(max_price, item.price);
    });
    return {min_price, max_price};
}


/************************Filter functions and classes **************************/


function createPriceFilter(superMin = 1, superMax = 1000, stepValue = 0.01) {
    const priceFilter = document.createElement('div');
    priceFilter.className = 'db_price-filter';

    const sliderContainer = document.createElement('div');
    sliderContainer.className = 'db_slider-container';

    const slider = document.createElement('div');
    slider.id = 'db_slider';

    slider.dataset.superMin = superMin;
    slider.dataset.superMax = superMax;
    slider.dataset.min = superMin;
    slider.dataset.max = superMax;
    slider.dataset.step = stepValue;

    const track = document.createElement('div');
    track.id = 'db_track';

    const range = document.createElement('div');
    range.id = 'db_range';

    const minHandle = document.createElement('div');
    minHandle.id = 'db_min-handle';
    minHandle.className = 'db_handle';

    const maxHandle = document.createElement('div');
    maxHandle.id = 'db_max-handle';
    maxHandle.className = 'db_handle';

    slider.appendChild(track);
    slider.appendChild(range);
    slider.appendChild(minHandle);
    slider.appendChild(maxHandle);
    sliderContainer.appendChild(slider);
    priceFilter.appendChild(sliderContainer);

    const priceInputs = document.createElement('div');
    priceInputs.className = 'db_price-inputs';

    const inputContainer1 = document.createElement('div');
    inputContainer1.className = 'db_input-container';
    const currencySymbol1 = document.createElement('span');
    currencySymbol1.className = 'db_currency-symbol';
    currencySymbol1.textContent = '€';
    const minPriceInput = document.createElement('input');
    minPriceInput.type = 'text';
    minPriceInput.id = 'db_min-price-input';
    minPriceInput.className = 'db_price-input';
    minPriceInput.value = superMin.toFixed(2);
    inputContainer1.appendChild(currencySymbol1);
    inputContainer1.appendChild(minPriceInput);

    const toText = document.createElement('span');
    toText.textContent = 'to';

    const inputContainer2 = document.createElement('div');
    inputContainer2.className = 'db_input-container';
    const currencySymbol2 = document.createElement('span');
    currencySymbol2.className = 'db_currency-symbol';
    currencySymbol2.textContent = '€';
    const maxPriceInput = document.createElement('input');
    maxPriceInput.type = 'text';
    maxPriceInput.id = 'db_max-price-input';
    maxPriceInput.className = 'db_price-input';
    maxPriceInput.value = superMax.toFixed(2);
    inputContainer2.appendChild(currencySymbol2);
    inputContainer2.appendChild(maxPriceInput);

    priceInputs.appendChild(inputContainer1);
    priceInputs.appendChild(toText);
    priceInputs.appendChild(inputContainer2);
    priceFilter.appendChild(priceInputs);

    const filterButtons = document.createElement('div');
    filterButtons.className = 'db_filter-buttons';

    const clearButton = document.createElement('button');
    clearButton.id = 'db_clear-button';
    clearButton.className = 'db_btn';
    clearButton.textContent = 'Clear';

    const applyButton = document.createElement('button');
    applyButton.id = 'db_apply-button';
    applyButton.className = 'db_btn';
    applyButton.textContent = 'Apply';

    filterButtons.appendChild(clearButton);
    filterButtons.appendChild(applyButton);
    priceFilter.appendChild(filterButtons);

    return priceFilter;
}

class RangeFilter {
    constructor(options) {
        this.options = options;
        this.slider = document.getElementById(options.sliderId);
        this.minHandle = document.getElementById(options.minHandleId);
        this.maxHandle = document.getElementById(options.maxHandleId);
        this.range = document.getElementById(options.rangeId);
        this.minInput = document.getElementById(options.minInputId);
        this.maxInput = document.getElementById(options.maxInputId);
        this.clearButton = document.getElementById(options.clearButtonId);
        this.applyButton = document.getElementById(options.applyButtonId);

        // Initialize superMin and superMax
        this.superMin = parseFloat(this.slider.dataset.superMin);
        this.superMax = parseFloat(this.slider.dataset.superMax);

        // Initialize minValue and maxValue within the super range
        this.minValue = parseFloat(this.slider.dataset.min);
        this.maxValue = parseFloat(this.slider.dataset.max);
        this.formatValue = options.formatValue || (value => value.toFixed(2));
        this.onChange = options.onChange || (() => { });
        this.onApply = options.onApply || (() => { });
        this.onClear = options.onClear || (() => { });

        this.isDragging = false;
        this.clickPrevented = false;

        this.init();
    }

    init() {
        this.addEventListeners();
        this.updateSliderFromValues();
        this.updateInputs();
    }

    updateSuperRange(newSuperMin, newSuperMax) {
        // Update the superMin and superMax with the new values
        this.superMin = newSuperMin;
        this.superMax = newSuperMax;
    
        // Ensure that the current minValue and maxValue are within the updated super range
        this.minValue = Math.max(this.minValue, this.superMin);
        this.maxValue = Math.min(this.maxValue, this.superMax);
    
        // Update the data attributes of the slider for consistency
        this.slider.dataset.superMin = this.superMin;
        this.slider.dataset.superMax = this.superMax;
    
        // Update the slider UI to reflect the new super range
        this.updateSliderFromValues();
    
        // Update the input boxes to reflect the potentially adjusted minValue and maxValue
        this.updateInputs();
    }

    setInitialValues(minValue, maxValue) {
        this.minValue = minValue;
        this.maxValue = maxValue;
        this.updateSliderFromValues();
        this.updateInputs();
    }

    getSliderWidth() {
        return this.slider.offsetWidth;
    }

    updateSliderFromValues() {
        const rangeMax = this.superMax - this.superMin;
        const minPercent = ((this.minValue - this.superMin) / rangeMax) * 100;
        const maxPercent = ((this.maxValue - this.superMin) / rangeMax) * 100;
        this.minHandle.style.left = `calc(${minPercent}% - 10px)`;
        this.maxHandle.style.left = `calc(${maxPercent}% - 10px)`;
        this.range.style.left = `${minPercent}%`;
        this.range.style.width = `${maxPercent - minPercent}%`;
    }

    updateInputs() {
        this.minInput.value = this.formatValue(this.minValue);
        this.maxInput.value = this.formatValue(this.maxValue);
    }

    validateAndSetValues() {
        let minInputVal = parseFloat(this.minInput.value);
        let maxInputVal = parseFloat(this.maxInput.value);

        if (isNaN(minInputVal) || minInputVal < this.superMin) {
            minInputVal = this.superMin;
        }

        if (isNaN(maxInputVal) || maxInputVal > this.superMax) {
            maxInputVal = this.superMax;
        }

        if (minInputVal >= maxInputVal) {
            minInputVal = maxInputVal - (this.options.step || 0.01);
        }

        this.minValue = minInputVal;
        this.maxValue = maxInputVal;

        this.updateSliderFromValues();
        this.updateInputs();
        this.onChange(this.minValue, this.maxValue);
    }

    handleDrag(event, handle) {
        const rect = this.slider.getBoundingClientRect();
        const clientX = event.touches ? event.touches[0].clientX : event.clientX;
        let newValue = this.superMin + (((clientX - rect.left) / this.getSliderWidth()) * (this.superMax - this.superMin));
        newValue = Math.max(this.superMin, Math.min(this.superMax, newValue));

        if (handle === this.minHandle) {
            this.minValue = Math.min(newValue, this.maxValue - (this.options.step || 0.01));
        } else {
            this.maxValue = Math.max(newValue, this.minValue + (this.options.step || 0.01));
        }

        this.updateSliderFromValues();
        this.updateInputs();
    }

    handleSliderClick(event) {
        if (this.isDragging || this.clickPrevented) return;
        const rect = this.slider.getBoundingClientRect();
        const clientX = event.clientX;
        const newValue = this.superMin + (((clientX - rect.left) / this.getSliderWidth()) * (this.superMax - this.superMin));
        const midValue = (this.maxValue + this.minValue) / 2;

        if (newValue < midValue) {
            this.minValue = Math.min(newValue, this.maxValue - (this.options.step || 0.01));
        } else {
            this.maxValue = Math.max(newValue, this.minValue + (this.options.step || 0.01));
        }

        this.updateSliderFromValues();
        this.updateInputs();
        this.onChange(this.minValue, this.maxValue);
    }

    startDrag(handle) {
        return (event) => {
            event.preventDefault();
            if (event.target !== handle) return;
            this.isDragging = true;
            this.clickPrevented = true;
            const moveHandler = (e) => this.handleDrag(e, handle);
            const stopHandler = () => {
                this.isDragging = false;
                document.removeEventListener('mousemove', moveHandler);
                document.removeEventListener('mouseup', stopHandler);
                document.removeEventListener('touchmove', moveHandler);
                document.removeEventListener('touchend', stopHandler);

                this.onChange(this.minValue, this.maxValue);

                setTimeout(() => {
                    this.clickPrevented = false;
                }, 100);
            };

            document.addEventListener('mousemove', moveHandler);
            document.addEventListener('mouseup', stopHandler);
            document.addEventListener('touchmove', moveHandler);
            document.addEventListener('touchend', stopHandler);
        };
    }

    addEventListeners() {
        this.minHandle.addEventListener('mousedown', this.startDrag(this.minHandle));
        this.maxHandle.addEventListener('mousedown', this.startDrag(this.maxHandle));
        this.minHandle.addEventListener('touchstart', this.startDrag(this.minHandle));
        this.maxHandle.addEventListener('touchstart', this.startDrag(this.maxHandle));

        this.minInput.addEventListener('blur', () => this.validateAndSetValues());
        this.maxInput.addEventListener('blur', () => this.validateAndSetValues());

        this.slider.addEventListener('click', (event) => this.handleSliderClick(event));

        this.clearButton.addEventListener('click', () => {
            this.minValue = this.superMin;
            this.maxValue = this.superMax;
            this.updateSliderFromValues();
            this.updateInputs();
            this.onClear();
        });

        this.applyButton.addEventListener('click', () => {
            this.onApply(this.minValue, this.maxValue);
        });

        window.addEventListener('resize', this.debounce(() => {
            this.updateSliderFromValues();
        }, 250));
    }

    debounce(func, wait) {
        let timeout;
        return (...args) => {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), wait);
        };
    }

    // Method to update the superMin and superMax, adjusting minValue and maxValue accordingly
    updateSuperRange(newSuperMin, newSuperMax) {
        this.superMin = newSuperMin;
        this.superMax = newSuperMax;

        // Ensure that the current min and max values are within the new super range
        this.minValue = Math.max(this.minValue, this.superMin);
        this.maxValue = Math.min(this.maxValue, this.superMax);

        // Update the slider and input fields to reflect the new super range
        this.updateSliderFromValues();
        this.updateInputs();
    }
}


function createLoadingContainer() {
    // Create loading indicator container
    const loadingIndicator = document.createElement('div');
    loadingIndicator.id = 'db_loading-indicator';
    loadingIndicator.className = 'db_loading db_spinner_hidden';

    // Create spinner
    const spinner = document.createElement('div');
    spinner.className = 'db_spinner';

    // Append spinner to loading indicator
    loadingIndicator.appendChild(spinner);

    return loadingIndicator;
}

// Function to show the loading indicator
function showLoadingIndicator() {
    document.getElementById('db_loading-indicator').classList.remove('db_spinner_hidden');
}

// Function to hide the loading indicator
function hideLoadingIndicator() {
    document.getElementById('db_loading-indicator').classList.add('db_spinner_hidden');
}


class MobileFilterModal {
    constructor({ headerText = '', generateBodyContent, footerButtons = [] }) {
        this.headerText = headerText;
        this.generateBodyContent = generateBodyContent;
        this.footerButtons = footerButtons;
        this.modal = null;
        this.backdrop = null;
        this.createModal();
        this.attachEvents();
    }

    createModal() {
        // Create backdrop
        this.backdrop = document.createElement('div');
        this.backdrop.className = 'db_m_filter_backdrop';

        // Create modal
        this.modal = document.createElement('div');
        this.modal.className = 'db_m_filter_modal';

        // Create modal content
        const modalContent = document.createElement('div');
        modalContent.className = 'db_m_filter_modal-content';

        // Create modal header
        const modalHeader = document.createElement('div');
        modalHeader.className = 'db_m_filter_modal-header';
        modalHeader.innerHTML = `<h2>${this.headerText}</h2><span class="db_m_filter_close-btn">&times;</span>`;

        // Create modal body
        const modalBody = document.createElement('div');
        modalBody.className = 'db_m_filter_modal-body';
        const content = typeof this.generateBodyContent === 'function' ? this.generateBodyContent() : this.generateBodyContent;
        if(typeof content === 'string') {
            modalBody.innerHTML = content;
        }
        else if(Array.isArray(content)) {
            content.forEach(item => modalBody.appendChild(item));
        }
        else if(content instanceof Element){
            modalBody.appendChild(content);
        }

        // Create modal footer
        const modalFooter = document.createElement('div');
        modalFooter.className = 'db_m_filter_modal-footer';
        modalFooter.innerHTML = this.footerButtons.map(btn => `<button id="db_m_filter_${btn.id}" class="db_m_filter_footer-btn">${btn.text}</button>`).join('');

        // Append sections to modal content
        modalContent.append(modalHeader, modalBody, modalFooter);
        this.modal.appendChild(modalContent);

        // Append modal and backdrop to the document body
        document.body.append(this.backdrop, this.modal);
    }

    removeModal() {
        document.body.removeChild(this.backdrop);
        document.body.removeChild(this.modal);
    }

    attachEvents() {
        const closeModal = () => {
            this.modal.classList.add('db_m_filter_slide-out');
            setTimeout(() => {
                this.backdrop.style.display = 'none';
                this.modal.style.display = 'none';
                this.modal.classList.remove('db_m_filter_slide-out');
            }, 400);
        };

        // Close modal via close button
        this.modal.querySelector('.db_m_filter_close-btn').addEventListener('click', closeModal);

        // Close modal by clicking outside (on the backdrop)
        this.backdrop.addEventListener('click', closeModal);

        // Attach events to footer buttons
        this.footerButtons.forEach(btn => {
            const buttonElement = document.getElementById(`db_m_filter_${btn.id}`);
            buttonElement.addEventListener('click', () => {
                if (btn.onClick) btn.onClick();
                if (btn.closeOnAction) closeModal();
            });
        });
    }

    open() {
        this.backdrop.style.display = 'block';
        this.modal.style.display = 'block';
        this.modal.classList.remove('db_m_filter_slide-out');
    }

    close() {
        this.modal.classList.add('db_m_filter_slide-out');
        setTimeout(() => {
            this.backdrop.style.display = 'none';
            this.modal.style.display = 'none';
            this.modal.classList.remove('db_m_filter_slide-out');
        }, 400);
    }
}

class FilterBar {
    constructor(filterOptions, filterTitle = 'Filter') {
        this.filterOptions = filterOptions;
        this.filterTitle = filterTitle;
        this.createFilterBar();
        this.setupFilterTabs();
    }

    createFilterBar() {
        // Create the main container
        this.filterBar = document.createElement('div');
        this.filterBar.classList.add('db_m_filter_navbar');

        // Create the filter button
        this.filterButton = document.createElement('div');
        this.filterButton.classList.add('db_m_filter-btn');
        this.filterButton.innerHTML = `<span class="db_m_filter-icon"></span> ${this.filterTitle}`;
        this.filterBar.appendChild(this.filterButton);

        // Create the filter tabs container
        this.filterTabs = document.createElement('div');
        this.filterTabs.classList.add('db_m_filter-tabs');
        this.filterBar.appendChild(this.filterTabs);
    }

    getFilterBar() {
        return this.filterBar;
    }

    setupFilterTabs() {
        // Create filter tabs based on filterOptions
        for (const option of this.filterOptions) {
            const tab = document.createElement('span');
            tab.classList.add('db_m_filter-tab');
            tab.dataset.filter = option.type;
            tab.textContent = option.label;
            tab.addEventListener('click', () => this.handleTabClick(tab, option.onclick));

            this.filterTabs.appendChild(tab);
        }
    }

    handleTabClick(clickedTab, onClick) {
        // Remove active class from all tabs
        const activeTab = this.filterTabs.querySelector('.active');
        if (activeTab) {
            activeTab.classList.remove('active');
        }

        // Add active class to the clicked tab
        clickedTab.classList.add('active');

        // Trigger your filtering logic here based on clickedTab.dataset.filter
        onClick();
    }
}


/***************************************************************** */