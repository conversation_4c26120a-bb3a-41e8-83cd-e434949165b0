@media (min-width: 993px) {
    .db_d_modal {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        width: 70%;
        max-width: 1200px;
        /* Set a maximum height */
        border: 1px solid #ccc;
        background-color: #fff;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2), 0 6px 20px rgba(0, 0, 0, 0.19); /* Simulate elevation */
        border-radius: 5px;
        box-sizing: border-box;
    }
  
    .db_d_modal-content {
        display: flex;
        justify-content: space-between;
    }
  
    .db_d_suggestions-container {
        flex: 2;
        display: flex;
        flex-direction: column;
        margin-left: 10px;
        margin-right: 10px;
    }
  
    .db_d_suggestions-title {
        font-size: 16px;
        line-height: 43px;
        margin: 0;
        text-align: left;
        width: 100%;
        font-weight: bolder;
    }
    .db_d_suggestion_title_icon {
        color: red;
        font-size: 20px;
        margin-right: .5rem;
    }
  
    .db_d_products-container {
        flex: 10;
        margin-bottom: 20px;
    }
  
    .db_d_suggestions-title {
        color: #555;
        margin: 0 auto;
    }
  
    .db_d_suggestions-container:last-child,
    .db_d_products-container:last-child {
        margin-bottom: 0;
    }
  
    .db_d_suggestion,
    .db_d_product {
        padding: 10px;
        border-bottom: 1px solid #eee;
    }
  
    .db_d_suggestion:last-child,
    .db_d_product:last-child {
        border-bottom: none;
    }
  
    .db_d_products {
        min-height: 300px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        transition: box-shadow .2s ease-in;
        padding: 10px;
    }
  
    .db_d_overlay {
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        background-color: rgba(0, 0, 0, 0.5);
    }
  
    .db_d_overlay {
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
    }
  
    .db_d_hidden {
        display: none;
    }
  
    .db_d_title-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        background-color: #EEE;
        border-bottom: 1px solid #eee;
    }
  
    .db_d_title-container h2 {
        margin: 0 auto;
        font-weight: bolder;
    }
  
    .db_d_close-button {
      position: relative;
      border: none;
      cursor: pointer;
      width: 24px;
      height: 24px;
      background: #3d3e3e;
      border-radius: 50%;
    }
    
    .db_d_close-button:before, .db_d_close-button:after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      width: 14px; /* Adjust width to create a balanced cross */
      height: 2px;
      background-color: #fff;
      transform: translate(-50%, -50%) rotate(45deg);
    }

    .db_d_close-button:after {
      transform: translate(-50%, -50%) rotate(-45deg);
    }
  
    .db_d_show-all-button-container {
        background-color: #EEE;
        cursor: pointer;
        display: flex;
        padding-left: 10px;
        padding-right: 10px;
        transition: box-shadow 0.2s ease-in;
        box-shadow: inset 0 0 2px 0 #6f6f6f;
    }
  
    .db_d_show-all-button-container h3 {
        font-size: 16px;
        line-height: 43px;
        margin: 0;
        font-weight: bolder;
    }
  
  
    .db_d_product-card {
      flex: 1 1 calc(30% - 20px);
      padding: 10px;
      margin-bottom: 20px;
    }

    .db_d_product-wrapper {
        display: flex;
        flex-direction: column;
        width: 100%;
        border: 1px solid #EEE;
        height: 100%;
        transition: box-shadow 0.2s, transform 0.3s;
        transition-delay: .2s;
        box-shadow: 0px 0px 0px rgba(0, 0, 0, 0);
        border-radius: 8px;
    }
  
    .db_d_product-wrapper:hover {
        box-shadow: 0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12);
    }
  
    .db_d_product-card img {
      display: block;
      width: auto;
      max-width: 100%;
      height: 150px;
      padding: 10px;
      margin: 0 auto;
      object-fit: contain;
      transition: transform 0.3s;
    }

    .db_d_product-wrapper:hover img {
      transform: scale(1.05);
    }
  
    .db_d_product-title {
      margin: 10px 0;
      padding: 5px;
      font-size: 16px;
      text-align: center;
      font-weight: 600;
      word-wrap: break-word;
      line-height: normal;
      color: #3b3b3b;
    }
  
    hr {
        border: solid;
        border-width: thin 0 0 0;
        border-color: #ccc;
        display: block;
        flex: 1 1 0px;
        height: 0;
        max-height: 0;
        max-width: 100%;
    }
  
    .db_d_product-price-wrapper {
        display: flex;
        justify-content: flex-end;
        padding-right: 10px;
    }
  
    .db_d_product-price {
        margin: 0;
        padding: 0 10px 5px 0;
        font-size: 24px;
        text-align: right;
        color: #0171e2;
        font-weight: 700;
    }
  
    .db_d_order-number {
        font-size: smaller;
        color: #888;
    }
  
    .db_d_input_autocomplete::selection {
        background: transparent;
        color: #e3403e;
    }
  
  
    /* suggestions */
    .db_d_suggestions-list-wrapper ul {
        list-style-type: none;
        padding: 0;
        width: 100%;
    }
  
    .db_d_suggestions-list-wrapper ul li {
        display: block;
        font-size: 12px;
        color: #3b3b3b;
        cursor: pointer;
    }
  
    .db_d_suggestions-list-wrapper ul li .input {
        color: #e5423d;
        font-weight: 700;
    }
  
    .db_d_suggestions-list-wrapper ul li .suffix {
        font-weight: normal;
    }
  
    /* result page */
    .db-all-main-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
      }
      
      .db-all-product-container {
        width: 25%;
      }
      
      .db_md-whiteframe-12dp:hover {
        box-shadow:
          0px 7px 8px -4px rgba(0, 0, 0, 0.2),
          0px 12px 17px 2px rgba(0, 0, 0, 0.14),
          0px 5px 22px 4px rgba(0, 0, 0, 0.12);
      }
      
      .db-all-product-img-wrapper {
        width: 100%;
        height: auto;
        max-height: 300px;
      }
      
      .db-all-product-img-wrapper img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
      
      
      /* result page style */
      .db_d_result_container-wrapper {
        display: flex;
        width: 100%;
      }
      
      .db_d_result_container {
        display: flex;
        flex-wrap: wrap;
        /* justify-content: space-around; */
        padding: 20px;
        background-color: #f5f5f5;
        width: 70%;
      }
      
      .db_d_result_product-card-wrapper {
        width: 20%;
        /* margin: 20px; */
        box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.15);
        border-radius: 10px;
        overflow: hidden;
        transition: all 0.3s ease-in-out;
        display: flex;
      }
      
      .db_d_result_product-card-wrapper:hover {
        transform: scale(1.05);
        box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.2);
      }
      
      .db_d_result_product-card {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        text-decoration: none;
        width: 100%;
      }
      
      .db_d_result_product-img-wrapper {
        width: 100%;
        height: 200px;
        overflow: hidden;
        background: white;
      }
      
      .db_d_result_product-img-wrapper img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
      
      .db_d_result_product-card-content {
        padding: 15px;
      }
      
      .db_d_result_product-title {
        margin: 0;
        color: #3b3b3b;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        font-size: 14px;
      }
      
      .db_d_result_product-title:hover {
        color: #E3403F;
      }
      
      .db_d_result_tax-info{
        color: #89898b;
        font-size: 12px;
        position: absolute;
        top: 70%;
        left: 0;
      }
      
      .db_d_result_product-card-wrapper:hover .db_d_result_product-title {
        -webkit-line-clamp: unset;
        overflow: unset;
      }
      
      .db_d_result_product-devider {
        margin: 10px 0;
      }
      
      .db_d_result_order-number-wrapper {
        display: flex;
        justify-content: space-between;
        font-size: 0.8em;
        color: #999;
        /* disabled color */
      }
      
      .db_d_result_product-price-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: auto;
        position: relative;
      }
      
      .db_d_result_product-price {
        color: #0171e2;
        font-weight: 700;
        font-size: 24px;
      }
      
      /* pagination style */
      .db_d_result_pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        padding: 20px 0;
      }
      
      .db_d_result_pagination-button {
        margin: 0 5px;
        padding: 10px 20px;
        border: none;
        background-color: #f5f5f5;
        cursor: pointer;
        border-radius: 10px;
      }
      
      .db_d_result_pagination-button:hover {
        background-color: #ddd;
      }
      
      .db_d_result_pagination-button-active {
        background-color: #bbb;
      }
      
      .db_d_result_button-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 10px;
      }
      
      .db_d_result_button {
        background-color: #0170E0;
        border: none;
        color: white;
        text-align: center;
        text-decoration: none;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 14px;
        margin: 4px 2px;
        cursor: pointer;
        transition-duration: 0.4s;
        width: 100%;
        border-radius: 3px;
        padding: 0;
      }
      
      .db_d_result_button:hover {
        background-color: #378AF3;
      }
      
      .db_d_result_prepend-icon {
        margin-right: 8px;
      }
      
      .db_d_result_icon-wrapper {
        background-color: red;
        color: white;
        border-top-right-radius: 40%;
        border-bottom-right-radius: 40%;
        padding: 9px;
      }
      
      .db_d_result_button:hover .db_d_result_icon-wrapper {
        background-color: #0170E0;
      }
      
      .db_d_result_button-text {
        flex-grow: 1;
      }
      
      /* filter section style */
      .db_d_result_filter-container {
        position: relative;
        width: 30%;
      }
      
      .db_d_result_filter {
        width: 300px;
        /* Adjust as needed */
        border: 1px solid #ccc;
        border-radius: 5px;
      }
      
      .db_d_result_filter-item {
        border-bottom: 1px solid #ccc;
      }
      
      .db_d_result_filter-item:last-child {
        border-bottom: none;
      }
      
      .db_d_result_filter-button {
        background-color: #f8f9fa;
        color: #3c4043;
        border: none;
        cursor: pointer;
        outline: none;
        padding: 12px 16px;
        width: 100%;
        text-align: left;
        font-size: 16px;
      }
      
      .db_d_result_filter-content {
        display: none;
        padding: 12px 16px;
      }
      
      .db_d_result_filter-content label {
        font-size: 12px;
        cursor: pointer;
      }
      
      .db_d_result_filter-content.db_d_result_show {
        display: block;
      }
      
      .db_d_result_caret {
        float: right;
        margin-top: 5px;
      }
      
      .db_d_result_caret::before {
        content: '▼';
      }
      
      .db_d_result_caret.db_d_result_up::before {
        content: '▲';
      }
      
      .db_d_result_checkbox-container {
        margin-bottom: 10px;
      }
      
      .db_d_result_filter-content input[type="checkbox"] {
        margin-right: 5px;
        cursor: pointer;
      }
      
      .db_d_result_filter-content {
        display: none;
        padding: 12px 16px;
        animation: revealContent 0.5s ease-in-out;
        max-height: 400px;
        overflow-y: auto;
      }
      
      .db_d_result_filter-content.db_d_result_show {
        display: block;
      }
      
      @keyframes revealContent {
        0% {
          opacity: 0;
          transform: translateY(-10px);
        }
      
        100% {
          opacity: 1;
          transform: translateY(0);
        }
      }
      
      /* switch checkbox style */
      .db_d_result_nested {
        margin-left: 30px;
      }
  }