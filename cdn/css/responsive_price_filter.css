.db_price-filter {
    /* width: 320px; */
    padding: 20px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.db_price-filter h2 {
    margin-bottom: 20px;
    color: #333;
}

.db_slider-container {
    position: relative;
    height: 40px;
    margin: 20px 0;
}

#db_slider {
    position: relative;
    height: 10px;
    background: #ddd;
    border-radius: 5px;
}

#db_track {
    position: absolute;
    height: 100%;
    width: 100%;
    background: #ddd;
    border-radius: 5px;
}

#db_range {
    position: absolute;
    height: 100%;
    background: #3b5998;
    border-radius: 5px;
}

.db_handle {
    position: absolute;
    width: 20px;
    height: 20px;
    background: #fff;
    border: 2px solid #3b5998;
    border-radius: 50%;
    cursor: pointer;
    top: -5px;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out, background-color 0.2s ease-in-out;
}

.db_handle:hover {
    transform: scale(1.2);
    background-color: #3b5998;
}

.db_handle:active {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.db_price-inputs {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
}

.db_input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.db_currency-symbol {
    position: absolute;
    left: 16px;
    font-size: 16px;
    color: #333;
}

.db_price-input {
    width: 95px;
    padding: 5px 5px 5px 20px;
    margin: 0 10px;
    border: 2px solid #ddd;
    border-radius: 5px;
    text-align: center;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.db_price-input:focus {
    border-color: #3b5998;
    outline: none;
}

.db_price-inputs span {
    font-size: 16px;
    color: #333;
}

.db_filter-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.db_btn {
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
}

#db_clear-button {
    background-color: #ffcccc;
}

#db_clear-button:hover {
    background-color: #ff9999;
}

#db_apply-button {
    background-color: #ccffcc;
}

#db_apply-button:hover {
    background-color: #99ff99;
}


/********************Loading spinner******************/
/* Loading indicator covers the entire parent */
#db_loading-indicator {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent overlay */
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000; /* Ensure it's on top of other elements */
}

.db_spinner_hidden {
    display: none !important;
}

/* Spinner styles */
.db_spinner {
    border: 16px solid #171818; 
    border-top: 16px solid #FF0000; /* Red color to match the website's color scheme */
    border-radius: 50%;
    width: 120px; /* Increased size for better visibility */
    height: 120px; /* Increased size for better visibility */
    animation: spin 2s linear infinite; /* Slower spin speed for a more relaxed feel */
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
/********************End of loading spinner***********/

