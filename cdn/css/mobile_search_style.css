/* Responsive design for mobile screens */
.db_m_result_product-card-wrapper {
    width: 100%;
}

.db_m_result_product-card {
    border: 1px solid lightgray;
    margin: 2px;
}

.db_m_result_product-cards_wrapper{
    padding: 5px;
}

@media (min-width: 375px) {
    .db_m_result_product-card-wrapper {
        width: 50%;
    }
}

@media (min-width: 768px) and (max-width: 992px) {
    .db_m_result_product-card-wrapper {
        width: 30%;
    }
}

@media (max-width: 992px) {
    .db_m_modal {
        display: flex;
        flex-direction: column;
        position: fixed;
        top: 0;
        width: 100%;
        background-color: white;
        z-index: 1000001;
    }

    .db_m_search-container {
        display: flex;
        width: 100%;
    }

    .db_m_search-icon,
    #db_m_search {
        border: 2px solid lightgray;
        border-top: none;
        padding: 8px;
        font-size: 25px;
        /* height: 30px; */
    }

    .db_m_search-icon {
        width: 10%;
    }
    .db_m_search-icon.db_m_clear-icon {
        position: relative;
        border-left: none;
        border-right: none;
    }
    
    .db_m_search-icon.db_m_clear-icon::before,
    .db_m_search-icon.db_m_clear-icon::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 19px; /* Cross size */
        height: 4px; /* Cross thickness */
        background-color: #3B63A6;
    }
    
    .db_m_search-icon.db_m_clear-icon::before {
        transform: translate(-50%, -50%) rotate(45deg);
    }
    
    .db_m_search-icon.db_m_clear-icon::after {
        transform: translate(-50%, -50%) rotate(-45deg);
    }

    .db_m_magnify-icon {
        background-color: black;
        border-right: none;
        color: white;
    }

    #db_m_search {
        /* flex-grow: 1; */
        border-left: none;
        border-right: none;
        outline: none;
        width: 80%;
    }

    .db_m_overlay {
        display: block;
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000000;
    }

    .db_m_toggle-modal {
        display: none;
    }

    .db_m_modal ul {
        list-style-type: none;
        padding: 0;
        /* margin-top: 50px; Adjust based on the height of your search bar */
    }

    .db_m_modal li {
        padding: 8px 16px;
        border-bottom: 1px solid #ddd;
    }

    .db_m_modal li:last-child {
        border-bottom: none;
    }

    .db_m_modal ul li a {
        text-decoration: none;
        color: black;
        font-weight: bold;
    }

    .db_m_arrow {
        float: right;
    }

    /* Pagination container */
    .db_m_result_pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        /* Add other styles as needed */
    }

    /* Generic page button styles */
    .db_m_result_pagination-button {
        display: inline-block;
        width: 30px;
        /* Size of the button */
        height: 30px;
        /* Size of the button */
        line-height: 30px;
        /* Center the text vertically */
        margin: 5px;
        background-color: gray;
        /* Inactive color */
        color: white;
        text-align: center;
        border-radius: 50%;
        /* Makes it a circle */
        cursor: pointer;
        border: none;
        outline: none;
        /* Add other styles such as font size, box-shadow as needed */
    }

    /* Styles for the active page button */
    .db_m_result_pagination-button-active {
        background-color: red;
        /* Active color */
        /* Add other styles to highlight the active button if needed */
    }

    /* product style */
    .db_m_result_product-cards_wrapper {
        display: flex;
        flex-wrap: wrap;
        /* width: 100%; */
    }

    .db_m_result_product-card {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        text-decoration: none;
        width: 100%;
    }

    .db_m_result_product-img-wrapper {
        width: 100%;
        height: 200px;
        overflow: hidden;
        background: white;
    }

    .db_m_result_product-img-wrapper img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .db_m_result_product-card-content {
        padding: 15px;
    }

    .db_m_result_product-title {
        margin: 0;
        color: #3b3b3b;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        font-size: 14px;
    }

    .db_m_result_product-devider {
        margin: 10px 0;
    }


    .db_m_result_product-price-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: auto;
        position: relative;
    }

    .db_m_result_product-price {
        color: #0171e2;
        font-weight: 700;
        font-size: 24px;
    }

    .db_m_result_button-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 10px;
    }

    .db_m_result_button {
        background-color: #0170E0;
        border: none;
        color: white;
        text-align: center;
        text-decoration: none;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 14px;
        margin: 4px 2px;
        cursor: pointer;
        transition-duration: 0.4s;
        width: 100%;
        border-radius: 3px;
        padding: 14px;
    }

    .db_m_result_button:hover {
        background-color: #378AF3;
    }

    .db_m_result_prepend-icon {
        margin-right: 8px;
    }

    .db_m_result_icon-wrapper {
        background-color: red;
        color: white;
        border-top-right-radius: 40%;
        border-bottom-right-radius: 40%;
        padding: 9px;
    }

    .db_m_result_button:hover .db_result_icon-wrapper {
        background-color: #0170E0;
    }

    .db_m_result_button-text {
        flex-grow: 1;
    }

    .db_m_result_tax-info {
        color: #89898b;
        font-size: 11px;
        position: absolute;
        top: 70%;
        left: 0;
    }
}


/**********************mobile filter modal*****************/

.db_m_filter_openModalBtn {
    margin: 20px;
}

.db_m_filter_backdrop {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
}

.db_m_filter_modal {
    display: none;
    position: fixed;
    z-index: 2;
    left: 0;
    bottom: 0;
    width: 100%;
    height: auto;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
    animation: slideIn 0.4s;
}

.db_m_filter_modal.db_m_filter_slide-out {
    animation: slideOut 0.4s forwards;
}

@keyframes slideIn {
    from {
        bottom: -100px;
        opacity: 0
    }

    to {
        bottom: 0;
        opacity: 1
    }
}

@keyframes slideOut {
    from {
        bottom: 0;
        opacity: 1
    }

    to {
        bottom: -100px;
        opacity: 0
    }
}

.db_m_filter_modal-content {
    background-color: #fff;
    margin: 0;
    padding: 0;
    border: 1px solid #888;
    width: 100%;
    max-width: none;
    border-radius: 10px 10px 0 0;
}

.db_m_filter_modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f57c00;
    color: white;
    padding: 10px;
    border-radius: 10px 10px 0 0;
}

.db_m_filter_modal-header h2 {
    margin: 0;
}

.db_m_filter_close-btn {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.db_m_filter_modal-body {
    padding: 10px 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.db_m_filter_modal-body label {
    display: block;
    margin-bottom: 8px;
}

.db_m_filter_modal-body button#db_m_filter_mehrBtn {
    display: block;
    margin: 10px 0;
    background: none;
    border: none;
    color: #f57c00;
    cursor: pointer;
}

.db_m_filter_modal-footer {
    display: flex;
    justify-content: space-between;
    padding: 10px 20px;
    border-top: 1px solid #ddd;
    background-color: #fff;
}

.db_m_filter_modal-footer .db_m_filter_footer-btn {
    flex: 1;
    margin: 5px;
    background-color: #f57c00;
    color: white;
    border: none;
    padding: 10px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    text-align: center;
    transition: background-color 0.3s;
}

.db_m_filter_modal-footer .db_m_filter_footer-btn#db_m_filter_deleteBtn {
    background-color: white;
    color: #f57c00;
    border: 2px solid #f57c00;
}

.db_m_filter_modal-footer .db_m_filter_footer-btn#db_m_filter_deleteBtn:hover {
    background-color: #f57c00;
    color: white;
}

.db_m_filter_modal-footer .db_m_filter_footer-btn#db_m_filter_doneBtn {
    background-color: #f57c00;
    color: white;
    border: 2px solid #f57c00;
}

.db_m_filter_modal-footer .db_m_filter_footer-btn#db_m_filter_doneBtn:hover {
    background-color: white;
    color: #f57c00;
}


.db_m_filter_navbar {
    display: flex;
    align-items: center;
    background-color: #f2f2f2;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    position: relative;
    /* Position context for the pseudo-element */
    overflow: hidden;
    /* Ensures no overflow outside the border-radius */
}

/* Inset border for the entire navbar */
.db_m_filter_navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    pointer-events: none;
    /* Ensures it doesn't interfere with user interaction */
}

.db_m_filter-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e0e0e0;
    /* Slightly darker to stand out */
    padding: 10px 20px;
    font-size: 16px;
    font-weight: bold;
    margin-right: 10px;
    /* Space between the filter button and tabs */
    border-top-right-radius: 15px;
    border-bottom-right-radius: 15px;
    box-shadow: 5px 0 5px rgba(0, 0, 0, 0.5);
    flex-shrink: 0;
    /* Prevents the filter button from shrinking */
}

.db_m_filter-btn .db_m_filter-icon {
    display: block;
    margin-right: 5px;
    width: 16px;
    height: 16px;
    position: relative;
}

.db_m_filter-btn .db_m_filter-icon::before,
.db_m_filter-btn .db_m_filter-icon::after {
    content: '';
    position: absolute;
    background: #333;
    display: block;
}

.db_m_filter-btn .db_m_filter-icon::before {
    top: 0;
    left: 25%;
    width: 50%;
    height: 60%;
    border-radius: 10px 10px 0 0;
}

.db_m_filter-btn .db_m_filter-icon::after {
    top: 60%;
    left: 10%;
    width: 80%;
    height: 40%;
    border-radius: 0 0 10px 10px;
}

.db_m_filter-tabs {
    display: flex;
    align-items: center;
    flex-grow: 1;
    overflow-x: auto;
    /* Allows horizontal scrolling */
    overflow-y: hidden;
    /* Prevents vertical scrolling */
    -ms-overflow-style: none;
    /* Hides scrollbar for IE and Edge */
    scrollbar-width: none;
    /* Hides scrollbar for Firefox */
}

/* Hides scrollbar for Chrome, Safari and Opera */
.db_m_filter-tabs::-webkit-scrollbar {
    display: none;
}

.db_m_filter-tab {
    margin-right: 20px;
    font-size: 16px;
    cursor: pointer;
    padding: 10px 10px;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    border-radius: 5px;
    white-space: nowrap;
    /* Keeps the tab labels on one line */
    flex-shrink: 0;
    /* Prevents tabs from shrinking */
}

.db_m_filter-tab.active {
    background-color: #d0d0d0;
    /* Active state background */
    border-bottom: 3px solid black;
    padding-bottom: 7px;
    /* Adjust padding to maintain consistent height */
}

/**************End of mobile filter modal***************/

/***************filter checkboxes *************/
/* CSS for the checkbox container */
.db_m_result_checkbox-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #f9f9f9;
    transition: background-color 0.3s ease;
}

.db_m_result_checkbox-container:hover {
    background-color: #f1f1f1;
}

/* CSS for indented checkboxes */
.db_m_result_checkbox-container.indent {
    margin-left: 20px; /* Adjust the value as needed for the desired indentation */
}

/* CSS for the checkbox */
.db_m_result_checkbox-container input[type="checkbox"] {
    margin-right: 10px;
    width: 18px;
    height: 18px;
    cursor: pointer;
}

/* CSS for the label */
.db_m_result_checkbox-container label {
    font-size: 16px;
    color: #333;
    cursor: pointer;
}

/* Optional: Custom checkbox design */
.db_m_result_checkbox-container input[type="checkbox"] {
    position: relative;
    appearance: none;
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 18px;
    height: 18px;
    cursor: pointer;
    outline: none;
    transition: background-color 0.3s ease;
}

.db_m_result_checkbox-container input[type="checkbox"]::before {
    content: "";
    position: absolute;
    top: 2px;
    left: 2px;
    width: 14px;
    height: 14px;
    background: no-repeat center/75% url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"%3E%3Cpath fill="none" stroke="%23fff" stroke-width="2" d="M3 12l5 5L21 5"/%3E%3C/svg%3E');
    opacity: 0;
    transition: opacity 0.3s ease;
}

.db_m_result_checkbox-container input[type="checkbox"]:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.db_m_result_checkbox-container input[type="checkbox"]:checked::before {
    opacity: 1;
}

/***************End filter checkboxes**********/


/***************No results found *************/
.db_m_result_no-result-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-top: 20px;
}

.db_m_result_no-result-message {
    font-size: 18px;
    color: #6c757d;
    text-align: center;
    margin: 0;
    line-height: 1.5;
}


/*************************************************/